{"name": "prisma", "productName": "Prisma", "author": "andrea<PERSON>i", "description": "Programma Interno per la Scansione dei Manoscritti", "version": "1.2.14", "private": true, "main": "src/main-process/index.js", "build": {"appId": "local.bav.prisma", "icon": "public/prisma_app_logo.png", "productName": "Prisma", "files": ["public/**/*", "src/**/*"], "publish": [{"provider": "generic", "url": "http://docker-repo:5080/"}], "nsis": {"oneClick": true, "perMachine": false}}, "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "start": "sirv public --no-clear", "electron": "wait-on http://localhost:5000 && electron ./src/main-process", "electron-dev": "concurrently \"yarn run dev\" \"yarn run electron\"", "preelectron-pack": "yarn run build", "electron-pack": "electron-builder"}, "dependencies": {"@ottozz/mousetrap": "^1.6.5", "d3-selection": "^3.0.0", "electron-serve": "^1.1.0", "electron-updater": "^6.1.4", "fs-extra": "^11.1.1", "ip": "^1.1.8", "ldap-authentication": "^2.2.9", "mysql2": "^3.1.2", "node-ssh": "^13.2.0", "sharp": "^0.31.3", "svelte-material-icons": "^2.0.0", "svelte-material-ui": "^6.0.0-beta.15", "svelte-spa-router": "^3.2.0"}, "devDependencies": {"@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-commonjs": "^20.0.0", "@rollup/plugin-node-resolve": "^13.0.4", "@zerodevx/svelte-toast": "^0.9.5", "concurrently": "^6.2.1", "electron": "^30.0.0", "electron-builder": "^24.9.1", "rollup": "^2.56.2", "rollup-plugin-css-only": "^3.1.0", "rollup-plugin-livereload": "^2.0.5", "rollup-plugin-svelte": "^7.1.0", "rollup-plugin-terser": "^7.0.2", "sirv-cli": "^1.0.12", "svelte": "^3.42.1", "wait-on": "^6.0.0"}}