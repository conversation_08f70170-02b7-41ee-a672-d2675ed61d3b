html {
	height: 100%;
	overflow: hidden;
  background-color: var(--mdc-theme-background);
}

body {
  height: calc(100% - 16px);
}

::selection {
  background: var(--mdc-theme-primary);
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  /* -webkit-appearance: none;
  margin: 0;*/
  -webkit-appearance: none;
  margin: 0;
  background: transparent url('input_arrows.png') no-repeat 50% 100%;
  padding: 12px;
  padding-right: 0;
  position: relative;
  cursor: pointer;
} 

/* svelte-material-ui colors */

:root {
  --mdc-theme-primary: #d43804;
  --mdc-theme-secondary: #bcbcbc;
  --mdc-theme-surface: #343846;
  --mdc-theme-background: #2E323E;
  --mdc-theme-error: #b71c1c;
  --mdc-theme-warning: darkorange;
  --mdc-theme-success: #2e9100;
  --mdc-theme-disabled: #c7bfbf21;
  --mdc-theme-primary-hover: #ff40002d;
  --mdc-theme-primary-selected: #ff40009c;
}

/* Titlebar CSS */
.cet-container {
  overflow: hidden !important;
}