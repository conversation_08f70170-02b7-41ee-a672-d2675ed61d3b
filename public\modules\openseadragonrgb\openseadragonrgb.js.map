{"version": 3, "sources": ["src/rgb.js"], "names": ["$", "version", "major", "Error", "Viewer", "prototype", "rgb", "options", "this", "rgbInstance", "viewer", "RGB", "extend", "onCanvasHover", "sampleSize", "tracker", "MouseTracker", "element", "canvas", "<PERSON><PERSON><PERSON><PERSON>", "delegate", "onMouseMove", "getValueAt", "canvasX", "canvasY", "center", "arguments", "length", "Point", "images", "i", "world", "getItemCount", "push", "getItemAt", "image", "drawer", "getImageOfPoint", "sampleData", "getRgbAt", "reds", "greens", "blues", "alphas", "s", "getMedian", "channelValues", "sort", "a", "b", "median", "Math", "floor", "color", "r", "g", "imageCoordinates", "event", "position", "Drawer", "point", "size", "getContentSize", "imagePoint", "TiledImage", "viewerElementToImageCoordinates", "viewport", "pointIsInImage", "x", "y", "useCanvas", "sampleOffset", "ratio", "pixelDensityRatio", "renderingContext", "_getContext", "getImageData", "data", "OpenSeadragon"], "mappings": "CAAA,SAAUA,GACN,aAEA,IAAKA,EAAEC,SAAWD,EAAEC,QAAQC,MAAQ,EAAG,CACnC,MAAM,IAAIC,MAAM,0EAGpBH,EAAEI,OAAOC,UAAUC,IAAM,SAASC,SAC9B,IAAKC,KAAKC,aAAeF,QAAS,CAC9BA,QAAUA,SAAW,GACrBA,QAAQG,OAASF,KACjBA,KAAKC,YAAc,IAAIT,EAAEW,IAAIJ,SAEjC,OAAOC,KAAKC,aAShBT,EAAEW,IAAM,SAAWJ,SAEfP,EAAEY,OAAQ,KAAMJ,KAAM,CAElBE,OAAyB,KAGzBG,cAA2B,KAC3BC,WAAyB,GAC1BP,SAEH,GAAIC,KAAKK,cAAe,CACpBL,KAAKO,QAAU,IAAIf,EAAEgB,aAAa,CAC9BC,QAAoBT,KAAKE,OAAOQ,OAChCC,YAAoBnB,EAAEoB,SAAUZ,KAAMa,iBAKlDrB,EAAEY,OAAQZ,EAAEW,IAAIN,UAAoD,CAShEiB,WAAY,SAAUC,QAASC,SAC3B,IAAIC,OAASC,UAAUC,SAAW,EAAIJ,QAAU,IAAIvB,EAAE4B,MAAOL,QAASC,SACtE,IAAId,OAASF,KAAKE,OAElB,IAAImB,OAAS,GACb,IAAK,IAAIC,EAAI,EAAGA,EAAIpB,OAAOqB,MAAMC,eAAgBF,IAAK,CAClDD,OAAOI,KAAMvB,OAAOqB,MAAMG,UAAUJ,IAGxC,IAAIK,MAAQzB,OAAO0B,OAAOC,gBAAiBZ,OAAQI,QAEnD,IAAIS,WAAa5B,OAAO0B,OAAOG,SAAUd,QACzC,IAAIe,KAAO,GAAIC,OAAS,GAAIC,MAAQ,GAAIC,OAAS,GACjD,IAAK,IAAIC,EAAI,EAAGA,EAAIN,WAAWX,OAAQiB,EAAIA,EAAI,EAAG,CAC9CJ,KAAKP,KAAMK,WAAWM,IACtBH,OAAOR,KAAMK,WAAWM,EAAI,IAC5BF,MAAMT,KAAMK,WAAWM,EAAI,IAC3BD,OAAOV,KAAMK,WAAWM,EAAI,IAEhC,IAAIC,UAAY,SAAWC,eACvBA,cAAcC,MAAM,SAAUC,EAAGC,GAAK,OAAOD,EAAIC,KACjD,IAAIC,OAASJ,cAAcK,KAAKC,MAAON,cAAcnB,OAAS,IAC9D,OAAOuB,QAEX,IAAIG,MAAQ,CACRC,EAAGT,UAAUL,MACbe,EAAGV,UAAUJ,QACbQ,EAAGJ,UAAUH,OACbM,EAAGH,UAAUF,SAEjB,GAAKR,MAAQ,CACTkB,MAAMlB,MAAQA,MACdkB,MAAMG,iBAAmB/B,OAE7B,OAAO4B,SAIf,SAAShC,YAAYoC,OACjBjD,KAAKK,cAAcL,KAAKc,WAAWmC,MAAMC,WAS7C1D,EAAE2D,OAAOtD,UAAUgC,gBAAkB,SAAUuB,MAAO/B,QAClD,IAAIM,MACJ,IAAK,IAAIL,EAAI,EAAGA,EAAID,OAAOF,OAAQG,IAAK,CACpC,IAAI+B,KAAOhC,OAAOC,GAAGgC,iBACrB,IAAIC,WACJ,GAAI/D,EAAEgE,WAAW3D,UAAU4D,gCAAiC,CACxDF,WAAalC,OAAOC,GAAGmC,gCAAgCL,WACpD,CAEHG,WAAavD,KAAKE,OAAOwD,SAASD,gCAAgCL,OAEtE,IAAIO,eAAiBJ,WAAWK,GAAK,GAAKL,WAAWM,GAAK,EAC1DF,eAAiBA,gBAAkBJ,WAAWK,GAAKP,KAAKO,GAAKL,WAAWM,GAAKR,KAAKQ,EAClF,GAAKF,eAAiB,CAClBhC,MAAQN,OAAOC,GACf,OAGR,OAAOK,OASXnC,EAAE2D,OAAOtD,UAAUkC,SAAW,SAASqB,OACnC,IAAKpD,KAAK8D,UAAW,CACjB,OAAO,MAEX,IAAIxD,WAAaN,KAAKE,OAAOD,YAAYK,WACzC,IAAIyD,cAAiBzD,WAAa,GAAM,EACxC,IAAI0D,MAAQxE,EAAEyE,kBACd,IAAIL,EAAMR,MAAMQ,EAAII,MAAUD,aAC9B,IAAIF,EAAMT,MAAMS,EAAIG,MAAUD,aAC9B,IAAIG,iBAAmBlE,KAAKmE,cAC5B,IAAIrC,WAAaoC,iBAAiBE,aAAaR,EAAGC,EAAGvD,WAAYA,YAAY+D,KAC7E,OAAOvC,aAxIf,CA2IGwC"}