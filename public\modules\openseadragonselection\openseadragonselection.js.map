{"version": 3, "sources": ["selection.js", "selectionoverlay.js", "selectionrect.js"], "names": ["$", "onOutsideDrag", "e", "this", "viewer", "setMouseNavEnabled", "delta", "viewport", "deltaPointsFromPixels", "end", "pointFromPixel", "position", "start", "Point", "x", "y", "rect", "oldRect", "restrictToImage", "clone", "rectDone", "angle1", "getAngleFromCenter", "angle2", "rotation", "Math", "PI", "startRotated", "getPrerotatedRect", "rotatedStartPoint", "startRotatedHeight", "width", "height", "bounds", "world", "getHomeBounds", "fitsIn", "Rect", "pointIsInImage", "restrictVector", "SelectionRect", "draw", "onOutsideDragEnd", "onClick", "canvas", "focus", "onInsideDrag", "addClass", "element", "onInsideDragEnd", "removeClass", "onBorderDrag", "border", "center", "getDegreeRotation", "rotate", "getCenter", "newCenter", "target", "minus", "onKeyPress", "key", "keyCode", "charCode", "confirm", "String", "fromCharCode", "keyboardShortcut", "toggleState", "dist", "distanceTo", "angle", "atan2", "heightMod<PERSON><PERSON><PERSON>", "self", "point", "prop", "version", "major", "Error", "Viewer", "prototype", "selection", "options", "selectionInstance", "Selection", "extend", "isSelecting", "buttonActiveImg", "to<PERSON><PERSON><PERSON><PERSON>", "showSelectionControl", "showConfirmDenyButtons", "styleConfirmDenyButtons", "returnPixelCoordinates", "onSelection", "prefixUrl", "navImages", "REST", "GROUP", "HOVER", "DOWN", "selectionConfirm", "selectionCancel", "handleStyle", "top", "left", "margin", "background", "cornersStyle", "makeNeutralElement", "style", "className", "borders", "handle", "corners", "i", "MouseTracker", "<PERSON><PERSON><PERSON><PERSON>", "bind", "append<PERSON><PERSON><PERSON>", "setTimeout", "right", "bottom", "overlay", "SelectionOverlay", "innerTracker", "clickTimeThreshold", "clickDistThreshold", "delegate", "dragEndHandler", "clickHandler", "outerTracker", "startDisabled", "addEvent", "container", "prefix", "useGroup", "buttons", "anyButton", "onFocusHandler", "onFocus", "onBlurHandler", "onBlur", "<PERSON><PERSON>", "getElement", "tooltip", "getString", "srcRest", "srcGroup", "srcHover", "srcDown", "onRelease", "push", "imgDown", "cloneNode", "confirmButton", "classList", "add", "cancelButton", "cancel", "transform", "add<PERSON><PERSON><PERSON>", "ControlDock", "setState", "enabled", "setTracking", "undraw", "visibility", "raiseEvent", "enable", "disable", "update", "normalize", "drawHTML", "drawer", "destroy", "result", "real", "viewportToImageRectangle", "fromRect", "round", "OpenSeadragon", "location", "Overlay", "apply", "arguments", "isPlainObject", "Object", "create", "replace", "equals", "other", "toString", "swapWidthHeight", "swapped", "diff", "fixed", "area", "getTopLeft", "getTopRight", "getBottomRight", "getBottomLeft", "areaEnd", "reduceRotation", "reduced"], "mappings": "CAAA,SAAAA,GACA,YA4UA,SAAAC,GAAAC,GAEAC,KAAAC,OAAAC,oBAAA,EACA,IAAAC,GAAAH,KAAAC,OAAAG,SAAAC,sBAAAN,EAAAI,OAAA,GACAG,EAAAN,KAAAC,OAAAG,SAAAG,eAAAR,EAAAS,UAAA,GACAC,EAAA,GAAAZ,GAAAa,MAAAJ,EAAAK,EAAAR,EAAAQ,EAAAL,EAAAM,EAAAT,EAAAS,EACA,IAAAZ,KAAAa,KAcA,CACA,GAAAC,EAIA,IAHAd,KAAAe,kBACAD,EAAAd,KAAAa,KAAAG,SAEAhB,KAAAiB,SAAA,CAEA,GAAAC,GAAAlB,KAAAa,KAAAM,mBAAAV,GACAW,EAAApB,KAAAa,KAAAM,mBAAAb,EACAN,MAAAa,KAAAQ,UAAArB,KAAAa,KAAAQ,SAAAH,EAAAE,GAAAE,KAAAC,OAEAvB,MAAAwB,aACAxB,KAAAa,KAAAY,EAAAzB,KAAA0B,kBAAApB,EAAAN,KAAA2B,qBAEA3B,KAAAa,KAAAe,OAAAzB,EAAAQ,EACAX,KAAAa,KAAAgB,QAAA1B,EAAAS,EAGA,IAAAkB,GAAA9B,KAAAC,OAAA8B,MAAAC,eACAhC,MAAAe,kBAAAf,KAAAa,KAAAoB,OAAA,GAAApC,GAAAqC,KAAA,EAAA,EAAAJ,EAAAF,MAAAE,EAAAD,WACA7B,KAAAa,KAAAC,OAlCA,CACA,GAAAd,KAAAe,gBAAA,CACA,IAAAoB,EAAAnC,KAAAS,GACA,MAEA2B,GAAAjC,EAAAG,GAEAN,KAAAwB,cACAxB,KAAA0B,kBAAAjB,EACAT,KAAAa,KAAAY,EAAAhB,EAAAH,EAAAN,KAAA2B,qBAEA3B,KAAAa,KAAA,GAAAhB,GAAAwC,cAAA5B,EAAAE,EAAAF,EAAAG,EAAAT,EAAAQ,EAAAR,EAAAS,GAEAZ,KAAAiB,UAAA,EAwBAjB,KAAAsC,OAGA,QAAAC,KAEAvC,KAAAC,OAAAC,oBAAA,GACAF,KAAAiB,UAAA,EAGA,QAAAuB,KACAxC,KAAAC,OAAAwC,OAAAC,QAGA,QAAAC,GAAA5C,GACAF,EAAA+C,SAAA5C,KAAA6C,QAAA,WACA,IAAA1C,GAAAH,KAAAC,OAAAG,SAAAC,sBAAAN,EAAAI,OAAA,EACAH,MAAAa,KAAAF,GAAAR,EAAAQ,EACAX,KAAAa,KAAAD,GAAAT,EAAAS,CACA,IAAAkB,GAAA9B,KAAAC,OAAA8B,MAAAC,eACAhC,MAAAe,kBAAAf,KAAAa,KAAAoB,OAAA,GAAApC,GAAAqC,KAAA,EAAA,EAAAJ,EAAAF,MAAAE,EAAAD,WACA7B,KAAAa,KAAAF,GAAAR,EAAAQ,EACAX,KAAAa,KAAAD,GAAAT,EAAAS,GAEAZ,KAAAsC,OAGA,QAAAQ,KACAjD,EAAAkD,YAAA/C,KAAA6C,QAAA,YAGA,QAAAG,GAAAC,EAAAlD,GACA,GAEAmD,GAFA/C,EAAAJ,EAAAI,MACAkB,EAAArB,KAAAa,KAAAsC,oBAEArC,EAAAd,KAAAe,gBAAAf,KAAAa,KAAAG,QAAA,IAOA,QANA,IAAAK,IAEAlB,EAAAA,EAAAiD,UAAA/B,EAAA,GAAAxB,GAAAa,MAAA,EAAA,IACAwC,EAAAlD,KAAAa,KAAAwC,aAEAlD,EAAAH,KAAAC,OAAAG,SAAAC,sBAAAF,GAAA,GACA8C,GACA,IAAA,GACAjD,KAAAa,KAAAD,GAAAT,EAAAS,EACAZ,KAAAa,KAAAgB,QAAA1B,EAAAS,CACA,MACA,KAAA,GACAZ,KAAAa,KAAAe,OAAAzB,EAAAQ,CACA,MACA,KAAA,GACAX,KAAAa,KAAAgB,QAAA1B,EAAAS,CACA,MACA,KAAA,GACAZ,KAAAa,KAAAF,GAAAR,EAAAQ,EACAX,KAAAa,KAAAe,OAAAzB,EAAAQ,CACA,MACA,KAAA,GACAX,KAAAa,KAAAD,GAAAT,EAAAS,EACAZ,KAAAa,KAAAgB,QAAA1B,EAAAS,EACAZ,KAAAa,KAAAF,GAAAR,EAAAQ,EACAX,KAAAa,KAAAe,OAAAzB,EAAAQ,CACA,MACA,KAAA,KACAX,KAAAa,KAAAD,GAAAT,EAAAS,EACAZ,KAAAa,KAAAgB,QAAA1B,EAAAS,EACAZ,KAAAa,KAAAe,OAAAzB,EAAAQ,CACA,MACA,KAAA,KACAX,KAAAa,KAAAe,OAAAzB,EAAAQ,EACAX,KAAAa,KAAAgB,QAAA1B,EAAAS,CACA,MACA,KAAA,KACAZ,KAAAa,KAAAgB,QAAA1B,EAAAS,EACAZ,KAAAa,KAAAF,GAAAR,EAAAQ,EACAX,KAAAa,KAAAe,OAAAzB,EAAAQ,EAGA,GAAA,IAAAU,EAAA,CAEA,GAAAiC,GAAAtD,KAAAa,KAAAwC,YAEAE,EAAAD,EAAAF,OAAA/B,EAAA6B,EAEA/C,GAAAoD,EAAAC,MAAAF,GACAtD,KAAAa,KAAAF,GAAAR,EAAAQ,EACAX,KAAAa,KAAAD,GAAAT,EAAAS,EAEA,GAAAkB,GAAA9B,KAAAC,OAAA8B,MAAAC,eACAhC,MAAAe,kBAAAf,KAAAa,KAAAoB,OAAA,GAAApC,GAAAqC,KAAA,EAAA,EAAAJ,EAAAF,MAAAE,EAAAD,WACA7B,KAAAa,KAAAC,GAEAd,KAAAsC,OAGA,QAAAmB,GAAA1D,GACA,GAAA2D,GAAA3D,EAAA4D,QAAA5D,EAAA4D,QAAA5D,EAAA6D,QACA,MAAAF,EACA1D,KAAA6D,UACAC,OAAAC,aAAAL,KAAA1D,KAAAgE,kBACAhE,KAAAiE,cAIA,QAAAxC,GAAAhB,EAAAH,EAAAuB,GACA,GAAApB,EAAAE,EAAAL,EAAAK,EAAA,CAEA,GAAAA,GAAAF,CACAA,GAAAH,EACAA,EAAAK,EAEA,GAAAR,GAAAG,EAAAkD,MAAA/C,GACAyD,EAAAzD,EAAA0D,WAAA7D,GACA8D,KAAA9C,KAAA+C,MAAAlE,EAAAQ,EAAAR,EAAAS,GAAAU,KAAAC,GAAA,EACA2B,EAAA,GAAArD,GAAAa,MACAP,EAAAQ,EAAA,EAAAF,EAAAE,EACAR,EAAAS,EAAA,EAAAH,EAAAG,GAEAC,EAAA,GAAAhB,GAAAwC,cACAa,EAAAvC,EAAAuD,EAAA,EACAhB,EAAAtC,EAAAiB,EAAA,EACAqC,EACArC,EACAuC,GAEAE,EAAA,GAAAzE,GAAAa,MAAA,EAAAmB,EAIA,OAHAyC,GAAAA,EAAAlB,OAAAvC,EAAAsC,oBAAA,GAAAtD,GAAAa,MAAA,EAAA,IACAG,EAAAF,GAAA2D,EAAA3D,EAAA,EACAE,EAAAD,GAAA0D,EAAA1D,EAAA,EACAC,EAGA,QAAAsB,GAAAoC,EAAAC,GACA,GAAA1C,GAAAyC,EAAAtE,OAAA8B,MAAAC,eACA,OAAAwC,GAAA7D,GAAA,GAAA6D,EAAA7D,GAAAmB,EAAAF,OAAA4C,EAAA5D,GAAA,GAAA4D,EAAA5D,GAAAkB,EAAAD,OAGA,QAAAO,GAAAjC,EAAAG,GACA,GAAAG,EACA,KAAA,GAAAgE,MAAA9D,EAAA,EAAAC,EAAA,GACAH,EAAAH,EAAAmE,GAAAtE,EAAAsE,GACAhE,EAAA,GAAAA,EAAA,IACAH,EAAAmE,GAAA,GACAtE,EAAAsE,IAAAnE,EAAAmE,GAAA,EACAnE,EAAAmE,GAAA,GACAnE,EAAAmE,GAAA,IACAtE,EAAAsE,IAAAnE,EAAAmE,GACAnE,EAAAmE,GAAA,IAvgBA,IAAA5E,EAAA6E,SAAA7E,EAAA6E,QAAAC,MAAA,EACA,KAAA,IAAAC,OAAA,+EAGA/E,GAAAgF,OAAAC,UAAAC,UAAA,SAAAC,GAMA,MALAhF,MAAAiF,oBAAAD,IACAA,EAAAA,MACAA,EAAA/E,OAAAD,KACAA,KAAAiF,kBAAA,GAAApF,GAAAqF,UAAAF,IAEAhF,KAAAiF,mBAUApF,EAAAqF,UAAA,SAAAF,GAEAnF,EAAAsF,QAAA,EAAAnF,MAEAC,OAAA,KACAmF,aAAA,EACAC,iBAAA,EACApE,UAAA,EAGA4B,QAAA,KACAyC,aAAA,KACAC,sBAAA,EACAC,wBAAA,EACAC,yBAAA,EACAC,wBAAA,EACA1B,iBAAA,IACAnD,KAAA,KACAW,cAAA,EACAG,mBAAA,GACAZ,iBAAA,EACA4E,YAAA,KACAC,UAAA,KACAC,WACAd,WACAe,KAAA,qBACAC,MAAA,2BACAC,MAAA,sBACAC,KAAA,yBAEAC,kBACAJ,KAAA,6BACAC,MAAA,mCACAC,MAAA,8BACAC,KAAA,iCAEAE,iBACAL,KAAA,4BACAC,MAAA,kCACAC,MAAA,6BACAC,KAAA,iCAGAG,aACAC,IAAA,MACAC,KAAA,MACA1E,MAAA,MACAC,OAAA,MACA0E,OAAA,gBACAC,WAAA,OACAvD,OAAA,kBAEAwD,cACA7E,MAAA,MACAC,OAAA,MACA2E,WAAA,OACAvD,OAAA,mBAGA+B,GAEAnF,EAAAsF,QAAA,EAAAnF,KAAA6F,UAAA7F,KAAAC,OAAA4F,WAEA7F,KAAA6C,UACA7C,KAAA6C,QAAAhD,EAAA6G,mBAAA,OACA1G,KAAA6C,QAAA8D,MAAAH,WAAA,qBACAxG,KAAA6C,QAAA+D,UAAA,iBAEA5G,KAAA6G,QAAA7G,KAAA6G,WAGA,KAAA,GAFAC,GACAC,KACAC,EAAA,EAAAA,EAAA,EAAAA,IACAhH,KAAA6G,QAAAG,KACAhH,KAAA6G,QAAAG,GAAAnH,EAAA6G,mBAAA,OACA1G,KAAA6G,QAAAG,GAAAJ,UAAA,UAAAI,EACAhH,KAAA6G,QAAAG,GAAAL,MAAAnG,SAAA,WACAR,KAAA6G,QAAAG,GAAAL,MAAA/E,MAAA,MACA5B,KAAA6G,QAAAG,GAAAL,MAAA9E,OAAA,MACA7B,KAAA6G,QAAAG,GAAAL,MAAAH,WAAA,QAGAM,EAAAjH,EAAA6G,mBAAA,OACAI,EAAAF,UAAA,UAAAI,EAAA,UACAF,EAAAH,MAAAnG,SAAA,WACAsG,EAAAH,MAAAN,IAAArG,KAAAoG,YAAAC,IACAS,EAAAH,MAAAL,KAAAtG,KAAAoG,YAAAE,KACAQ,EAAAH,MAAA/E,MAAA5B,KAAAoG,YAAAxE,MACAkF,EAAAH,MAAA9E,OAAA7B,KAAAoG,YAAAvE,OACAiF,EAAAH,MAAAJ,OAAAvG,KAAAoG,YAAAG,OACAO,EAAAH,MAAAH,WAAAxG,KAAAoG,YAAAI,WACAM,EAAAH,MAAA1D,OAAAjD,KAAAoG,YAAAnD,OACA,GAAApD,GAAAoH,cACApE,QAAA7C,KAAA6G,QAAAG,GACAE,YAAAlE,EAAAmE,KAAAnH,KAAAgH,KAGAD,EAAAC,GAAAnH,EAAA6G,mBAAA,OACAK,EAAAC,GAAAJ,UAAA,UAAAI,EAAA,UACAD,EAAAC,GAAAL,MAAAnG,SAAA,WACAuG,EAAAC,GAAAL,MAAA/E,MAAA5B,KAAAyG,aAAA7E,MACAmF,EAAAC,GAAAL,MAAA9E,OAAA7B,KAAAyG,aAAA5E,OACAkF,EAAAC,GAAAL,MAAAH,WAAAxG,KAAAyG,aAAAD,WACAO,EAAAC,GAAAL,MAAA1D,OAAAjD,KAAAyG,aAAAxD,OACA,GAAApD,GAAAoH,cACApE,QAAAkE,EAAAC,GACAE,YAAAlE,EAAAmE,KAAAnH,KAAAgH,EAAA,MAGAhH,KAAA6G,QAAAG,GAAAI,YAAAN,GACA9G,KAAA6C,QAAAuE,YAAApH,KAAA6G,QAAAG,IAEAK,WAAArH,KAAA6C,QAAAuE,YAAAD,KAAAnH,KAAA6C,QAAAkE,EAAAC,IAAA,EAEAhH,MAAA6G,QAAA,GAAAF,MAAAN,IAAA,EACArG,KAAA6G,QAAA,GAAAF,MAAA/E,MAAA,OACA5B,KAAA6G,QAAA,GAAAF,MAAAW,MAAA,EACAtH,KAAA6G,QAAA,GAAAF,MAAA9E,OAAA,OACA7B,KAAA6G,QAAA,GAAAF,MAAAY,OAAA,EACAvH,KAAA6G,QAAA,GAAAF,MAAA/E,MAAA,OACA5B,KAAA6G,QAAA,GAAAF,MAAAL,KAAA,EACAtG,KAAA6G,QAAA,GAAAF,MAAA9E,OAAA,OACAkF,EAAA,GAAAJ,MAAAN,IAAA,OACAU,EAAA,GAAAJ,MAAAL,KAAA,OACAS,EAAA,GAAAJ,MAAAN,IAAA,OACAU,EAAA,GAAAJ,MAAAW,MAAA,OACAP,EAAA,GAAAJ,MAAAY,OAAA,OACAR,EAAA,GAAAJ,MAAAW,MAAA,OACAP,EAAA,GAAAJ,MAAAY,OAAA,OACAR,EAAA,GAAAJ,MAAAL,KAAA,OAEAtG,KAAAwH,UACAxH,KAAAwH,QAAA,GAAA3H,GAAA4H,iBAAAzH,KAAA6C,QAAA7C,KAAAa,MAAA,GAAAhB,GAAAwC,gBAGArC,KAAA0H,aAAA,GAAA7H,GAAAoH,cACApE,QAAA7C,KAAA6C,QACA8E,mBAAA3H,KAAAC,OAAA0H,mBACAC,mBAAA5H,KAAAC,OAAA2H,mBACAV,YAAArH,EAAAgI,SAAA7H,KAAA2C,GACAmF,eAAAjI,EAAAgI,SAAA7H,KAAA8C,GAEAiF,aAAAlI,EAAAgI,SAAA7H,KAAAwC,KAKAxC,KAAAgI,aAAA,GAAAnI,GAAAoH,cACApE,QAAA7C,KAAAC,OAAAwC,OACAkF,mBAAA3H,KAAAC,OAAA0H,mBACAC,mBAAA5H,KAAAC,OAAA2H,mBACAV,YAAArH,EAAAgI,SAAA7H,KAAAF,GACAgI,eAAAjI,EAAAgI,SAAA7H,KAAAuC,GACAwF,aAAAlI,EAAAgI,SAAA7H,KAAAwC,GACAyF,eAAAjI,KAAAoF,cAGApF,KAAAgE,kBACAnE,EAAAqI,SACAlI,KAAAC,OAAAkI,UACA,WACAtI,EAAAgI,SAAA7H,KAAAyD,IACA,EAIA,IAAA2E,GAAApI,KAAA4F,WAAA5F,KAAAC,OAAA2F,WAAA,GACAyC,EAAArI,KAAAC,OAAAqI,SAAAtI,KAAAC,OAAAqI,QAAAA,QACAC,EAAAF,EAAArI,KAAAC,OAAAqI,QAAAA,QAAA,GAAA,KACAE,EAAAD,EAAAA,EAAAE,QAAA,KACAC,EAAAH,EAAAA,EAAAI,OAAA,IAwBA,IAvBA3I,KAAAuF,uBACAvF,KAAAsF,aAAA,GAAAzF,GAAA+I,QACA/F,QAAA7C,KAAAsF,aAAAzF,EAAAgJ,WAAA7I,KAAAsF,cAAA,KACAqC,mBAAA3H,KAAAC,OAAA0H,mBACAC,mBAAA5H,KAAAC,OAAA2H,mBACAkB,QAAAjJ,EAAAkJ,UAAA,6BAAA,mBACAC,QAAAZ,EAAApI,KAAA6F,UAAAd,UAAAe,KACAmD,SAAAb,EAAApI,KAAA6F,UAAAd,UAAAgB,MACAmD,SAAAd,EAAApI,KAAA6F,UAAAd,UAAAiB,MACAmD,QAAAf,EAAApI,KAAA6F,UAAAd,UAAAkB,KACAmD,UAAApJ,KAAAiE,YAAAkD,KAAAnH,MACAyI,QAAAD,EACAG,OAAAD,IAEAL,IACArI,KAAAC,OAAAqI,QAAAA,QAAAe,KAAArJ,KAAAsF,cACAtF,KAAAC,OAAAqI,QAAAzF,QAAAuE,YAAApH,KAAAsF,aAAAzC,UAEA7C,KAAAsF,aAAAgE,UACAtJ,KAAAqF,gBAAArF,KAAAsF,aAAAgE,QAAAC,WAAA,GACAvJ,KAAAsF,aAAAzC,QAAAuE,YAAApH,KAAAqF,mBAGArF,KAAAwF,uBAAA,CACAxF,KAAAwJ,cAAA,GAAA3J,GAAA+I,QACA/F,QAAA7C,KAAAwJ,cAAA3J,EAAAgJ,WAAA7I,KAAAwJ,eAAA,KACA7B,mBAAA3H,KAAAC,OAAA0H,mBACAC,mBAAA5H,KAAAC,OAAA2H,mBACAkB,QAAAjJ,EAAAkJ,UAAA,8BAAA,oBACAC,QAAAZ,EAAApI,KAAA6F,UAAAK,iBAAAJ,KACAmD,SAAAb,EAAApI,KAAA6F,UAAAK,iBAAAH,MACAmD,SAAAd,EAAApI,KAAA6F,UAAAK,iBAAAF,MACAmD,QAAAf,EAAApI,KAAA6F,UAAAK,iBAAAD,KACAmD,UAAApJ,KAAA6D,QAAAsD,KAAAnH,MACAyI,QAAAD,EACAG,OAAAD,GAEA,IAAA7E,GAAA7D,KAAAwJ,cAAA3G,OACAgB,GAAA4F,UAAAC,IAAA,kBACA1J,KAAA6C,QAAAuE,YAAAvD,GAEA7D,KAAA2J,aAAA,GAAA9J,GAAA+I,QACA/F,QAAA7C,KAAA2J,aAAA9J,EAAAgJ,WAAA7I,KAAA2J,cAAA,KACAhC,mBAAA3H,KAAAC,OAAA0H,mBACAC,mBAAA5H,KAAAC,OAAA2H,mBACAkB,QAAAjJ,EAAAkJ,UAAA,8BAAA,mBACAC,QAAAZ,EAAApI,KAAA6F,UAAAM,gBAAAL,KACAmD,SAAAb,EAAApI,KAAA6F,UAAAM,gBAAAJ,MACAmD,SAAAd,EAAApI,KAAA6F,UAAAM,gBAAAH,MACAmD,QAAAf,EAAApI,KAAA6F,UAAAM,gBAAAF,KACAmD,UAAApJ,KAAA4J,OAAAzC,KAAAnH,MACAyI,QAAAD,EACAG,OAAAD,GAEA,IAAAkB,GAAA5J,KAAA2J,aAAA9G,OACA+G,GAAAH,UAAAC,IAAA,iBACA1J,KAAA6C,QAAAuE,YAAAwC,GAEA5J,KAAAyF,0BACA5B,EAAA8C,MAAAnG,SAAA,WACAqD,EAAA8C,MAAAN,IAAA,MACAxC,EAAA8C,MAAAL,KAAA,MACAzC,EAAA8C,MAAAkD,UAAA,yBAEAD,EAAAjD,MAAAnG,SAAA,WACAoJ,EAAAjD,MAAAN,IAAA,MACAuD,EAAAjD,MAAAL,KAAA,MACAsD,EAAAjD,MAAAkD,UAAA,sBAIA7J,KAAAC,OAAA6J,WAAA,YAAA9J,KAAA2F,aAEA3F,KAAAC,OAAA6J,WAAA,OAAA9J,KAAAsC,KAAA6E,KAAAnH,OACAA,KAAAC,OAAA6J,WAAA,YAAA9J,KAAAsC,KAAA6E,KAAAnH,OACAA,KAAAC,OAAA6J,WAAA,SAAA9J,KAAAsC,KAAA6E,KAAAnH,OACAA,KAAAC,OAAA6J,WAAA,SAAA9J,KAAAsC,KAAA6E,KAAAnH,QAGAH,EAAAsF,OAAAtF,EAAAqF,UAAAJ,UAAAjF,EAAAkK,YAAAjF,WAEAb,YAAA,WACA,MAAAjE,MAAAgK,UAAAhK,KAAAoF,cAGA4E,SAAA,SAAAC,GASA,MARAjK,MAAAoF,YAAA6E,EAEAjK,KAAAgI,aAAAkC,YAAAD,GACAA,EAAAjK,KAAAsC,OAAAtC,KAAAmK,SACAnK,KAAAqF,kBACArF,KAAAqF,gBAAAsB,MAAAyD,WAAAH,EAAA,UAAA,UAEAjK,KAAAC,OAAAoK,WAAA,oBAAAJ,QAAAA,IACAjK,MAGAsK,OAAA,WACA,MAAAtK,MAAAgK,UAAA,IAGAO,QAAA,WACA,MAAAvK,MAAAgK,UAAA,IAGA1H,KAAA,WAKA,MAJAtC,MAAAa,OACAb,KAAAwH,QAAAgD,OAAAxK,KAAAa,KAAA4J,aACAzK,KAAAwH,QAAAkD,SAAA1K,KAAAC,OAAA0K,OAAAxC,UAAAnI,KAAAC,OAAAG,WAEAJ,MAGAmK,OAAA,WAGA,MAFAnK,MAAAwH,QAAAoD,UACA5K,KAAAa,KAAA,KACAb,MAGA6D,QAAA,WACA,GAAA7D,KAAAa,KAAA,CACA,GAAAgK,GAAA7K,KAAAa,KAAA4J,WACA,IAAAzK,KAAA0F,uBAAA,CACA,GAAAoF,GAAA9K,KAAAC,OAAAG,SAAA2K,yBAAAF,EACAC,GAAAjL,EAAAwC,cAAA2I,SAAAF,GAAAG,QACAH,EAAAzJ,SAAAwJ,EAAAxJ,SACAwJ,EAAAC,EAEA9K,KAAAC,OAAAoK,WAAA,YAAAQ,GACA7K,KAAAmK,SAEA,MAAAnK,OAGA4J,OAAA,WAEA,MADA5J,MAAAC,OAAAoK,WAAA,oBAAA,GACArK,KAAAmK,aAuMAe,eChhBA,SAAArL,GACA,YAuBAA,GAAA4H,iBAAA,SAAA5E,EAAAsI,GACAtL,EAAAuL,QAAAC,MAAArL,KAAAsL,WAGAzL,EAAA0L,cAAA1I,GACA7C,KAAAqB,SAAAwB,EAAAsI,SAAA9J,UAAA,EAEArB,KAAAqB,SAAA8J,EAAA9J,UAAA,GAIAxB,EAAA4H,iBAAA3C,UAAAjF,EAAAsF,OAAAqG,OAAAC,OAAA5L,EAAAuL,QAAAtG,YAMA4F,SAAA,WACA7K,EAAAuL,QAAAtG,UAAA4F,SAAAW,MAAArL,KAAAsL,WACAtL,KAAA2G,MAAAkD,UAAA7J,KAAA2G,MAAAkD,UAAA6B,QAAA,oBAAA,IACA,WAAA1L,KAAAqB,SAAA,QAQAmJ,OAAA,SAAAW,GACAtL,EAAAuL,QAAAtG,UAAA0F,OAAAa,MAAArL,KAAAsL,WACAtL,KAAAqB,SAAA8J,EAAA9J,UAAA,MAIA6J,eC1DA,SAAArL,GACA,YAeAA,GAAAwC,cAAA,SAAA1B,EAAAC,EAAAgB,EAAAC,EAAAR,GACAxB,EAAAqC,KAAAmJ,MAAArL,MAAAW,EAAAC,EAAAgB,EAAAC,IAOA7B,KAAAqB,SAAAA,GAAA,GAGAxB,EAAAwC,cAAA2I,SAAA,SAAAnK,GACA,MAAA,IAAAhB,GAAAwC,cACAxB,EAAAF,EACAE,EAAAD,EACAC,EAAAe,MACAf,EAAAgB,SAIAhC,EAAAwC,cAAAyC,UAAAjF,EAAAsF,OAAAqG,OAAAC,OAAA5L,EAAAqC,KAAA4C,YAMA9D,MAAA,WACA,MAAA,IAAAnB,GAAAwC,cAAArC,KAAAW,EAAAX,KAAAY,EAAAZ,KAAA4B,MAAA5B,KAAA6B,OAAA7B,KAAAqB,WASAsK,OAAA,SAAAC,GACA,MAAA/L,GAAAqC,KAAA4C,UAAA6G,OAAAN,MAAArL,MAAA4L,KACA5L,KAAAqB,WAAAuK,EAAAvK,UASAwK,SAAA,WACA,MAAA,IACAvK,KAAA2J,MAAA,IAAAjL,KAAAW,GAAA,IAAA,IACAW,KAAA2J,MAAA,IAAAjL,KAAAY,GAAA,IAAA,IACAU,KAAA2J,MAAA,IAAAjL,KAAA4B,OAAA,IAAA,IACAN,KAAA2J,MAAA,IAAAjL,KAAA6B,QAAA,IAAA,IACAP,KAAA2J,MAAA,IAAAjL,KAAAqB,UAAA,IACA,KAGAyK,gBAAA,WACA,GAAAC,GAAA/L,KAAAgB,OAKA,OAJA+K,GAAAnK,MAAA5B,KAAA6B,OACAkK,EAAAlK,OAAA7B,KAAA4B,MACAmK,EAAApL,IAAAX,KAAA4B,MAAA5B,KAAA6B,QAAA,EACAkK,EAAAnL,IAAAZ,KAAA6B,OAAA7B,KAAA4B,OAAA,EACAmK,GAOA5I,kBAAA,WACA,MAAAnD,MAAAqB,UAAA,IAAAC,KAAAC,KAQAJ,mBAAA,SAAAqD,GACA,GAAAwH,GAAAxH,EAAAhB,MAAAxD,KAAAqD,YACA,OAAA/B,MAAA+C,MAAA2H,EAAArL,EAAAqL,EAAApL,IAQAqK,MAAA,WACA,MAAA,IAAApL,GAAAwC,cACAf,KAAA2J,MAAAjL,KAAAW,GACAW,KAAA2J,MAAAjL,KAAAY,GACAU,KAAA2J,MAAAjL,KAAA4B,OACAN,KAAA2J,MAAAjL,KAAA6B,QACA7B,KAAAqB,WASAoJ,UAAA,WACA,GAAAwB,GAAAjM,KAAAgB,OAUA,OATAiL,GAAArK,MAAA,IACAqK,EAAAtL,GAAAsL,EAAArK,MACAqK,EAAArK,WAEAqK,EAAApK,OAAA,IACAoK,EAAArL,GAAAqL,EAAApK,OACAoK,EAAApK,YAEAoK,EAAA5K,UAAAC,KAAAC,GACA0K,GAQAhK,OAAA,SAAAiK,GAWA,IAAA,GAVArL,GAAAb,KAAAyK,YACA1D,GACAlG,EAAAsL,aACAtL,EAAAuL,cACAvL,EAAAwL,iBACAxL,EAAAyL,iBAEApJ,EAAArC,EAAAwC,YACAhC,EAAAR,EAAAsC,oBACAoJ,EAAAL,EAAAG,iBACArF,EAAA,EAAAA,EAAA,EAAAA,IAEA,GADAD,EAAAC,GAAAD,EAAAC,GAAA5D,OAAA/B,EAAA6B,GACA6D,EAAAC,GAAArG,EAAAuL,EAAAvL,GAAAoG,EAAAC,GAAArG,EAAA4L,EAAA5L,GACAoG,EAAAC,GAAApG,EAAAsL,EAAAtL,GAAAmG,EAAAC,GAAApG,EAAA2L,EAAA3L,EACA,OAAA,CAGA,QAAA,GAQA4L,eAAA,WACA,GAAAC,EAUA,OATAzM,MAAAqB,SAAAC,KAAAC,OACAkL,EAAAzM,KAAA8L,kBACAW,EAAApL,UAAAC,KAAAC,GAAA,GACAvB,KAAAqB,SAAAC,KAAAC,GAAA,GACAkL,EAAAzM,KAAA8L,kBACAW,EAAApL,UAAAC,KAAAC,GAAA,GAEAkL,EAAAzM,KAAAgB,QAEAyL,MAIAvB", "file": "openseadragonselection.js", "sourcesContent": ["(function($) {\n    'use strict';\n\n    if (!$.version || $.version.major < 2) {\n        throw new Error('This version of OpenSeadragonSelection requires OpenSeadragon version 2.0.0+');\n    }\n\n    $.Viewer.prototype.selection = function(options) {\n        if (!this.selectionInstance || options) {\n            options = options || {};\n            options.viewer = this;\n            this.selectionInstance = new $.Selection(options);\n        }\n        return this.selectionInstance;\n    };\n\n\n    /**\n    * @class Selection\n    * @classdesc Provides functionality for selecting part of an image\n    * @memberof OpenSeadragon\n    * @param {Object} options\n    */\n    $.Selection = function ( options ) {\n\n        $.extend( true, this, {\n            // internal state properties\n            viewer:                  null,\n            isSelecting:             false,\n            buttonActiveImg:         false,\n            rectDone:                true,\n\n            // options\n            element:                 null,\n            toggleButton:            null,\n            showSelectionControl:    true,\n            showConfirmDenyButtons:  true,\n            styleConfirmDenyButtons: true,\n            returnPixelCoordinates:  true,\n            keyboardShortcut:        'c',\n            rect:                    null,\n            startRotated:            false, // useful for rotated crops\n            startRotatedHeight:      0.1,\n            restrictToImage:         false,\n            onSelection:             null,\n            prefixUrl:               null,\n            navImages:               {\n                selection: {\n                    REST:   'selection_rest.png',\n                    GROUP:  'selection_grouphover.png',\n                    HOVER:  'selection_hover.png',\n                    DOWN:   'selection_pressed.png'\n                },\n                selectionConfirm: {\n                    REST:   'selection_confirm_rest.png',\n                    GROUP:  'selection_confirm_grouphover.png',\n                    HOVER:  'selection_confirm_hover.png',\n                    DOWN:   'selection_confirm_pressed.png'\n                },\n                selectionCancel: {\n                    REST:   'selection_cancel_rest.png',\n                    GROUP:  'selection_cancel_grouphover.png',\n                    HOVER:  'selection_cancel_hover.png',\n                    DOWN:   'selection_cancel_pressed.png'\n                },\n            },\n            handleStyle: {\n                top:        '50%',\n                left:       '50%',\n                width:      '6px',\n                height:     '6px',\n                margin:     '-4px 0 0 -4px',\n                background: '#000',\n                border:     '1px solid #ccc'\n            },\n            cornersStyle: {\n                width:      '6px',\n                height:     '6px',\n                background: '#000',\n                border:     '1px solid #ccc'\n            }\n\n        }, options );\n\n        $.extend( true, this.navImages, this.viewer.navImages );\n\n        if (!this.element) {\n            this.element = $.makeNeutralElement('div');\n            this.element.style.background = 'rgba(0, 0, 0, 0.1)';\n            this.element.className        = 'selection-box';\n        }\n        this.borders = this.borders || [];\n        var handle;\n        var corners = [];\n        for (var i = 0; i < 4; i++) {\n            if (!this.borders[i]) {\n                this.borders[i]                  = $.makeNeutralElement('div');\n                this.borders[i].className        = 'border-' + i;\n                this.borders[i].style.position   = 'absolute';\n                this.borders[i].style.width      = '1px';\n                this.borders[i].style.height     = '1px';\n                this.borders[i].style.background = '#fff';\n            }\n\n            handle                  = $.makeNeutralElement('div');\n            handle.className        = 'border-' + i + '-handle';\n            handle.style.position   = 'absolute';\n            handle.style.top        = this.handleStyle.top;\n            handle.style.left       = this.handleStyle.left;\n            handle.style.width      = this.handleStyle.width;\n            handle.style.height     = this.handleStyle.height;\n            handle.style.margin     = this.handleStyle.margin;\n            handle.style.background = this.handleStyle.background;\n            handle.style.border     = this.handleStyle.border;\n            new $.MouseTracker({\n                element:     this.borders[i],\n                dragHandler: onBorderDrag.bind(this, i),\n            });\n\n            corners[i]                  = $.makeNeutralElement('div');\n            corners[i].className        = 'corner-' + i + '-handle';\n            corners[i].style.position   = 'absolute';\n            corners[i].style.width      = this.cornersStyle.width;\n            corners[i].style.height     = this.cornersStyle.height;\n            corners[i].style.background = this.cornersStyle.background;\n            corners[i].style.border     = this.cornersStyle.border;\n            new $.MouseTracker({\n                element:     corners[i],\n                dragHandler: onBorderDrag.bind(this, i + 0.5),\n            });\n\n            this.borders[i].appendChild(handle);\n            this.element.appendChild(this.borders[i]);\n            // defer corners, so they are appended last\n            setTimeout(this.element.appendChild.bind(this.element, corners[i]), 0);\n        }\n        this.borders[0].style.top = 0;\n        this.borders[0].style.width = '100%';\n        this.borders[1].style.right = 0;\n        this.borders[1].style.height = '100%';\n        this.borders[2].style.bottom = 0;\n        this.borders[2].style.width = '100%';\n        this.borders[3].style.left = 0;\n        this.borders[3].style.height = '100%';\n        corners[0].style.top = '-3px';\n        corners[0].style.left = '-3px';\n        corners[1].style.top = '-3px';\n        corners[1].style.right = '-3px';\n        corners[2].style.bottom = '-3px';\n        corners[2].style.right = '-3px';\n        corners[3].style.bottom = '-3px';\n        corners[3].style.left = '-3px';\n\n        if (!this.overlay) {\n            this.overlay = new $.SelectionOverlay(this.element, this.rect || new $.SelectionRect());\n        }\n\n        this.innerTracker = new $.MouseTracker({\n            element:            this.element,\n            clickTimeThreshold: this.viewer.clickTimeThreshold,\n            clickDistThreshold: this.viewer.clickDistThreshold,\n            dragHandler:        $.delegate( this, onInsideDrag ),\n            dragEndHandler:     $.delegate( this, onInsideDragEnd ),\n            // keyHandler:         $.delegate( this, onKeyPress ),\n            clickHandler:       $.delegate( this, onClick ),\n            // scrollHandler:      $.delegate( this.viewer, this.viewer.innerTracker.scrollHandler ),\n            // pinchHandler:       $.delegate( this.viewer, this.viewer.innerTracker.pinchHandler ),\n        });\n\n        this.outerTracker = new $.MouseTracker({\n            element:            this.viewer.canvas,\n            clickTimeThreshold: this.viewer.clickTimeThreshold,\n            clickDistThreshold: this.viewer.clickDistThreshold,\n            dragHandler:        $.delegate( this, onOutsideDrag ),\n            dragEndHandler:     $.delegate( this, onOutsideDragEnd ),\n            clickHandler:       $.delegate( this, onClick ),\n            startDisabled:      !this.isSelecting,\n        });\n\n        if (this.keyboardShortcut) {\n            $.addEvent(\n                this.viewer.container,\n                'keypress',\n                $.delegate(this, onKeyPress),\n                false\n            );\n        }\n\n        var prefix = this.prefixUrl || this.viewer.prefixUrl || '';\n        var useGroup = this.viewer.buttonGroup && this.viewer.buttonGroup.buttons;\n        var anyButton = useGroup ? this.viewer.buttonGroup.buttons[0] : null;\n        var onFocusHandler = anyButton ? anyButton.onFocus : null;\n        var onBlurHandler = anyButton ? anyButton.onBlur : null;\n        if (this.showSelectionControl) {\n            this.toggleButton = new $.Button({\n                element:    this.toggleButton ? $.getElement( this.toggleButton ) : null,\n                clickTimeThreshold: this.viewer.clickTimeThreshold,\n                clickDistThreshold: this.viewer.clickDistThreshold,\n                tooltip:    $.getString('Tooltips.SelectionToggle') || 'Toggle selection',\n                srcRest:    prefix + this.navImages.selection.REST,\n                srcGroup:   prefix + this.navImages.selection.GROUP,\n                srcHover:   prefix + this.navImages.selection.HOVER,\n                srcDown:    prefix + this.navImages.selection.DOWN,\n                onRelease:  this.toggleState.bind( this ),\n                onFocus:    onFocusHandler,\n                onBlur:     onBlurHandler\n            });\n            if (useGroup) {\n                this.viewer.buttonGroup.buttons.push(this.toggleButton);\n                this.viewer.buttonGroup.element.appendChild(this.toggleButton.element);\n            }\n            if (this.toggleButton.imgDown) {\n                this.buttonActiveImg = this.toggleButton.imgDown.cloneNode(true);\n                this.toggleButton.element.appendChild(this.buttonActiveImg);\n            }\n        }\n        if (this.showConfirmDenyButtons) {\n            this.confirmButton = new $.Button({\n                element:    this.confirmButton ? $.getElement( this.confirmButton ) : null,\n                clickTimeThreshold: this.viewer.clickTimeThreshold,\n                clickDistThreshold: this.viewer.clickDistThreshold,\n                tooltip:    $.getString('Tooltips.SelectionConfirm') || 'Confirm selection',\n                srcRest:    prefix + this.navImages.selectionConfirm.REST,\n                srcGroup:   prefix + this.navImages.selectionConfirm.GROUP,\n                srcHover:   prefix + this.navImages.selectionConfirm.HOVER,\n                srcDown:    prefix + this.navImages.selectionConfirm.DOWN,\n                onRelease:  this.confirm.bind( this ),\n                onFocus:    onFocusHandler,\n                onBlur:     onBlurHandler\n            });\n            var confirm = this.confirmButton.element;\n            confirm.classList.add('confirm-button');\n            this.element.appendChild(confirm);\n\n            this.cancelButton = new $.Button({\n                element:    this.cancelButton ? $.getElement( this.cancelButton ) : null,\n                clickTimeThreshold: this.viewer.clickTimeThreshold,\n                clickDistThreshold: this.viewer.clickDistThreshold,\n                tooltip:    $.getString('Tooltips.SelectionConfirm') || 'Cancel selection',\n                srcRest:    prefix + this.navImages.selectionCancel.REST,\n                srcGroup:   prefix + this.navImages.selectionCancel.GROUP,\n                srcHover:   prefix + this.navImages.selectionCancel.HOVER,\n                srcDown:    prefix + this.navImages.selectionCancel.DOWN,\n                onRelease:  this.cancel.bind( this ),\n                onFocus:    onFocusHandler,\n                onBlur:     onBlurHandler\n            });\n            var cancel = this.cancelButton.element;\n            cancel.classList.add('cancel-button');\n            this.element.appendChild(cancel);\n\n            if (this.styleConfirmDenyButtons) {\n                confirm.style.position = 'absolute';\n                confirm.style.top = '50%';\n                confirm.style.left = '50%';\n                confirm.style.transform = 'translate(-100%, -50%)';\n\n                cancel.style.position = 'absolute';\n                cancel.style.top = '50%';\n                cancel.style.left = '50%';\n                cancel.style.transform = 'translate(0, -50%)';\n            }\n        }\n\n        this.viewer.addHandler('selection', this.onSelection);\n\n        this.viewer.addHandler('open', this.draw.bind(this));\n        this.viewer.addHandler('animation', this.draw.bind(this));\n        this.viewer.addHandler('resize', this.draw.bind(this));\n        this.viewer.addHandler('rotate', this.draw.bind(this));\n    };\n\n    $.extend( $.Selection.prototype, $.ControlDock.prototype, /** @lends OpenSeadragon.Selection.prototype */{\n\n        toggleState: function() {\n            return this.setState(!this.isSelecting);\n        },\n\n        setState: function(enabled) {\n            this.isSelecting = enabled;\n            // this.viewer.innerTracker.setTracking(!enabled);\n            this.outerTracker.setTracking(enabled);\n            enabled ? this.draw() : this.undraw();\n            if (this.buttonActiveImg) {\n                this.buttonActiveImg.style.visibility = enabled ? 'visible' : 'hidden';\n            }\n            this.viewer.raiseEvent('selection_toggle', {enabled: enabled});\n            return this;\n        },\n\n        enable: function() {\n            return this.setState(true);\n        },\n\n        disable: function() {\n            return this.setState(false);\n        },\n\n        draw: function() {\n            if (this.rect) {\n                this.overlay.update(this.rect.normalize());\n                this.overlay.drawHTML(this.viewer.drawer.container, this.viewer.viewport);\n            }\n            return this;\n        },\n\n        undraw: function() {\n            this.overlay.destroy();\n            this.rect = null;\n            return this;\n        },\n\n        confirm: function() {\n            if (this.rect) {\n                var result = this.rect.normalize();\n                if (this.returnPixelCoordinates) {\n                    var real = this.viewer.viewport.viewportToImageRectangle(result);\n                    real = $.SelectionRect.fromRect(real).round();\n                    real.rotation = result.rotation;\n                    result = real;\n                }\n                this.viewer.raiseEvent('selection', result);\n                this.undraw();\n            }\n            return this;\n        },\n\n        cancel: function() {\n            this.viewer.raiseEvent('selection_cancel', false);\n            return this.undraw();\n        },\n    });\n\n    function onOutsideDrag(e) {\n        // Disable move when makeing new selection\n        this.viewer.setMouseNavEnabled(false);\n        var delta = this.viewer.viewport.deltaPointsFromPixels(e.delta, true);\n        var end = this.viewer.viewport.pointFromPixel(e.position, true);\n        var start = new $.Point(end.x - delta.x, end.y - delta.y);\n        if (!this.rect) {\n            if (this.restrictToImage) {\n                if (!pointIsInImage(this, start)) {\n                    return;\n                }\n                restrictVector(delta, end);\n            }\n            if (this.startRotated) {\n                this.rotatedStartPoint = start;\n                this.rect = getPrerotatedRect(start, end, this.startRotatedHeight);\n            } else {\n                this.rect = new $.SelectionRect(start.x, start.y, delta.x, delta.y);\n            }\n            this.rectDone = false;\n        } else {\n            var oldRect;\n            if (this.restrictToImage) {\n                oldRect = this.rect.clone();\n            }\n            if (this.rectDone) {\n                // rotate\n                var angle1 = this.rect.getAngleFromCenter(start);\n                var angle2 = this.rect.getAngleFromCenter(end);\n                this.rect.rotation = (this.rect.rotation + angle1 - angle2) % Math.PI;\n            } else {\n                if (this.startRotated) {\n                    this.rect = getPrerotatedRect(this.rotatedStartPoint, end, this.startRotatedHeight);\n                } else {\n                    this.rect.width += delta.x;\n                    this.rect.height += delta.y;\n                }\n            }\n            var bounds = this.viewer.world.getHomeBounds();\n            if (this.restrictToImage && !this.rect.fitsIn(new $.Rect(0, 0, bounds.width, bounds.height))) {\n                this.rect = oldRect;\n            }\n        }\n        this.draw();\n    }\n\n    function onOutsideDragEnd() {\n        // Eable move after new selection is done\n        this.viewer.setMouseNavEnabled(true);\n        this.rectDone = true;\n    }\n\n    function onClick() {\n        this.viewer.canvas.focus();\n    }\n\n    function onInsideDrag(e) {\n        $.addClass(this.element, 'dragging');\n        var delta = this.viewer.viewport.deltaPointsFromPixels(e.delta, true);\n        this.rect.x += delta.x;\n        this.rect.y += delta.y;\n        var bounds = this.viewer.world.getHomeBounds();\n        if (this.restrictToImage && !this.rect.fitsIn(new $.Rect(0, 0, bounds.width, bounds.height))) {\n            this.rect.x -= delta.x;\n            this.rect.y -= delta.y;\n        }\n        this.draw();\n    }\n\n    function onInsideDragEnd() {\n        $.removeClass(this.element, 'dragging');\n    }\n\n    function onBorderDrag(border, e) {\n        var delta = e.delta;\n        var rotation = this.rect.getDegreeRotation();\n        var center;\n        var oldRect = this.restrictToImage ? this.rect.clone() : null;\n        if (rotation !== 0) {\n            // adjust vector\n            delta = delta.rotate(-1 * rotation, new $.Point(0, 0));\n            center = this.rect.getCenter();\n        }\n        delta = this.viewer.viewport.deltaPointsFromPixels(delta, true);\n        switch (border) {\n            case 0:\n                this.rect.y += delta.y;\n                this.rect.height -= delta.y;\n                break;\n            case 1:\n                this.rect.width += delta.x;\n                break;\n            case 2:\n                this.rect.height += delta.y;\n                break;\n            case 3:\n                this.rect.x += delta.x;\n                this.rect.width -= delta.x;\n                break;\n            case 0.5:\n                this.rect.y += delta.y;\n                this.rect.height -= delta.y;\n                this.rect.x += delta.x;\n                this.rect.width -= delta.x;\n                break;\n            case 1.5:\n                this.rect.y += delta.y;\n                this.rect.height -= delta.y;\n                this.rect.width += delta.x;\n                break;\n            case 2.5:\n                this.rect.width += delta.x;\n                this.rect.height += delta.y;\n                break;\n            case 3.5:\n                this.rect.height += delta.y;\n                this.rect.x += delta.x;\n                this.rect.width -= delta.x;\n                break;\n        }\n        if (rotation !== 0) {\n            // calc center deviation\n            var newCenter = this.rect.getCenter();\n            // rotate new center around old\n            var target = newCenter.rotate(rotation, center);\n            // adjust new center\n            delta = target.minus(newCenter);\n            this.rect.x += delta.x;\n            this.rect.y += delta.y;\n        }\n        var bounds = this.viewer.world.getHomeBounds();\n        if (this.restrictToImage && !this.rect.fitsIn(new $.Rect(0, 0, bounds.width, bounds.height))) {\n            this.rect = oldRect;\n        }\n        this.draw();\n    }\n\n    function onKeyPress(e) {\n        var key = e.keyCode ? e.keyCode : e.charCode;\n        if (key === 13) {\n            this.confirm();\n        } else if (String.fromCharCode(key) === this.keyboardShortcut) {\n            this.toggleState();\n        }\n    }\n\n    function getPrerotatedRect(start, end, height) {\n        if (start.x > end.x) {\n            // always draw left to right\n            var x = start;\n            start = end;\n            end = x;\n        }\n        var delta = end.minus(start);\n        var dist = start.distanceTo(end);\n        var angle = -1 * Math.atan2(delta.x, delta.y) + (Math.PI / 2);\n        var center = new $.Point(\n            delta.x / 2 + start.x,\n            delta.y / 2 + start.y\n        );\n        var rect = new $.SelectionRect(\n            center.x - (dist / 2),\n            center.y - (height / 2),\n            dist,\n            height,\n            angle\n        );\n        var heightModDelta = new $.Point(0, height);\n        heightModDelta = heightModDelta.rotate(rect.getDegreeRotation(), new $.Point(0, 0));\n        rect.x += heightModDelta.x / 2;\n        rect.y += heightModDelta.y / 2;\n        return rect;\n    }\n\n    function pointIsInImage(self, point) {\n        var bounds = self.viewer.world.getHomeBounds();\n        return point.x >= 0 && point.x <= bounds.width && point.y >= 0 && point.y <= bounds.height;\n    }\n\n    function restrictVector(delta, end) {\n        var start;\n        for (var prop in {x: 0, y: 0}) {\n            start = end[prop] - delta[prop];\n            if (start < 1 && start > 0) {\n                if (end[prop] > 1) {\n                    delta[prop] -= end[prop] - 1;\n                    end[prop] = 1;\n                } else if (end[prop] < 0) {\n                    delta[prop] -= end[prop];\n                    end[prop] = 0;\n                }\n            }\n        }\n    }\n\n})(OpenSeadragon);\n", "(function( $ ){\n    'use strict';\n\n    /**\n     * @class Overlay\n     * @classdesc Provides a way to float an HTML element on top of the viewer element.\n     *\n     * @memberof OpenSeadragon\n     * @param {Object} options\n     * @param {Element} options.element\n     * @param {OpenSeadragon.Point|OpenSeadragon.Rect|OpenSeadragon.SelectionRect} options.location - The\n     * location of the overlay on the image. If a {@link OpenSeadragon.Point}\n     * is specified, the overlay will keep a constant size independently of the\n     * zoom. If a {@link OpenSeadragon.Rect} is specified, the overlay size will\n     * be adjusted when the zoom changes.\n     * @param {OpenSeadragon.OverlayPlacement} [options.placement=OpenSeadragon.OverlayPlacement.TOP_LEFT]\n     * Relative position to the viewport.\n     * Only used if location is a {@link OpenSeadragon.Point}.\n     * @param {OpenSeadragon.Overlay.OnDrawCallback} [options.onDraw]\n     * @param {Boolean} [options.checkResize=true] Set to false to avoid to\n     * check the size of the overlay everytime it is drawn when using a\n     * {@link OpenSeadragon.Point} as options.location. It will improve\n     * performances but will cause a misalignment if the overlay size changes.\n     */\n    $.SelectionOverlay = function( element, location) {\n        $.Overlay.apply( this, arguments );\n\n        // set the rotation in radians\n        if ( $.isPlainObject( element ) ) {\n            this.rotation = element.location.rotation || 0;\n        } else {\n            this.rotation = location.rotation || 0;\n        }\n    };\n\n    $.SelectionOverlay.prototype = $.extend( Object.create($.Overlay.prototype), {\n\n        /**\n         * @function\n         * @param {Element} container\n         */\n        drawHTML: function() {\n            $.Overlay.prototype.drawHTML.apply( this, arguments );\n            this.style.transform = this.style.transform.replace(/ ?rotate\\(.+rad\\)/, '') +\n                ' rotate(' + this.rotation + 'rad)';\n        },\n\n        /**\n         * @function\n         * @param {OpenSeadragon.Point|OpenSeadragon.Rect} location\n         * @param {OpenSeadragon.OverlayPlacement} position\n         */\n        update: function( location ) {\n            $.Overlay.prototype.update.apply( this, arguments );\n            this.rotation = location.rotation || 0;\n        }\n    });\n\n}( OpenSeadragon ));\n", "(function( $ ){\n    'use strict';\n\n    /**\n     * @class SelectionRect\n     * @classdesc A display rectangle is very similar to {@link OpenSeadragon.Rect} but adds rotation\n     * around the center point\n     *\n     * @memberof OpenSeadragon\n     * @extends OpenSeadragon.Rect\n     * @param {Number} x The vector component 'x'.\n     * @param {Number} y The vector component 'y'.\n     * @param {Number} width The vector component 'height'.\n     * @param {Number} height The vector component 'width'.\n     * @param {Number} rotation The rotation in radians\n     */\n    $.SelectionRect = function( x, y, width, height, rotation ) {\n        $.Rect.apply( this, [ x, y, width, height ] );\n\n        /**\n         * The rotation in radians\n         * @member {Number} rotation\n         * @memberof OpenSeadragon.SelectionRect#\n         */\n        this.rotation = rotation || 0;\n    };\n\n    $.SelectionRect.fromRect = function(rect) {\n        return new $.SelectionRect(\n            rect.x,\n            rect.y,\n            rect.width,\n            rect.height\n        );\n    };\n\n    $.SelectionRect.prototype = $.extend( Object.create($.Rect.prototype), {\n\n        /**\n         * @function\n         * @returns {OpenSeadragon.Rect} a duplicate of this Rect\n         */\n        clone: function() {\n            return new $.SelectionRect(this.x, this.y, this.width, this.height, this.rotation);\n        },\n\n        /**\n         * Determines if two Rectangles have equivalent components.\n         * @function\n         * @param {OpenSeadragon.Rect} rectangle The Rectangle to compare to.\n         * @return {Boolean} 'true' if all components are equal, otherwise 'false'.\n         */\n        equals: function( other ) {\n            return $.Rect.prototype.equals.apply(this, [ other ]) &&\n                ( this.rotation === other.rotation );\n        },\n\n        /**\n         * Provides a string representation of the rectangle which is useful for\n         * debugging.\n         * @function\n         * @returns {String} A string representation of the rectangle.\n         */\n        toString: function() {\n            return '[' +\n                (Math.round(this.x*100) / 100) + ',' +\n                (Math.round(this.y*100) / 100) + ',' +\n                (Math.round(this.width*100) / 100) + 'x' +\n                (Math.round(this.height*100) / 100) + '@' +\n                (Math.round(this.rotation*100) / 100) +\n            ']';\n        },\n\n        swapWidthHeight: function() {\n            var swapped = this.clone();\n            swapped.width = this.height;\n            swapped.height = this.width;\n            swapped.x += (this.width - this.height) / 2;\n            swapped.y += (this.height - this.width) / 2;\n            return swapped;\n        },\n\n        /**\n         * @function\n         * @returns {Number} The rotaion in degrees\n         */\n        getDegreeRotation: function() {\n            return this.rotation * (180/Math.PI);\n        },\n\n        /**\n         * @function\n         * @param {OpenSeadragon.Point} point\n         * @returns {Number} The angle in radians\n         */\n        getAngleFromCenter: function(point) {\n            var diff = point.minus(this.getCenter());\n            return Math.atan2(diff.x, diff.y);\n        },\n\n        /**\n         * Rounds pixel coordinates\n         * @function\n         * @returns {SelectionRect} The altered rect\n         */\n        round: function() {\n            return new $.SelectionRect(\n                Math.round(this.x),\n                Math.round(this.y),\n                Math.round(this.width),\n                Math.round(this.height),\n                this.rotation\n            );\n        },\n\n        /**\n         * Fixes negative width/height, rotation larger than PI\n         * @function\n         * @returns {SelectionRect} The normalized rect\n         */\n        normalize: function() {\n            var fixed = this.clone();\n            if (fixed.width < 0) {\n                fixed.x += fixed.width;\n                fixed.width *= -1;\n            }\n            if (fixed.height < 0) {\n                fixed.y += fixed.height;\n                fixed.height *= -1;\n            }\n            fixed.rotation %= Math.PI;\n            return fixed;\n        },\n\n        /**\n         * @function\n         * @param {OpenSeadragon.Rect} area\n         * @returns {Boolean} Does this rect fit in a specified area\n         */\n        fitsIn: function(area) {\n            var rect = this.normalize();\n            var corners = [\n                rect.getTopLeft(),\n                rect.getTopRight(),\n                rect.getBottomRight(),\n                rect.getBottomLeft(),\n            ];\n            var center = rect.getCenter();\n            var rotation = rect.getDegreeRotation();\n            var areaEnd = area.getBottomRight();\n            for (var i = 0; i < 4; i++) {\n                corners[i] = corners[i].rotate(rotation, center);\n                if (corners[i].x < area.x || corners[i].x > areaEnd.x ||\n                    corners[i].y < area.y || corners[i].y > areaEnd.y) {\n                    return false;\n                }\n            }\n            return true;\n        },\n\n        /**\n         * Reduces rotation to within [-45, 45] degrees by swapping width & height\n         * @function\n         * @returns {SelectionRect} The altered rect\n         */\n        reduceRotation: function() {\n            var reduced;\n            if (this.rotation < Math.PI / (-4)) {\n                reduced = this.swapWidthHeight();\n                reduced.rotation += Math.PI / 2;\n            } else if (this.rotation > Math.PI / 4) {\n                reduced = this.swapWidthHeight();\n                reduced.rotation -= Math.PI / 2;\n            } else {\n                reduced = this.clone();\n            }\n            return reduced;\n        },\n    });\n\n}( OpenSeadragon ));\n"]}