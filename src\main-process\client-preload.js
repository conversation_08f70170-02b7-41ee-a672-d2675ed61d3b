const { ipcRenderer, contextBridge } = require('electron/renderer');
  
contextBridge.exposeInMainWorld(
  'electron',
  {
    /* RENDERER TO MAIN */
    application: (command, params) => ipcRenderer.invoke('application', command, params),
    variables: (command, params) => ipcRenderer.invoke('variables', command, params),
    auth: (command, params) => ipcRenderer.invoke('auth', command, params),
    metis: (command, params) => ipcRenderer.invoke('metis', command, params),
    database: (command, params) => ipcRenderer.invoke('database', command, params),
    filesystem: (command, params) => ipcRenderer.invoke('filesystem', command, params),
    sharp: (command, params) => ipcRenderer.invoke('sharp', command, params),
    worker: (command, params) => ipcRenderer.invoke('worker', command, params),
    ssh: (command, params) => ipcRenderer.invoke('ssh', command, params),

    /* MAIN TO RENDERER */
    onJobPreviewCreationCompleted: (callback) => ipcRenderer.on('job-preview-creation-completed', (_event, value) => callback(value)),
    onScanPreviewCreationCompleted: (callback) => ipcRenderer.on('scan-preview-creation-completed', (_event, value) => callback(value))
  }
)