let ip = require("ip");
const os = require("os");

exports.MSD_SOCKET_HOST=ip.address();
exports.MSD_SOCKET_PORT=6000;
exports.MSD_COMMAND_TIMEOUT=10000;
exports.MSD_COMMAND_TIMEOUT_DEV=1000;

exports.ldapOptions = {
  url: 'ldap://BAV-AD1.bav.local',
  bindDN: 'CN=Administrator,CN=Users,DC=bav,DC=local',
  bindCredentials: 'BV$DC2019!10',
  searchBase: 'DC=bav,DC=local',
  searchFilter: '(|(sAMAccountName={{username}})(mail={{username}}))'
}

exports.dbOptions = {
  host: "**************",
  port: 3306,
  database: "DEV_INSIDE",
  user: "prisma",
  password: "2023_09(prisma)BAV",
}

exports.logParams = {
  idDigit: null,
  host: os.hostname(),
  user: null,
  userProfile: null,
  userCompany: null,
  scannerId: null
}

exports.selectedJob = {
  idDigit: null,
  statusDigit: null
};

exports.statusDigitMap = {
  acquisizione: 'in pausa',
  validazione: 'acquisito',
  finalizzazione: 'validato'
}

exports.isilonSshOptions = {
  host: 'isilon.bav.local',
  username: '<EMAIL>',
  password: 'BV$pr1sm42023!11'
}