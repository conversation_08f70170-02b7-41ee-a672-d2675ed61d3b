const { app, BrowserWindow, screen, ipcMain, protocol, net, Menu } = require('electron/main');
const { autoUpdater } = require('electron-updater');
const serve = require('electron-serve');
const path = require('node:path');

/* IPC Handlers in separate files */
const application = require('./ipc-handlers/application');
const variables = require('./ipc-handlers/variables');
const auth = require('./ipc-handlers/auth');
const database = require('./ipc-handlers/database');
const filesystem = require('./ipc-handlers/filesystem');
const sharp = require('./ipc-handlers/sharp');
const metis = require('./ipc-handlers/metis');
const worker = require('./ipc-handlers/worker');
const ssh = require('./ipc-handlers/ssh');

let screenWidth;
let screenHeight;
let clientWin;

const loadURL = serve({ directory: 'public' });
const isDev = !app.isPackaged;

process.on('uncaughtException', function (err) {
    console.log(err);
});

/* Hide Menu for performance reasons */
Menu.setApplicationMenu(null);

function createWindow() {
    clientWin = new BrowserWindow({
        width: screenWidth,
        height: screenHeight,
        webPreferences: {
            preload: path.join(__dirname, 'client-preload.js')
        },
        icon: path.join(__dirname, '..', '..', 'public', 'prisma_app_logo.png'),
        show: false,
        autoHideMenuBar: true,
        roundedCorners: true,
        backgroundColor: '#FFF'
    });

    if (isDev) {
        process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = "true";
        clientWin.loadURL('http://localhost:5000/');
    } else {
        loadURL(clientWin);
    }

    clientWin.on('closed', function () {
        clientWin = null
    });

    clientWin.once('ready-to-show', () => {
        clientWin.maximize();
        clientWin.show();
    });

    /* Open DevTools with F12 */
    clientWin.webContents.on('before-input-event', (_, input) => {
        if (input.type === 'keyDown' && input.key === 'F12') {
            clientWin.webContents.toggleDevTools();
        }
    });
}

protocol.registerSchemesAsPrivileged([
    { scheme: 'local', privileges: { standard: true, secure: true, supportFetchAPI: true } },
    { scheme: 'isilon', privileges: { standard: true, secure: true, supportFetchAPI: true } }
])

app.on('ready', async () => {
    const hostInfo = await database['db-get-host-info'].call(this);
    if(!isDev && hostInfo?.check_for_updates) autoUpdater.checkForUpdates();

    protocol.handle('local', (request) => {
        return net.fetch('file://C:' + request.url.slice('local://'.length + 1));
    })

    protocol.handle('isilon', (request) => {
        return net.fetch('file://P:' + request.url.slice('isilon://'.length + 1));
    })
  
    createHandlers();

    const { width, height } = screen.getPrimaryDisplay().workAreaSize;
    screenWidth = width;
    screenHeight = height;

    createWindow();
});

app.on('before-quit', async (e) => {
    const { selectedJob, statusDigitMap } = require ( './config');

    e.preventDefault();
    
    /* Set status_digit to previous one */
    const params = {};
    params.statusDigit = statusDigitMap[selectedJob.statusDigit];
    params.idDigit = selectedJob.idDigit;

    if(params.idDigit && params.statusDigit)
        await database["db-set-status-digit"].call(this, params);

    /* Send disconnect command to Metis */
    await metis['msd-disconnect'].call(this);
    
    app.exit(0);
});

autoUpdater.on('update-downloaded', (ev, info) => {
    autoUpdater.quitAndInstall();
})

const createHandlers = () => {
    /* application */
    ipcMain.handle('application', async (event, ...args) => {
        const command = args[0];
        const params = args[1];
        const fn = application[command];

        return await fn.call(this, params);
    })

    /* variables */
    ipcMain.handle('variables', async (event, ...args) => {
        const command = args[0];
        const params = args[1];
        const fn = variables[command];

        return await fn.call(this, params);
    })

    /* auth */
    ipcMain.handle('auth', async (event, ...args) => {
        const command = args[0];
        const params = args[1];
        const fn = auth[command];

        return await fn.call(this, params);
    })

    /* database */
    ipcMain.handle('database', async (event, ...args) => {
        const command = args[0];
        const params = args[1];
        const fn = database[command];

        return await fn.call(this, params);
    })

    /* filesystem */
    ipcMain.handle('filesystem', async (event, ...args) => {
        const command = args[0];
        const params = args[1];
        const fn = filesystem[command];

        return await fn.call(this, params);
    })

    /* sharp */
    ipcMain.handle('sharp', async (event, ...args) => {
        const command = args[0];
        const params = args[1];
        const fn = sharp[command];

        return await fn.call(this, params);
    })

    /* metis */
    ipcMain.handle('metis', async (event, ...args) => {
        const command = args[0];
        const params = args[1];
        const fn = metis[command];

        return await fn.call(this, params);
    })

    /* worker */
    ipcMain.handle('worker', async (event, ...args) => {
        const command = args[0];
        const params = args[1];
        const fn = worker[command];

        return await fn.call(this, params);
    })

    /* ssh */
    ipcMain.handle('ssh', async (event, ...args) => {
        const command = args[0];
        const params = args[1];
        const fn = ssh[command];

        return await fn.call(this, params);
    })
}