const { app } = require('electron/main');

const restart = (params) => {
    if(params?.user){
        if(params?.idDigit)
            app.relaunch({ args: process.argv.slice(1).concat(['--user=' + JSON.stringify(params.user)], ['--iddigit=' + params.idDigit]) });
        else
            app.relaunch({ args: process.argv.slice(1).concat(['--user=' + JSON.stringify(params.user)]) });
    } else {
        /* Remove --user arg if found */
        const userArgIndex = process.argv.findIndex((arg) => arg.includes('--user='));
        
        if( userArgIndex != -1)
            process.argv.splice(userArgIndex, 1);
        
        app.relaunch({ args: process.argv.slice(1) });
    }

    app.quit();
}

const getLoggedUser = () => {
    return app.commandLine.getSwitchValue('user');
}

const getLastJob = () => {
    return app.commandLine.getSwitchValue('iddigit');
}

module.exports = {
    'restart': restart,
    'get-logged-user': getLoggedUser,
    'get-last-job': getLastJob
}