const { authenticate } = require('ldap-authentication');
const { ldapOptions } = require('../config');

let response = {
    result: null,
    data: null
};

const login = (params) => {
    return new Promise(async (resolve, reject) => {

        let options = {
            ldapOpts: {
                url: ldapOptions.url
            },
            adminDn: ldapOptions.bindDN,
            adminPassword: ldapOptions.bindCredentials,
            userSearchBase: ldapOptions.searchBase,
            usernameAttribute: 'sAMAccountName',
            username: params.username,
            userPassword: params.password
        }

        try{
            let user = await authenticate(options)
            
            response.result = "OK";
            response.data = {};
            response.data["username"] = user.sAMAccountName;
            response.data["displayName"] = user.displayName;
            response.data["memberOf"] = [];

            if(Array.isArray(user.memberOf)) {
                user.memberOf.forEach(group => {
                    response.data["memberOf"].push(group.substring(3, group.indexOf(',')));
                });
            } else {
                response.data["memberOf"].push(user.memberOf.substring(3, user.memberOf.indexOf(',')));
            }
            
        } catch(err) {
            response.result = "KO";
            response.data = err;
        } finally {
            resolve(response);
        }
    });
}

module.exports = {
    'login': login
}