let { dbOptions, logParams, selectedJob } = require('../config');
const utility = require('../utilities/utility');
const mysql = require('mysql2/promise');

/**
 * Private function called by below methods to initialize DB connection
 */
const getConnection = async () => {
    const connection = await mysql.createConnection({
        host: dbOptions.host,
        port: dbOptions.port,
        database: dbOptions.database,
        user: dbOptions.user,
        password: dbOptions.password
    });

    return connection;
}

/**
 * Query eseguita sulla vista VW_JOBS dopo il login per ottenere la lista dei Job per la società
 */
const getHostInfo = async () => {
    let connection;
    try {
        connection = await getConnection();

        const query = "SELECT host, scanner_id, check_for_updates, is_leggio FROM TB_CNF_Host_Mapping WHERE host = ?";
        let [ rows ] = await connection.execute(query, [ logParams.host ]);

        return rows[0];
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Query eseguita sulla vista VW_JOBS dopo il login per ottenere la lista dei Job per la società
 */
const getJobs = async (params) => {
    let connection;
    try {
        connection = await getConnection();
        let query = '';

        if(params.userCompany == 'BAV'){
            if(params.getInRecovery)
                query = "SELECT * FROM VW_Jobs WHERE status_digit = 'finalizzato' AND is_in_recovery = true ORDER BY segnatura";
            else
                query = "SELECT * FROM VW_Jobs WHERE status_digit != 'finalizzato' AND status_digit is not null ORDER BY segnatura";
        } else {
            if(params.getInRecovery)
                query = "SELECT * FROM VW_Jobs WHERE label_societa = ? AND status_digit = 'finalizzato' AND is_in_recovery = true ORDER BY segnatura";
            else
                query = "SELECT * FROM VW_Jobs WHERE label_societa = ? AND status_digit != 'finalizzato' AND status_digit is not null ORDER BY segnatura";
        }
        
        let [ rows ] = params.userCompany == 'BAV' ? await connection.execute(query, [ ]) : await connection.execute(query, [ params.userCompany ]);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Inizializzazione della digitalizzazione dopo la prima volta che è selezionato un Job
 */
const getConfigsForDigit = async (params) => {
    let connection;
    try {
        connection = await getConnection();

        const query = "SELECT * FROM TB_CNF_Tecniche WHERE id_digit = ?";
        let [ rows ] = await connection.execute(query, [ params.idDigit ]);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Salvataggio delle configurazioni del job
 */
const saveConfigsForDigit = async (params) => {
    let connection;
    try {
        params = utility.undefinedToNull(params);

        connection = await getConnection();
        const idDigit = params[0];

        let query = "SELECT * FROM TB_CNF_Tecniche WHERE id_digit = ?";
        let [ rows ] = await connection.execute(query, [ idDigit ]);

        if(rows.length == 0) {
            query = "INSERT INTO TB_CNF_Tecniche (id_digit, s_optical_resolution, s_output_resolution_percentage, s_precision, s_flip_scan, s_um_sharpening_enabled, s_um_sharpening_type, s_um_sharpening_intensity, s_um_sharpening_radius, s_um_sharpening_noise_limit, s_um_sharpening_hv_gain, s_um_sharpening_noise_red_enabled, s_um_sharpening_noise_red_intensity, s_um_sharpening_ch_diff_enabled, s_um_sharpening_ch_diff_intensity, s_color_mode, s_exposure_compensation, s_light_left, s_light_center_left, s_light_center_right, s_light_right, s_icc_profile_source, s_icc_profile_destination, s_icc_profile_display, s_pressure_kg, s_enable_plates, s_enable_balance, s_balance_friction, s_auto_calc_thick_ratio, s_thick_weight_ratio, s_auto_open_after_scan, s_auto_close_on_scan, s_wait_for_scan_pedal, s_wait_for_pressure, s_auto_open_close, s_auto_open_mm_down, s_lock_glass_move, s_open_amount_percentage, s_open_glass_speed, s_close_glass_speed, s_glass_acceleration, s_closing_glass_position, s_work_with_contact, s_contact_distance, s_enable_fine_pressure, s_pressure_accuracy, s_up_speed, s_down_speed, s_stop_scanner_pressure, s_scan_path) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
        } else {
            /* Move idDigit from first to last position */
            params.push(params.shift());
            query = "UPDATE TB_CNF_Tecniche SET s_optical_resolution = ?, s_output_resolution_percentage = ?, s_precision = ?, s_flip_scan = ?, s_um_sharpening_enabled = ?, s_um_sharpening_type = ?, s_um_sharpening_intensity = ?, s_um_sharpening_radius = ?, s_um_sharpening_noise_limit = ?, s_um_sharpening_hv_gain = ?, s_um_sharpening_noise_red_enabled = ?, s_um_sharpening_noise_red_intensity = ?, s_um_sharpening_ch_diff_enabled = ?, s_um_sharpening_ch_diff_intensity = ?, s_color_mode = ?, s_exposure_compensation = ?, s_light_left = ?, s_light_center_left = ?, s_light_center_right = ?, s_light_right = ?, s_icc_profile_source = ?, s_icc_profile_destination = ?, s_icc_profile_display = ?, s_pressure_kg = ?, s_enable_plates = ?, s_enable_balance = ?, s_balance_friction = ?, s_auto_calc_thick_ratio = ?, s_thick_weight_ratio = ?, s_auto_open_after_scan = ?, s_auto_close_on_scan = ?, s_wait_for_scan_pedal = ?, s_wait_for_pressure = ?, s_auto_open_close = ?, s_auto_open_mm_down = ?, s_lock_glass_move = ?, s_open_amount_percentage = ?, s_open_glass_speed = ?, s_close_glass_speed = ?, s_glass_acceleration = ?, s_closing_glass_position = ?, s_work_with_contact = ?, s_contact_distance = ?, s_enable_fine_pressure = ?, s_pressure_accuracy = ?, s_up_speed = ?, s_down_speed = ?, s_stop_scanner_pressure = ?, s_scan_path = ? WHERE id_digit = ?"
        }

        [ rows ] = await connection.execute(query, params);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Salvataggio delle dimensioni della crop area sia in conf_tenciche che in inside
 */
const saveCropAreaDimensions = async (params) => {
    let connection;
    try {
        connection = await getConnection();

        let query = "UPDATE TB_CNF_Tecniche SET a_crop_area_x = ?, a_crop_area_y = ?, a_crop_area_width = ?, a_crop_area_height = ?, a_crop_area_width_pixels = ?, a_crop_area_height_pixels = ?, a_respect_margin = ?, a_crop_central_margin = ? WHERE id_digit = ?";
        let [ rows ] = await connection.execute(query, [ params.cropArea.x, params.cropArea.y, params.cropArea.width, params.cropArea.height, params.cropAreaPixels.width, params.cropAreaPixels.height, params.respectMargin, params.centralMargin, params.idDigit ]);

        query = "UPDATE TB_DIGIT_Acquisizioni SET crop_area = ?, margini = ? WHERE iddigit = ?";
        await connection.execute(query, [ `Larghezza: ${params.cropAreaPixels.width}px, Altezza: ${params.cropAreaPixels.height}px`, `Margine centrale: ${params.centralMargin}px, Margine di rispetto: ${params.respectMargin}px`, params.idDigit ]);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Salvataggio delle dimensioni della crop area della risguardia in inside
 */
const saveCropAreaRisguardia = async (params) => {
    let connection;
    try {
        connection = await getConnection();

        query = "UPDATE TB_DIGIT_Acquisizioni SET crop_risguardia = ? WHERE iddigit = ?";
        let [ rows ] = await connection.execute(query, [ `Larghezza: ${params.cropAreaPixels.width}px, Altezza: ${params.cropAreaPixels.height}px`, params.idDigit ]);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Salvataggio della posizione della crop area in conf_tenciche, dopo una scansione
 */
const saveCropAreaPosition = async (params) => {
    let connection;
    try {
        connection = await getConnection();

        const query = "UPDATE TB_CNF_Tecniche SET a_crop_area_x = ?, a_crop_area_y = ? WHERE id_digit = ?";
        const [ rows ] = await connection.execute(query, [ params.cropArea.x, params.cropArea.y, params.idDigit ]);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Recupero dei template di configurazione tecniche
 */
const getConfigTemplates = async () => {
    let connection;
    try {
        connection = await getConnection();

        const query = "SELECT * FROM TB_CNF_Tecniche_Template";
        let [ rows ] = await connection.execute(query, [ ]);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Salvataggio di un template di configurazione tecniche
 */
const saveConfigTemplate = async (params) => {
    let connection;
    try {
        params = utility.undefinedToNull(params);
        connection = await getConnection();

        const query = "INSERT INTO TB_CNF_Tecniche_Template (template_name, template_description, s_optical_resolution, s_output_resolution_percentage, s_precision, s_flip_scan, s_um_sharpening_enabled, s_um_sharpening_type, s_um_sharpening_intensity, s_um_sharpening_radius, s_um_sharpening_noise_limit, s_um_sharpening_hv_gain, s_um_sharpening_noise_red_enabled, s_um_sharpening_noise_red_intensity, s_um_sharpening_ch_diff_enabled, s_um_sharpening_ch_diff_intensity, s_color_mode, s_exposure_compensation, s_light_left, s_light_center_left, s_light_center_right, s_light_right, s_icc_profile_source, s_icc_profile_destination, s_icc_profile_display, s_pressure_kg, s_enable_plates, s_enable_balance, s_balance_friction, s_auto_calc_thick_ratio, s_thick_weight_ratio, s_auto_open_after_scan, s_auto_close_on_scan, s_wait_for_scan_pedal, s_wait_for_pressure, s_auto_open_close, s_auto_open_mm_down, s_lock_glass_move, s_open_amount_percentage, s_open_glass_speed, s_close_glass_speed, s_glass_acceleration, s_closing_glass_position, s_work_with_contact, s_contact_distance, s_enable_fine_pressure, s_pressure_accuracy, s_up_speed, s_down_speed, s_stop_scanner_pressure, s_scan_path, a_crop_area_x, a_crop_area_y, a_crop_area_width, a_crop_area_height, a_respect_margin, a_crop_central_margin) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        let [ rows ] = await connection.execute(query, params);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Setta lo stato di un job
 */
const setDigitStatus = async (params) => {
    let connection;
    try {
        connection = await getConnection();
        
        let query, rows;
        if(params.scanner && params.host){
            query = "UPDATE TB_DIGIT_Acquisizioni SET statusDigit = ?, scanner = ?, hostPC = ? WHERE iddigit = ?";
            [ rows ] = await connection.execute(query, [ params.statusDigit, params.scanner, params.host, params.idDigit ]);
        } else {
            query = "UPDATE TB_DIGIT_Acquisizioni SET statusDigit = ? WHERE iddigit = ?";
            [ rows ] = await connection.execute(query, [ params.statusDigit, params.idDigit ]);
        }
        
        /* update selectedJob status in config */
        selectedJob.statusDigit = params.statusDigit;

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Recupero degli ordinatori disponibili
 */
const getOrdinatori = async () => {
    let connection;
    try {
        connection = await getConnection();

        const query = "SELECT * FROM TB_CNF_Ordinatori WHERE hidden = false ORDER BY name_short";
        let [ rows ] = await connection.execute(query, [ ]);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Recupero degli ordinatori disponibili
 */
const getEccezioni = async () => {
    let connection;
    try {
        connection = await getConnection();

        const query = "SELECT * FROM TB_CNF_Eccezioni WHERE hidden = false ORDER BY visualization_sequence";
        let [ rows ] = await connection.execute(query, [ ]);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Recupero le scansioni effettuate per il job
 */
const getScansioni = async (params) => {
    let connection;
    try {
        connection = await getConnection();

        const query = "SELECT * FROM TB_CNF_Scansioni WHERE id_digit = ? ORDER BY sequenziale_scansione ASC";
        let [ rows ] = await connection.execute(query, [ params.idDigit ]);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Inserisce una nuova scansione in seguito a comando di Scan
 */
const addScansione = async (params) => {
    let connection;
    try {
        params = utility.undefinedToNull(params);
        connection = await getConnection();

        const query = "INSERT INTO TB_CNF_Scansioni (id_digit, sequenziale_scansione, segnatura, pagina_doppia, ordinamento_arabo, progressione_sx, codice_ordinatore_sx, nome_ordinatore_sx, indice_progressione_sx, lettera_sx, lettera_fine_sx, prefisso_numero_pagina_sx,numero_pagina_sx, numero_pagina_fine_sx, interno_pagina_sx, tipo_pagina_sx, codice_eccezione_sx, nome_eccezione_sx, valore_eccezione_sx, parte_eccezione_sx, display_name_sx, progressione_dx, codice_ordinatore_dx, nome_ordinatore_dx, indice_progressione_dx, lettera_dx, lettera_fine_dx, prefisso_numero_pagina_dx, numero_pagina_dx, numero_pagina_fine_dx, interno_pagina_dx, tipo_pagina_dx, codice_eccezione_dx, nome_eccezione_dx, valore_eccezione_dx, parte_eccezione_dx, display_name_dx, crop_area_width, crop_area_height) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ? , ?, ?, ?, ?, ? , ?, ?, ?, ?, ?, ?, ? , ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        let [ rows ] = await connection.execute(query, params);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Elimina una scansione
 */
const deleteScansione = async (params) => {
    let connection;
    try {
        connection = await getConnection();

        const query = "DELETE FROM TB_CNF_Scansioni WHERE id = ?";
        let [ rows ] = await connection.execute(query, [ params.id ]);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Aggiorna una scansione in seguito al'edit tramite pulsante rinomina
 */
const updateScansione = async (params) => {
    let connection;
    try {
        params = utility.undefinedToNull(params);
        connection = await getConnection();

        const query = "UPDATE TB_CNF_Scansioni SET pagina_doppia = ?, ordinamento_arabo = ?, codice_ordinatore_sx = ?, nome_ordinatore_sx = ?, indice_progressione_sx = ?, lettera_sx = ?, lettera_fine_sx = ?, prefisso_numero_pagina_sx = ?, numero_pagina_sx = ?, numero_pagina_fine_sx = ?, interno_pagina_sx = ?, tipo_pagina_sx = ?, codice_eccezione_sx = ?, nome_eccezione_sx = ?, valore_eccezione_sx = ?, parte_eccezione_sx = ?, display_name_sx = ?, codice_ordinatore_dx = ?, nome_ordinatore_dx = ?, indice_progressione_dx = ?, lettera_dx = ?, lettera_fine_dx = ?, prefisso_numero_pagina_dx = ?, numero_pagina_dx = ?, numero_pagina_fine_dx = ?, interno_pagina_dx = ?, tipo_pagina_dx = ?, codice_eccezione_dx = ?, nome_eccezione_dx = ?, valore_eccezione_dx = ?, parte_eccezione_dx = ?, display_name_dx = ?, crop_area_width = ?, crop_area_height = ? WHERE id = ?";
        let [ rows ] = await connection.execute(query, params);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Aggiorna le dimensioni di una scansione in seguito a comando di Scan con replace abilitato
 */
const updateDimensioniScansione = async (params) => {
    let connection;
    try {
        connection = await getConnection();

        const query = "UPDATE TB_CNF_Scansioni SET created_at = ?, crop_area_width = ?, crop_area_height = ? WHERE id = ?";
        let [ rows ] = await connection.execute(query, [new Date(), params.cropAreaWidthPixels, params.cropAreaHeightPixels, params.id]);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Aggiorna la pagina e il pageType di una scansione
 */
const updatePaginaScansione = async (params) => {
    let connection;
    try {
        connection = await getConnection();

        const query = "UPDATE TB_CNF_Scansioni SET numero_pagina_sx = ?, tipo_pagina_sx = ?, display_name_sx = ?, numero_pagina_dx = ?, tipo_pagina_dx = ?, display_name_dx = ? WHERE id = ?";
        let [ rows ] = await connection.execute(query, [ params.paginaSx, params.pageTypeSx, params.displayNameSx, params.paginaDx, params.pageTypeDx, params.displayNameDx, params.scanId ]);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Aggiorna l'ordinatore di una scansione
 */
const updateOrdinatoreScansione = async (params) => {
    let connection;
    try {
        connection = await getConnection();

        const query = "UPDATE TB_CNF_Scansioni SET codice_ordinatore_sx = ?, nome_ordinatore_sx = ?, display_name_sx = ?, codice_ordinatore_dx = ?, nome_ordinatore_dx = ?, display_name_dx = ? WHERE id = ?";
        let [ rows ] = await connection.execute(query, [ params.codiceOrdinatoreSx, params.nomeOrdinatoreSx, params.displayNameSx, params.codiceOrdinatoreDx, params.nomeOrdinatoreDx, params.displayNameDx, params.scanId ]);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Aggiorna l'eccezione di una scansione
 */
const updateEccezioneScansione = async (params) => {
    let connection;
    try {
        connection = await getConnection();

        const query = "UPDATE TB_CNF_Scansioni SET codice_eccezione_sx = ?, nome_eccezione_sx = ?, valore_eccezione_sx = ?, parte_eccezione_sx = ?, display_name_sx = ?, codice_eccezione_dx = ?, nome_eccezione_dx = ?, valore_eccezione_dx = ?, parte_eccezione_dx = ?, display_name_dx = ?  WHERE id = ?";
        let [ rows ] = await connection.execute(query, [ params.codiceEccezioneSx, params.nomeEccezioneSx, params.valoreEccezioneSx, params.parteEccezioneSx, params.displayNameSx, params.codiceEccezioneDx, params.nomeEccezioneDx, params.valoreEccezioneDx, params.parteEccezioneDx, params.displayNameDx, params.scanId ]);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Aggiorna gli interni di una scansione
 */
const updateInterniScansione = async (params) => {
    let connection;
    try {
        connection = await getConnection();

        const query = "UPDATE TB_CNF_Scansioni SET interno_pagina_sx = ?, display_name_sx = ?, interno_pagina_dx = ?, display_name_dx = ? WHERE id = ?";
        let [ rows ] = await connection.execute(query, [ params.internoSx, params.displayNameSx, params.internoDx, params.displayNameDx, params.scanId ]);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Ottieni note di una scansione
 */
const getNoteScansione = async (params) => {
    let connection;
    try {
        connection = await getConnection();

        const query = "SELECT testo_nota FROM TB_CNF_Scansioni WHERE id = ?";
        let [ rows ] = await connection.execute(query, [ params.id ]);

        return rows[0].testo_nota;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Aggiorna le note di una scansione
 */
const updateNoteScansione = async (params) => {
    let connection;
    try {
        connection = await getConnection();

        const query = "UPDATE TB_CNF_Scansioni SET testo_nota = ? WHERE id = ?";
        let [ rows ] = await connection.execute(query, [ params.notes, params.id ]);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Aggiorna le note di un job
 */
const updateNoteJob = async (params) => {
    let connection;
    try {
        connection = await getConnection();

        const query = "UPDATE TB_DIGIT_Acquisizioni SET " + params.column + " = ? WHERE iddigit = ?";
        let [ rows ] = await connection.execute(query, [ params.value, params.idDigit ]);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Diminuisce di uno i sequenziali delle scansioni a partire da quella da cui è stato premuto il pulsante "Remove"
 */
const decreaseSeqScansioni = async (params) => {
    let connection;
    try {
        connection = await getConnection();

        const query = "UPDATE TB_CNF_Scansioni SET sequenziale_scansione = sequenziale_scansione - ? WHERE id_digit = ? AND sequenziale_scansione > ?";
        let [ rows ] = await connection.execute(query, [ params.step, params.idDigit, params.sequenzialeScansione ]);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Aumenta di uno i sequenziali delle scansioni a partire da quella da cui è stato premuto il pulsante "Insert"
 */
const increaseSeqScansioni = async (params) => {
    let connection;
    try {
        connection = await getConnection();

        const query = "UPDATE TB_CNF_Scansioni SET sequenziale_scansione = sequenziale_scansione + ? WHERE id_digit = ? AND sequenziale_scansione > ?";
        let [ rows ] = await connection.execute(query, [ params.step, params.idDigit, params.sequenzialeScansione ]);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Muove il sequenziale delle scansioni selezionate di 1 sopra o sotto
 */
const moveSeqScansioni = async (params) => {
    let connection;
    try {
        connection = await getConnection();

        let query, scanIdsToAdjust;

        
        if(params.direction == 'up'){
            /* Get Ids of scans to update afterwards */
            query = "SELECT id FROM TB_CNF_Scansioni WHERE id_digit = ? AND sequenziale_scansione < ? ORDER BY sequenziale_scansione DESC LIMIT ?";
            [ scanIdsToAdjust ] = await connection.execute(query, [ params.idDigit, Math.min(...params.sequenzialiScansioni),  params.sequenzialiScansioni.length]);

            /* Update sequenziali for selected scans */
            query = "UPDATE TB_CNF_Scansioni SET sequenziale_scansione = sequenziale_scansione - 1 WHERE id_digit = ? AND id IN ("+ params.idScansioni.join(',') +")";
            await connection.execute(query, [ params.idDigit ]);

            /* Update sequenziali for scans to adjust */
            query = "UPDATE TB_CNF_Scansioni SET sequenziale_scansione = sequenziale_scansione + ? WHERE id_digit = ? AND id IN (?)";
            await connection.execute(query, [ params.sequenzialiScansioni.length, params.idDigit,  scanIdsToAdjust.map(scan => scan.id).join(',')]);
        }
        else {
            query = "SELECT id FROM TB_CNF_Scansioni WHERE id_digit = ? AND sequenziale_scansione > ? ORDER BY sequenziale_scansione ASC LIMIT ?";
            [ scanIdsToAdjust ] = await connection.execute(query, [ params.idDigit, Math.max(...params.sequenzialiScansioni),  params.sequenzialiScansioni.length]);

            /* Update sequenziali for selected scans */
            query = "UPDATE TB_CNF_Scansioni SET sequenziale_scansione = sequenziale_scansione + 1 WHERE id_digit = ? AND id IN ("+ params.idScansioni.join(',') +")";
            await connection.execute(query, [ params.idDigit ]);

            /* Update sequenziali for scans to adjust */
            query = "UPDATE TB_CNF_Scansioni SET sequenziale_scansione = sequenziale_scansione - ? WHERE id_digit = ? AND id IN (?)";
            await connection.execute(query, [ params.sequenzialiScansioni.length, params.idDigit,  scanIdsToAdjust.map(scan => scan.id).join(',')]);
        }
    
        return;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Get a specific value from LOV table
 */
const getLov = async (params) => {
    let connection;
    try {
        connection = await getConnection();

        let query = "SELECT * FROM TB_CNF_Prisma_Lov WHERE name = ? AND is_active = true";
        let [ rows ] = await connection.execute(query, [ params.name ]);

        return rows[0]?.value;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Logga a DB
 */
const log = async (params) => {
    let connection;
    try {
        connection = await getConnection();

        /* Get value of log level from LOV */
        let query = "SELECT value, user FROM TB_CNF_Prisma_Lov WHERE name = ? AND (user = ? OR user IS NULL) AND is_active = true";
        let [ rows ] = await connection.execute(query, [ 'log_level', logParams.user ]);

        /* Return if no rows are found in Lov table */
        if(rows.length == 0) {
            return;
        }

        /* If a record for the current user exists, use that, otherwise use the default global record */
        const specificRecordForUser = rows.find((record) => record.user != null && record.user != undefined);
        const dbRecord = specificRecordForUser ? specificRecordForUser : rows.find((record) => record.user == null);

        let shouldLog = true;
        if(dbRecord.value == 'ERROR' && params.level != 'ERROR') shouldLog = false;
        if(dbRecord.value == 'WARNING' && params.level != 'ERROR' && params.level != 'WARNING') shouldLog = false;
        if(dbRecord.value == 'WORKER' && params.level != 'ERROR' && params.level != 'WARNING' && params.level != 'WORKER') shouldLog = false;
        if(dbRecord.value == 'INFO' && params.level != 'ERROR' && params.level != 'WARNING' && params.level != 'INFO') shouldLog = false;
        if(dbRecord.value == 'DEBUG' && params.level != 'ERROR' && params.level != 'WARNING' && params.level != 'INFO' && params.level != 'DEBUG') shouldLog = false;
        if(params.level == 'EVENT') shouldLog = true;

        if(!shouldLog) {
            return;
        }

        if(params.eventType){
            query = "INSERT INTO TB_Logs (id_digit, host, scanner_id, user, user_profile, user_company, level, text, event_type) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            [ rows ] = await connection.execute(query, [ logParams.idDigit, logParams.host, logParams.scannerId, logParams.user, logParams.userProfile, logParams.userCompany, params.level, params.text, params.eventType ]);
        } else {
            query = "INSERT INTO TB_Logs (id_digit, host, scanner_id, user, user_profile, user_company, level, text) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            [ rows ] = await connection.execute(query, [ logParams.idDigit, logParams.host, logParams.scannerId, logParams.user, logParams.userProfile, logParams.userCompany, params.level, params.text ]);
        }

        return rows;
    } catch (err) {
        console.log(err);
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * gets logs of level "EVENT" for the job
 */
const getLogs = async (params) => {
    let connection;
    try {
        connection = await getConnection();

        const query = "SELECT * FROM TB_Logs WHERE id_digit = ? and level = 'EVENT'";
        let [ rows ] = await connection.execute(query, [ params.idDigit ]);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Aggiorna una singola colonna (parametrica) della tabella Acquisizioni
 */
const updateAcquisizioniColumn = async (params) => {
    let connection;
    try {
        connection = await getConnection();
        
        const query = "UPDATE TB_DIGIT_Acquisizioni SET "+ params.columnName + " = ? WHERE iddigit = ?";
        const [ rows ] = await connection.execute(query, [ params.value, params.idDigit ]);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Ritorna il valore di una singola colonna (parametrica) della tabella Acquisizioni
 */
const getAcquisizioniValue = async (params) => {
    let connection;
    try {
        connection = await getConnection();
        
        const query = "SELECT "+ params.columnName + " FROM TB_DIGIT_Acquisizioni WHERE iddigit = ?";
        const [ rows ] = await connection.execute(query, [ params.idDigit ]);

        if(!rows[0]) return null;
        return rows[0][params.columnName];
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Ritorna il numero di record per ciascun log di tipo EVENT
 */
const getEventsNumber = async (params) => {
    let connection;
    try {
        connection = await getConnection();
        
        const query = "SELECT event_type, count(*) AS 'event_number' FROM TB_Logs WHERE id_digit = ? AND level = 'EVENT' GROUP BY event_type";
        const [ rows ] = await connection.execute(query, [ params.idDigit ]);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Ritorna la distinct degli users ed i loro profili per i log di tipo EVENT
 */
const getEventsUsers = async (params) => {
    let connection;
    try {
        connection = await getConnection();
        
        const query = "SELECT DISTINCT user, user_profile  FROM TB_Logs WHERE id_digit = ? AND level = 'EVENT'";
        const [ rows ] = await connection.execute(query, [ params.idDigit ]);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}


/**
 * Ritorna la distinct degli scanner e host per i log di tipo EVENT
 */
const getEventsScannersAndHosts = async (params) => {
    let connection;
    try {
        connection = await getConnection();
        
        const query = "SELECT DISTINCT scanner_id, host FROM TB_Logs WHERE id_digit = ? AND level = 'EVENT'";
        const [ rows ] = await connection.execute(query, [ params.idDigit ]);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

/**
 * Aggiorna i campi utili alle statistiche della tabella Acquisizioni
 */
const updateAcquisizioniStats = async (params) => {
    let connection;
    try {
        connection = await getConnection();
        
        const query = "UPDATE TB_DIGIT_Acquisizioni SET numAcq = ?, numSost = ?, numRin = ?, numCanc = ?, numFile = ?, lista_operatori = ?, lista_tutor = ?, lista_supertutor = ?, lista_scannerID = ?, lista_hostPC = ? WHERE iddigit = ?";
        const [ rows ] = await connection.execute(query, [ params.acquisitionNumber, params.substitutionNumber, params.renameNumber, params.deletionNumber, params.fileNumber, params.operatoriList, params.tutorList, params.supertutorList, params.scannerList, params.hostList, params.idDigit ]);

        return rows;
    } catch (err) {
        await log({level: 'ERROR', text: err.stack});
    } finally {
        if(connection) await connection.end();
    }
}

module.exports = {
    'db-get-host-info': getHostInfo,
    'db-get-jobs': getJobs,
    'db-get-configs-for-digit': getConfigsForDigit,
    'db-save-configs-for-digit': saveConfigsForDigit,
    'db-save-crop-area-dimensions': saveCropAreaDimensions,
    'db-save-crop-area-risguardia': saveCropAreaRisguardia,
    'db-save-crop-area-position': saveCropAreaPosition,
    'db-get-config-templates': getConfigTemplates,
    'db-save-config-templates': saveConfigTemplate,
    'db-set-status-digit': setDigitStatus,
    'db-get-ordinatori': getOrdinatori,
    'db-get-eccezioni': getEccezioni,
    'db-get-scansioni': getScansioni,
    'db-add-scansione': addScansione,
    'db-delete-scansione': deleteScansione,
    'db-update-scansione': updateScansione,
    'db-update-dimensioni-scansione': updateDimensioniScansione,
    'db-update-pagina-scansione': updatePaginaScansione,
    'db-update-ordinatore-scansione': updateOrdinatoreScansione,
    'db-update-eccezione-scansione': updateEccezioneScansione,
    'db-update-interni-scansione': updateInterniScansione,
    'db-get-note-scansione': getNoteScansione,
    'db-update-note-scansione': updateNoteScansione,
    'db-update-note-job': updateNoteJob,
    'db-decrease-seq-scansioni': decreaseSeqScansioni,
    'db-increase-seq-scansioni': increaseSeqScansioni,
    'db-move-seq-scansioni': moveSeqScansioni,
    'db-get-lov': getLov,
    'db-log': log,
    'db-get-logs': getLogs,
    'db-update-acquisizioni-column': updateAcquisizioniColumn,
    'db-get-acquisizioni-value': getAcquisizioniValue,
    'db-get-events-number': getEventsNumber,
    'db-get-events-users': getEventsUsers,
    'db-get-events-scanners-and-hosts': getEventsScannersAndHosts,
    'db-update-acquisizioni-stats': updateAcquisizioniStats
}
