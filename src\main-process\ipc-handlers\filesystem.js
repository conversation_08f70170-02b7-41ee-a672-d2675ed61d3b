const fs = require('fs-extra')
const path = require('node:path');
const database = require("./database");

const joinPath = async (paramsArr) => {
    try {
        return path.join.apply(null, paramsArr);
    } catch (err) {
        await database['db-log'].call(this, {level: 'ERROR', text: err.stack});
    }
}

const ensureDir = async (path) => {
    try {
        await fs.ensureDir(path);
    } catch (err) {
        await database['db-log'].call(this, {level: 'ERROR', text: err.stack});
    }
} 

const ensureFile = async (path) => {
    try {
        await fs.ensureFile(path);
    } catch (err) {
        await database['db-log'].call(this, {level: 'ERROR', text: err.stack});
    }
} 

const pathExists = async (path) => {
    try {
        return await fs.pathExists(path);
    } catch (err) {
        await database['db-log'].call(this, {level: 'ERROR', text: err.stack});
    }
}

const copyFileFromPublic = async (params) => {
    try {
        const sourcePath = path.join(__dirname, '..', '..', '..', 'public', params.source);
    
        await fs.copy(sourcePath, params.destination);
    } catch (err) {
        await database['db-log'].call(this, {level: 'ERROR', text: err.stack});
    }
}

const copyFileOrDir = async (params) => {
    try {    
        await fs.copy(params.source, params.destination);
    } catch (err) {
        await database['db-log'].call(this, {level: 'ERROR', text: err.stack});
    }
}

const deleteFileOrDir = async (path) => {
    try {
        await fs.remove(path);
    } catch (err) {
        await database['db-log'].call(this, {level: 'ERROR', text: err.stack});
    }
}

const readFile = async (path) => {
    try {
        return await fs.readFile(path);
    } catch (err) {
        await database['db-log'].call(this, {level: 'ERROR', text: err.stack});
    }
}

const writeFile = async (params) => {
    try {
        if(params.mode == 'write') {
            await fs.writeFile(params.path, params.text + '\n');
        } else if (params.mode == 'append') {
            await fs.appendFile(params.path, params.text + '\n');
        }
    } catch (err) {
        await database['db-log'].call(this, {level: 'ERROR', text: err.stack});
    }
}

const renameFileOrDir = async (params) => {
    try {
        /* check that source file or dir exists before renamnig */
        const fileOrDirExists = await fs.pathExists(params.oldPath);

        if(fileOrDirExists)
            await fs.rename(params.oldPath, params.newPath);
    } catch (err) {
        await database['db-log'].call(this, {level: 'ERROR', text: err.stack});
    }
}

const getDirFiles = async (params) => {
    try {
        const dirents = await fs.readdir(params.path, { withFileTypes: true });
        if(params.fileExtension)
            return dirents.filter(dirent => dirent.isFile() && path.extname(dirent.name) == params.fileExtension).map(dirent => dirent.name);
        else
            return dirents.filter(dirent => dirent.isFile()).map(dirent => dirent.name);
    } catch (err) {
        await database['db-log'].call(this, {level: 'ERROR', text: err.stack});
    }
}

const checkJobPreviews = async (params) => {
    try {
        /* get Tif list from scan folder */
        const tifDirents = await fs.readdir(params.scan_path, { withFileTypes: true });
        const tifNamesList = tifDirents.filter(dirent => dirent.isFile() && path.extname(dirent.name) == '.tif').map(dirent => dirent.name);

        /* Get dzi list from preview folder */
        const dziDirents = await fs.readdir(params.preview_path, { withFileTypes: true });
        const dziNamesList = dziDirents.filter(dirent => dirent.isFile() && path.extname(dirent.name) == '.dzi').map(dirent => dirent.name);

        let resultSet = new Set();
        for (const tifName of tifNamesList) {
            const tifFullPath = path.join(params.scan_path, tifName);
            const tifStats = await fs.stat(tifFullPath);
            const tifLastModifiedDate = tifStats.mtime;

            const dziName = dziNamesList.find(dziName => dziName.slice(0, -4) == tifName.slice(0, -4));
            if(dziName) {
                const dziFullPath = path.join(params.preview_path, dziName);
                const dziStats = await fs.stat(dziFullPath);
                const dziLastModifiedDate = dziStats.mtime;

                if(tifLastModifiedDate > dziLastModifiedDate)
                    resultSet.add(tifName); /* Tif has been modified after the preview creation date */
            } else resultSet.add(tifName); /* No preview found */
        }
        return resultSet;
    } catch (err) {
        await database['db-log'].call(this, {level: 'ERROR', text: err.stack});
    }
}

module.exports = {
    'fs-join-path': joinPath,
    'fs-ensure-dir': ensureDir,
    'fs-ensure-file': ensureFile,
    'fs-path-exists': pathExists,
    'fs-copy-file-from-public': copyFileFromPublic,
    'fs-copy-file-or-dir': copyFileOrDir,
    'fs-delete-file-or-dir': deleteFileOrDir,
    'fs-read-file': readFile,
    'fs-write-file': writeFile,
    'fs-rename-file-or-dir': renameFileOrDir,
    'fs-get-dir-files': getDirFiles,
    'fs-check-job-previews': checkJobPreviews
}