const { app } = require('electron/main');
const net = require('node:net');
const sharp = require ('./sharp');
const utility = require('../utilities/utility');
const { MSD_SOCKET_HOST, MSD_SOCKET_PORT, MSD_COMMAND_TIMEOUT,  MSD_COMMAND_TIMEOUT_DEV } = require('../config');

let client;
let response;

const connect = () => {
  client = new net.Socket();
  response = {};

  return new Promise((resolve, reject) => {
    client.connect({
      host: MSD_SOCKET_HOST,
      port: MSD_SOCKET_PORT
    });

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onConnectListener.removeAllListeners('connect');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);

    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);

      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onConnectListener.removeAllListeners('connect');
      return resolve(response);
    });

    /* Success Handling */
    const onConnectListener = client.on('connect', function ()
    {
      clearTimeout(timeoutId);

      response.result = "OK";
      onConnectListener.removeAllListeners('connect');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const disconnect = () => {
  response = {};
  const buffer = utility.newBufferWithCommand(00);

  return new Promise((resolve, reject) => {
    if(!client) return resolve(response);
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);

      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const getStatus = () => {
  response = {};
  const codeMap = {
    0: "Ready",
    1: "Busy in menu",
    2: "Acquiring",
    3: "Busy executing previous command",
    4: "Error"
  }
  const buffer = utility.newBufferWithCommand(01);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);

      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      const statusCode = data.readUInt32BE(8);
      response.data = codeMap[statusCode];
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const getScannerModel = () => {
  response = {};
  const buffer = utility.newBufferWithCommand(02);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      response.data = utility.readTextFromBuffer(data);
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const getSoftwareVersion = () => {
  response = {};
  const buffer = utility.newBufferWithCommand(03);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      response.data = utility.readTextFromBuffer(data);
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const getSensorSize = () => {
  response = {};
  const buffer = utility.newBufferWithCommand(04);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      response.data = data.readUInt32BE(8);
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const getAvailableOpticalResolutions = () => {
  response = {};
  const buffer = utility.newBufferWithCommand(05);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";

      const numOfResolutions = data.readUInt32BE(8);

      response.data = {};
      response.data.selectedResolutionIndex = data.readUInt32BE(12);
      response.data.availableResolutions = [];

      for (let i = 0; i < numOfResolutions; i++) {
        const resolution = data.readDoubleBE(16+(i*8));
        response.data.availableResolutions.push(resolution);
      }

      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const setOpticalResolution = (params) => {
  response = {};
  const buffer = utility.newBufferWithCommand(06);
  buffer.writeUint32BE(params.resolutionIndex, 8);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const setOutputResolutionPercentage = (params) => {
  response = {};
  const buffer = utility.newBufferWithCommand(07);
  buffer.writeDoubleBE(params.resolutionPercentage, 8);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const setPrecision = (params) => {
  response = {};
  const buffer = utility.newBufferWithCommand(08);
  buffer.writeUint32BE(params.precision, 8);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const setFlipScan = (params) => {
  response = {};
  const buffer = utility.newBufferWithCommand(09);
  buffer.writeUint32BE(params.rotation, 8); 

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const setColorMode = (params) => {
  response = {};
  const buffer = utility.newBufferWithCommand(10);
  buffer.writeUint32BE(params.colorMode, 8);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const getAvailableSaveFileFormats = () => {
  response = {};
  const buffer = utility.newBufferWithCommand(11);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";

      const numOfSaveFileFormats = data.readUInt32BE(8);

      response.data = {};
      response.data.selectedSaveFileFormatIndex = data.readUInt32BE(12);
      response.data.compressionLevel = data.readUInt32BE(16);
      response.data.availableSaveFileFormats = [];

      for (let i = 0; i < numOfSaveFileFormats; i++) {
        const saveFileFormat = data.toString('utf8', 20+(i*4), 24+(i*4)); //da testare bene gli indici
        response.data.availableSaveFileFormats.push(saveFileFormat);
      }

      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const setSaveFileFormat = (params) => {
  response = {};
  const buffer = utility.newBufferWithCommand(12);
  buffer.writeUint32BE(params.saveFileFormatIndex, 8);
  buffer.writeUint32BE(params.compressionLevel, 12);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const setUmSharpening = (params) => {
  response = {};
  const buffer = utility.newBufferWithCommand(13);
  buffer.writeUint32BE(params.isEnabled, 8);
  buffer.writeUint32BE(params.isColorRGB, 12);
  buffer.writeDoubleBE(params.intensity, 16);
  buffer.writeDoubleBE(params.radius, 24);
  buffer.writeUint32BE(params.noiseLimit, 32);
  buffer.writeDoubleBE(params.HvsVGain, 36);
  buffer.writeUint32BE(params.isNoiseReduct, 44);
  buffer.writeUint32BE(params.NRIntensity, 48);
  buffer.writeUint32BE(params.isReduceChDiff, 52);
  buffer.writeUint32BE(params.CDIntensity, 56);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const getAvailableSetLists = () => {
  response = {};
  const buffer = utility.newBufferWithCommand(14);
  let nextStringLengthInBytes;
  let numOfListSets;
  let bufferOffset = 267;

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', async function (data)
    {
        /* KO if App_id is not MSD */
        if(!utility.appIdCheck(data)){
          response.result = "KO";
          response.data = 'APP_ID not recognized. Expected "MSD"';
          return resolve(response);
        }

        /* KO if NACK was received */
        if(!utility.NACKCheck(data)){
          response.result = "KO";
          response.data = 'Received NACK';
          return resolve(response);
        }

        numOfListSets = data.readUInt32BE(8);

        response.data = {};
        response.data.selectedListSetIndex = data.readUInt32BE(12);
        response.data.availableListSets = [];

        while(numOfListSets > 0){
          // Read next string length and increase the offset to point at the beginning of the string
          nextStringLengthInBytes = data.readUInt32BE(bufferOffset);
          bufferOffset = bufferOffset + 4;

          // Read the string and increase the offset to point at the next UInt32
          response.data.availableListSets.push(data.toString('utf8', bufferOffset, bufferOffset + nextStringLengthInBytes));
          bufferOffset = bufferOffset + nextStringLengthInBytes;

          numOfListSets--;
        }

        response.result = "OK";
        onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
        return resolve(response);
    });
  });
}

const setSetList = (params) => {
  response = {};
  const buffer = utility.newBufferWithCommand(15);
  buffer.writeUint32BE(params.setListIndex, 8);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const getPreviewInfo = () => {
  response = {};
  const buffer = utility.newBufferWithCommand(16);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";

      response.data = {};
      response.data.fullResWidth = data.readUInt32BE(8);
      response.data.fullResHeight = data.readUInt32BE(12);
      response.data.previewWidth = data.readUInt32BE(16);
      response.data.previewHeight = data.readUInt32BE(20);
      response.data.previewPpi = data.readDoubleBE(24);

      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const getPrescanImageData = () => {
  response = {};
  const buffer = utility.newBufferWithCommand(18);
  let isFirstPacket = true;
  let concatArray = [];
  let concatLength = 0;
  let previewWidth;
  let previewHeight;
  let totalImageSize;
  let resultBuffer = Buffer.alloc(0);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', async function (data)
    {
      if(isFirstPacket){
        /* KO if App_id is not MSD */
        if(!utility.appIdCheck(data)){
          response.result = "KO";
          response.data = 'APP_ID not recognized. Expected "MSD"';
          return resolve(response);
        }

        /* KO if NACK was received */
        if(!utility.NACKCheck(data)){
          response.result = "KO";
          response.data = 'Received NACK';
          return resolve(response);
        }

        previewWidth = data.readUInt32BE(8);
        previewHeight = data.readUInt32BE(12);
        totalImageSize = data.readUInt32BE(263);
        isFirstPacket = false;
      }
      else {
        concatArray.push(data);
        concatLength += data.length;

        //check if is last packet
        if(concatLength >= totalImageSize) {
          response.result = "OK";
          resultBuffer = Buffer.concat(concatArray);
          response.data = await sharp.buildPrescanImage(resultBuffer, previewWidth, previewHeight);
          onDataListener.removeAllListeners('data');
          onErrorListener.removeAllListeners('error');
          return resolve(response);
        }
      }
    });
  });
}

const setCropsFilenamePaths = (params) => {
  response = {};
  let bufferOffset = 8;
  let pathByteSize, totalExtraPacketSize = 0;
  let pathsConcat = '';

  const buffer = utility.newBufferWithCommand(19);
  
  buffer.writeUint8(params.cropAreas.length, bufferOffset);
  bufferOffset += 1;

  params.cropAreas.forEach(cropArea => {
    buffer.writeUint16BE(cropArea.topLeftX, bufferOffset);
    bufferOffset += 2;
    buffer.writeUint16BE(cropArea.topLeftY, bufferOffset);
    bufferOffset += 2;
    buffer.writeUint16BE(cropArea.bottomRightX, bufferOffset);
    bufferOffset += 2;
    buffer.writeUint16BE(cropArea.bottomRightY, bufferOffset);
    bufferOffset += 2;

    pathByteSize = Buffer.byteLength(cropArea.path);
    totalExtraPacketSize += pathByteSize;
    pathsConcat += cropArea.path;

    buffer.writeUint16BE(pathByteSize, bufferOffset);
    bufferOffset += 2;
  });

  buffer.writeUInt32BE(totalExtraPacketSize, 263);

  const bufferExtra = Buffer.alloc(totalExtraPacketSize, 0, 'binary');
  bufferExtra.write(pathsConcat, 0, 'binary');

  return new Promise((resolve, reject) => {
    client.write(buffer);
    client.write(bufferExtra);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const startPrescan = () => {
  response = {};
  const buffer = utility.newBufferWithCommand(20);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const setPrescanCropArea = (params) => {
  response = {};

  const buffer = utility.newBufferWithCommand(21);
  
  buffer.writeUint16BE(params.topLeftX, 8);
  buffer.writeUint16BE(params.topLeftY, 10);
  buffer.writeUint16BE(params.bottomRightX, 12);
  buffer.writeUint16BE(params.bottomRightY, 14);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const startScan = () => {
  response = {};
  const buffer = utility.newBufferWithCommand(22);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const stopAcquisition = (params) => {
  response = {};

  const buffer = utility.newBufferWithCommand(24);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const getScanProgress = () => {
  response = {};
  const buffer = utility.newBufferWithCommand(25);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      response.data = data.readUInt32BE(8);
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const getAcquisitionProgress = () => {
  const map = {
    0: 'Undefined',
    1: 'Initializing',
    2: 'Waiting for pressure/pedal',
    3: 'Acquiring',
    4: 'Processing',
    5: 'Saving',
    6: 'Last scan completed',
    7: 'Last scan interrupted by user',
    8: 'Last scan interrupted by error'
  }
  response = {};
  const buffer = utility.newBufferWithCommand(26);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      response.data = map[data.readUInt32BE(8)];
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const getLinearizationInfo = () => {
  response = {};
  const buffer = utility.newBufferWithCommand(27);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";

      const numOfLinearizationPoints = data.readUInt32BE(8);

      response.data = {};
      response.data.linearizationChartName = data.toString('utf8', 12, 28).replace("\u0000", "");
      response.data.greyLevels = [];

      for (let i = 0; i < numOfLinearizationPoints; i++) {
        const greyLevel = data.readUInt8(76+i);
        response.data.greyLevels.push(greyLevel);
      }

      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const executeLinearization = (params) => {
  response = {};
  let bufferOffset = 8;

  const buffer = utility.newBufferWithCommand(28);
  
  buffer.writeUint16BE(params.colorPatches.length, bufferOffset);
  bufferOffset += 2;
  buffer.writeUint16BE(params.windowSize, bufferOffset);
  bufferOffset += 2;

  params.colorPatches.forEach(colorPatch => {
    buffer.writeUint16BE(colorPatch.coordinates.x, bufferOffset);
    bufferOffset += 2;
    buffer.writeUint16BE(colorPatch.coordinates.y, bufferOffset);
    bufferOffset += 2;
  });

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const setExposureCompensation = (params) => {
  response = {};
  const buffer = utility.newBufferWithCommand(29);
  buffer.writeInt32BE(params.exposureCompensation, 8);

  return new Promise((resolve, reject) => {
    client.write(buffer);
  
    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);

    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const getLightSchematics = () => {
  response = {};
  const buffer = utility.newBufferWithCommand(30);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";

      response.data = {
        defaultValues: {},
        currentValues: {}
      };

      /* Default values */
      response.data.defaultValues.light1 = data.readUInt8(8);
      response.data.defaultValues.light2 = data.readUInt8(9);
      response.data.defaultValues.light3 = data.readUInt8(10);
      response.data.defaultValues.light4 = data.readUInt8(11);

      /* Current values */
      response.data.currentValues.light1 = data.readUInt8(12);
      response.data.currentValues.light2 = data.readUInt8(13);
      response.data.currentValues.light3 = data.readUInt8(14);
      response.data.currentValues.light4 = data.readUInt8(15);

      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const setLightSchematics = (params) => {
  response = {};
  const buffer = utility.newBufferWithCommand(31);
  buffer.writeUInt8(params.light1, 8);
  buffer.writeUInt8(params.light2, 9);
  buffer.writeUInt8(params.light3, 10);
  buffer.writeUInt8(params.light4, 11);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const getCurrentIccProfileSelection = (params) => {
  response = {};
  let nextStringLengthInBytes;
  let numOfIccProfiles;
  let bufferOffset = 267;

  const buffer = utility.newBufferWithCommand(32);
  buffer.writeUInt8(params.iccProfileType, 8);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', async function (data)
    {
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      numOfIccProfiles = data.readUInt32BE(8);

      response.data = {};
      response.data.selectedIccProfile = data.readUInt32BE(12);
      response.data.iccProfiles = [];

      while(numOfIccProfiles > 0){
        // Read next string length and increase the offset to point at the beginning of the string
        nextStringLengthInBytes = data.readUInt32BE(bufferOffset);
        bufferOffset = bufferOffset + 4;

        // Read the string and increase the offset to point at the next UInt32
        response.data.iccProfiles.push(data.toString('utf8', bufferOffset, bufferOffset + nextStringLengthInBytes));
        bufferOffset = bufferOffset + nextStringLengthInBytes;

        numOfIccProfiles--;
      }
      
      response.result = "OK";
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const setIccProfile = (params) => {
  response = {};
  const buffer = utility.newBufferWithCommand(33);
  buffer.writeUInt8(params.iccProfileType, 8);
  buffer.writeUInt8(params.iccProfileIndex, 9);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const getBookCradleParametersAndStatus = () => {
  response = {};
  const buffer = utility.newBufferWithCommand(34);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";

      response.data = {};
      response.data.pressureKg = data.readDoubleBE(8);
      response.data.enablePlates = data.readUInt8(16);
      response.data.enableBalance = data.readUInt8(17);
      response.data.balanceFriction = data.readUInt8(18);
      response.data.autoCalcThickRatio = data.readUInt8(19);
      response.data.thickWeightRatio = data.readUInt8(20);
      response.data.autoOpenAfterScan = data.readUInt8(21);
      response.data.autoCloseOnScan = data.readUInt8(22);
      response.data.waitForScanPedal = data.readUInt8(23);
      response.data.waitForPressure = data.readUInt8(24);
      response.data.autoOpenClose = data.readUInt8(25);
      response.data.autoOpenMmDown = data.readUInt16BE(26);
      response.data.lockGlassMove = data.readUInt8(28);
      response.data.openAmountPercentage = data.readUInt8(29);
      response.data.openGlassSpeed = data.readUInt16BE(30);
      response.data.closeGlassSpeed = data.readUInt16BE(32);
      response.data.glassAcceleration = data.readDoubleBE(34);
      response.data.closingGlassPosition = data.readUInt16BE(42);
      response.data.workWithContact = data.readUInt8(44);
      response.data.contactDistance = data.readUInt8(45);
      response.data.enableFinePressure = data.readUInt8(46);
      response.data.pressureAccuracy = data.readUInt8(47);
      response.data.upSpeed = data.readUInt8(48);
      response.data.downSpeed = data.readUInt8(49);
      response.data.stopScannerPressure = data.readUInt16BE(50);

      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const setBookCradleParameters = (params) => {
  response = {};
  const buffer = utility.newBufferWithCommand(35);

  buffer.writeDoubleBE(params.pressureKg, 8);
  buffer.writeUInt8(params.enablePlates, 16);
  buffer.writeUInt8(params.enableBalance, 17);
  buffer.writeUInt8(params.balanceFriction, 18);
  buffer.writeUInt8(params.autoCalcThickRatio, 19);
  buffer.writeUInt8(params.thickWeightRatio, 20);
  buffer.writeUInt8(params.autoOpenAfterScan, 21);
  buffer.writeUInt8(params.autoCloseOnScan, 22);
  buffer.writeUInt8(params.waitForScanPedal, 23);
  buffer.writeUInt8(params.waitForPressure, 24);
  buffer.writeUInt8(params.autoOpenClose, 25);
  buffer.writeUInt16BE(params.autoOpenMmDown, 26);
  buffer.writeUInt8(params.lockGlassMove, 28);
  buffer.writeUInt8(params.openAmountPercentage, 29);
  buffer.writeUInt16BE(params.openGlassSpeed, 30);
  buffer.writeUInt16BE(params.closeGlassSpeed, 32);
  buffer.writeDoubleBE(params.glassAcceleration, 34);
  buffer.writeUInt16BE(params.closingGlassPosition, 42);
  buffer.writeUInt8(params.workWithContact, 44);
  buffer.writeUInt8(params.contactDistance, 45);
  buffer.writeUInt8(params.enableFinePressure, 46);
  buffer.writeUInt8(params.pressureAccuracy, 47);
  buffer.writeUInt8(params.upSpeed, 48);
  buffer.writeUInt8(params.downSpeed, 49);
  buffer.writeUInt16BE(params.stopScannerPressure, 50);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const getBookCradleContactParameters = () => {
  response = {};
  const buffer = utility.newBufferWithCommand(36);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";

      response.data = {};
      response.data.thickness = data.readUInt16BE(8);
      response.data.lightDistance = data.readUInt16BE(10);

      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const setBookCradleContactParameters = (params) => {
  response = {};
  const buffer = utility.newBufferWithCommand(37);

  buffer.writeUInt16BE(params.thickness, 8);
  buffer.writeUInt16BE(params.lightDistance, 10);

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

const setTiffMetadata = (params) => {
  response = {};
  let bufferOffset = 8;

  const buffer = utility.newBufferWithCommand(38);

  /* Image description */
  buffer.writeUInt32BE(params.imageDescription.length, bufferOffset);
  bufferOffset += 4;

  buffer.write(params.imageDescription, bufferOffset, 'binary');
  bufferOffset += params.imageDescription.length;

  /* Author */
  buffer.writeUInt32BE(params.author.length, bufferOffset);
  bufferOffset += 4;

  buffer.write(params.author, bufferOffset, 'binary');
  bufferOffset += params.author.length;

  /* Host PC */
  buffer.writeUInt32BE(params.hostPC.length, bufferOffset);
  bufferOffset += 4;

  buffer.write(params.hostPC, bufferOffset, 'binary');
  bufferOffset += params.hostPC.length;

  /* Copyright */
  buffer.writeUInt32BE(params.copyright.length, bufferOffset);
  bufferOffset += 4;

  buffer.write(params.copyright, bufferOffset, 'binary');

  return new Promise((resolve, reject) => {
    client.write(buffer);

    const timeoutId = setTimeout(() => {
      if(!response.result){
        response.result = "KO";
        response.data = "Timeout (10s) reached";
        onErrorListener.removeAllListeners('error');
        onDataListener.removeAllListeners('data');
        return resolve(response);
      }
    }, app.isPackaged ? MSD_COMMAND_TIMEOUT : MSD_COMMAND_TIMEOUT_DEV);
  
    /* Error handling */
    const onErrorListener = client.on('error', function (data)
    {
      clearTimeout(timeoutId);
      
      response.result = "KO";
      response.data = data;
      onErrorListener.removeAllListeners('error');
      onDataListener.removeAllListeners('data');
      return resolve(response);
    });

    /* Success handling */
    const onDataListener = client.on('data', function (data)
    {
      clearTimeout(timeoutId);
      
      /* KO if App_id is not MSD */
      if(!utility.appIdCheck(data)){
        response.result = "KO";
        response.data = 'APP_ID not recognized. Expected "MSD"';
        return resolve(response);
      }

      /* KO if NACK was received */
      if(!utility.NACKCheck(data)){
        response.result = "KO";
        response.data = 'Received NACK';
        return resolve(response);
      }

      /* Received ACK, can process the response */
      response.result = "OK";
      onDataListener.removeAllListeners('data');
      onErrorListener.removeAllListeners('error');
      return resolve(response);
    });
  });
}

module.exports = {
  'msd-connect': connect,
  'msd-disconnect': disconnect,
  'msd-get-status': getStatus,
  'msd-get-scanner-model': getScannerModel,
  'msd-get-scanner-model': getSoftwareVersion,
  'msd-get-sensor-size': getSensorSize,
  'msd-get-available-optical-resolutions': getAvailableOpticalResolutions,
  'msd-set-optical-resolution': setOpticalResolution,
  'msd-set-output-resolution-percentage': setOutputResolutionPercentage,
  'msd-set-precision': setPrecision,
  'msd-set-flip-scan': setFlipScan,
  'msd-set-color-mode': setColorMode,
  'msd-get-available-save-file-formats': getAvailableSaveFileFormats,
  'msd-set-save-file-format': setSaveFileFormat,
  'msd-set-um-sharpening': setUmSharpening,
  'msd-get-available-set-lists': getAvailableSetLists,
  'msd-set-set-list': setSetList,
  'msd-get-preview-info': getPreviewInfo,
  'msd-get-preview-image-data': getPrescanImageData,
  'msd-set-crops-filename-paths': setCropsFilenamePaths,
  'msd-start-prescan': startPrescan,
  'msd-set-prescan-crop-area': setPrescanCropArea,
  'msd-start-scan': startScan,
  'msd-stop-acquisition': stopAcquisition,
  'msd-get-scan-progress': getScanProgress,
  'msd-get-acquisition-progress': getAcquisitionProgress,
  'msd-get-linearization-info': getLinearizationInfo,
  'msd-execute-linearization': executeLinearization,
  'msd-set-exposure-compensation': setExposureCompensation,
  'msd-get-light-schematics': getLightSchematics,
  'msd-set-light-schematic':setLightSchematics,
  'msd-get-current-icc-profile-selection': getCurrentIccProfileSelection,
  'msd-set-icc-profile': setIccProfile,
  'msd-get-book-cradle-parameters-and-status': getBookCradleParametersAndStatus,
  'msd-set-book-cradle-parameters': setBookCradleParameters,
  'msd-get-book-cradle-contact-parameters': getBookCradleContactParameters,
  'msd-set-book-cradle-contact-parameters': setBookCradleContactParameters,
  'msd-set-tiff-metadata': setTiffMetadata
}