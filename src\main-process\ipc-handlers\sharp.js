const sharp = require("sharp");
const database = require ('./database');
const filesystem = require('./filesystem');

const buildPrescanImage = async (inputBuffer, previewWidth, previewHeight) => {
    let stream;

    try {
        await database['db-log'].call(this, {level: 'DEBUG', text: '[sharp.js][buildPrescanImage] Preview dimensions: ' + previewWidth + 'x' + previewHeight});

        stream = Uint32Array.from(inputBuffer);
        
        let tempArr = new Uint32Array(stream.length);
        let j = 0;

        // Switch Alpha from 1st position to 4th position for each byte
        for (let i = 0; i < stream.length; i = i + 4) {
            tempArr[j] = stream[i+1];
            tempArr[j+1] = stream[i+2];
            tempArr[j+2] = stream[i+3];
            tempArr[j+3] = stream[i];

            j = j + 4;
        }
        stream = tempArr;

        const imageBuffer = await sharp(stream, {
            raw: {
                width: previewWidth,
                height: previewHeight,
                channels: 4
            }
        })
        .removeAlpha()
        .toFormat('jpeg')
        .toBuffer();

        return `data:image/jpeg;base64,${imageBuffer.toString('base64')}`
    } catch (err) {
        await database['db-log'].call(this, {level: 'ERROR', text: err.stack});
    }
};

const buildDzi = async (params) => {
    try {
        const tifExists = await filesystem['fs-path-exists'].call(this, params.inputPath);
        
        if(!tifExists){
            await database['db-log'].call(this, {level: 'WARNING', text: 'File does not exist: ' + params.inputPath});
            return;
        }
        
        await sharp(params.inputPath, {limitInputPixels:false}).jpeg().tile({size: 8192}).toFile(params.outputPath);
    } catch (err) {
        await database['db-log'].call(this, {level: 'ERROR', text: '[sharp.js][buildDzi] ' + err.stack});
    }
}

module.exports = {
    buildPrescanImage,
    'build-dzi': buildDzi
}