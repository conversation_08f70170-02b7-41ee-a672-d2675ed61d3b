const { app } = require('electron/main');
const { logParams, selectedJob } = require ( '../config');

const getAppVariable = (params) => {
    let variableValue;

    switch (params.variableName) {
        case 'appPath':
            variableValue = app.getPath('userData');
            break;
        case 'isPackaged':
            variableValue = app.isPackaged;
            break;
        case 'appVersion':
            variableValue = app.getVersion();
            break;
        default:
            break;
    }

    return variableValue;
}

const configSetLogParams = (params) => {
    logParams.idDigit = params.idDigit;
    logParams.user = params.user;
    logParams.userProfile = params.userProfile;
    logParams.userCompany = params.userCompany;
    logParams.scannerId = params.scannerId;
}

const configSetSelectedJob = (params) => {
    selectedJob.idDigit = params.selectedJob.id_digit;
    selectedJob.statusDigit = params.selectedJob.status_digit;
}

module.exports = {
    'get-app-variable': getAppVariable,
    'config-set-log-params': configSetLogParams,
    'config-set-selected-job': configSetSelectedJob
}