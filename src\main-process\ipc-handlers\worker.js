const { BrowserWindow, utilityProcess } = require('electron/main');
const path = require('node:path');
const database = require('./database');

/* Creates dzi previews of a whole job, for the scan list passed in input */
const createJobPreview = async (params) => {
    const maxParallelWorkerNum = require('os').cpus().length || await database['db-get-lov'].call(this, {name: 'max_parallel_workers'});
    params.parallelWorkerNum = params.scanList.length <= maxParallelWorkerNum ? params.scanList.length : maxParallelWorkerNum;

    let completedWorkerNum = 0;
    for (let i = 0; i < params.parallelWorkerNum; i++) {
        params.workerIndex = i + 1;

        const worker = utilityProcess.fork(path.join(__dirname, '../workers/job-preview-creator.js'))

        /* Log messages coming from worker */
        worker.on('message', async ({action: action, msg: msg}) => {
            if(action == 'LOG')
                await database['db-log'].call(this, {level: 'WORKER', text: msg});
        });

        /* When all workers completed, notify renderer process */
        worker.once('exit', async () => {
            /* Increase the number of workers which completed their task */
            completedWorkerNum ++;

            /* Check if all workers have completed */
            if(completedWorkerNum == params.parallelWorkerNum){
                /* Get renderer window and send notification */
                const clientWin = BrowserWindow.fromId(1);
                clientWin.webContents.send('job-preview-creation-completed');
            }
        });

        /* Start the worker task */
        worker.postMessage({type: 'start', params: params});
    }
}

/* Creates dzi preview of a single scan */
const createScanPreview = async (params) => {
    const worker = utilityProcess.fork(path.join(__dirname, '../workers/scan-preview-creator.js'));

    /* When the worker completed, notify renderer process */
    worker.once('exit', async () => {
        /* Get renderer window and send notification */
        const clientWin = BrowserWindow.fromId(1);
        clientWin.webContents.send('scan-preview-creation-completed');
    });

    /* Start the worker task */
    worker.postMessage({type: 'start', params: params});
}

module.exports = {
    'create-job-preview': createJobPreview,
    'create-scan-preview': createScanPreview
}