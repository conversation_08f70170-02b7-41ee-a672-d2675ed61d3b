exports.newBufferWithCommand = (command) => {
    const buffer = Buffer.alloc(267, 0, 'binary');
    buffer.write('BAV', 0, 'binary');
    buffer.writeUInt32BE(00 + command, 3);
    
    return buffer;
}

exports.appIdCheck = (buffer) => {
    const app_id = buffer.toString('utf8', 0, 3);

    return app_id == 'MSD';
}

exports.NACKCheck = (buffer) => {
    return buffer.readUInt8(7) == 1;
}

exports.readTextFromBuffer = (buffer) => {
    const stringLength = buffer.readUInt8(8);

    const textStartIndex = 9;
    const textEndIndex = 9 + stringLength;

    return buffer.toString('utf8', textStartIndex, textEndIndex);
}

exports.undefinedToNull = (arr) => {
    if(!Array.isArray(arr)) return;
    return arr.map(el => el === undefined ? null : el);
}