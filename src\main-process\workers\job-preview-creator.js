const database = require('../ipc-handlers/database');
const sharp = require('../ipc-handlers/sharp');
const path = require('node:path');

process.parentPort.on('message', async (e) => {
    /* Retrieve params */
    const type = e.data.type;
    const params = e.data.params;

    if(type == 'start')
        await run(params);
});

const run = async (params) => {
    const scanList = params.scanList;
    const scanPath = params.scanPath;
    const previewPath = params.previewPath;
    const parallelWorkerNum = params.parallelWorkerNum;
    const workerIndex = params.workerIndex;

    /* If no scan are found, exit */
    if(scanList.length <= 0 || workerIndex > scanList.length)
        process.exit();

    /** 
     * scanList.length      123
     * parallelWorkerNum    4
     * ..............................
     * 
     * chunkStart   0                    0 --> workerIndex == 1 ? 0 : Math.ceil(scanList.length * ((workerIndex-1)/parallelWorkerNum))
     * chunkEnd     floor(123 * 1/4)     30 --> Math.floor(scanList.length * (workerIndex/parallelWorkerNum))
     * 
     * chunkStart   ceil(123 * 1/4)      31
     * chunkEnd     floor(123 * 2/4)     61
     * 
     * chunkStart   ceil(123 * 2/4)      62
     * chunkEnd     floor(123 * 3/4)     92
     * 
     * chunkStart   ceil(123 * 3/4)      93
     * chunkEnd     floor(123 * 4/4)     123
     * 
    */

    /* Calculate chunk indexes for current worker (see formula above) */
    const chuckStartIndex = workerIndex == 1 ? 0 : Math.ceil(scanList.length * ((workerIndex-1)/parallelWorkerNum));
    const chunkEndIndex = Math.floor(scanList.length * (workerIndex/parallelWorkerNum))

    let scanListChunk = [];
    /* Get the chunk of the scanList array this worker has to work on */
    if(chuckStartIndex == chunkEndIndex){
        /* If start and end are the same, slice won't work so get the value directly */
        scanListChunk.push(scanList[chuckStartIndex]);
        process.parentPort.postMessage({action: 'LOG', msg: '[job-preview-creator.js] worker ' + workerIndex + ' (' + process.pid + '): chunk [' + chuckStartIndex + ']'});
    } else{
        scanListChunk = scanList.slice(chuckStartIndex, chunkEndIndex);
        process.parentPort.postMessage({action: 'LOG', msg: '[job-preview-creator.js] worker ' + workerIndex + ' (' + process.pid + '): chunk [' + chuckStartIndex + ', ' + chunkEndIndex + ']'});
    }

    for (const scan of scanListChunk) {
        /* prepare baseFileName with id */
        const baseFileName = scan.id.toString();

        /* Build dzi for left image */
        let sharpParams = {};
        sharpParams.inputPath = path.join(scanPath, baseFileName + '_0.tif');
        sharpParams.outputPath = path.join(previewPath, baseFileName + '_0.dz');
        await sharp['build-dzi'].call(this, sharpParams);

        if(scan.pagina_doppia){
            /* Build dzi for right image */
            sharpParams.inputPath = path.join(scanPath, baseFileName + '_1.tif');
            sharpParams.outputPath = path.join(previewPath, baseFileName + '_1.dz');
            await sharp['build-dzi'].call(this, sharpParams);
        }
    }

    /* kill process once completed */
    process.exit();
}