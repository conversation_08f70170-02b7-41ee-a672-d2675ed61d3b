const sharp = require('../ipc-handlers/sharp');
const path = require('node:path');

process.parentPort.on('message', async (e) => {
    /* Retrieve params */
    const type = e.data.type;
    const params = e.data.params;

    if(type == 'start')
        await run(params);
});

const run = async (params) => {
    const scanId = params.scanId;
    const isDoublePage = params.isDoublePage;
    const scanPath = params.scanPath;
    const previewPath = params.previewPath;

    /* Build dzi for left image */
    let sharpParams = {};
    sharpParams.inputPath = path.join(scanPath, scanId.toString() + '_0.tif');
    sharpParams.outputPath = path.join(previewPath, scanId.toString() + '_0.dz');
    await sharp['build-dzi'].call(this, sharpParams);

    if(isDoublePage){
        /* Build dzi for right image */
        sharpParams.inputPath = path.join(scanPath, scanId.toString() + '_1.tif');
        sharpParams.outputPath = path.join(previewPath, scanId.toString() + '_1.dz');
        await sharp['build-dzi'].call(this, sharpParams);
    }

    /* kill process once completed */
    process.exit();
}