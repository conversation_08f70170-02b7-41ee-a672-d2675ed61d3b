<script>
    import { _isSettingColors, _colorPatches, _mainViewer } from '@store';
    import CircularProgressIndicator from '@components/common/CircularProgressIndicator.svelte'
    import * as d3 from 'd3-selection';
    import { notify } from '@utility';

    //smui components
    import Paper, { Title, Content } from '@smui/paper';
    import Button, { Label } from '@smui/button';

    //icons
    import CheckIcon from 'svelte-material-icons/CheckBold.svelte';
    import InProgressIcon from 'svelte-material-icons/Cached.svelte';

    const getLinearizationInfo = async () => {
        /* NOTE - modified for develop branch */
		/* Get number of patches and expected colors 
		const response = await window.electron.metis("msd-get-linearization-info");
        await window.electron.database('db-log', {level:'DEBUG', text:'[ColorSettingForm.svelte][getLinearizationinfo] number of color patches returned for calibration: ' + response.data.greyLevels.length});
        */
        const fake = [199, 161, 119, 84, 53];
        let colorPatches = [];

        fake.forEach((color, index) => {
            let newColorPatch = {};
            newColorPatch.divId = "color-patch-" + index;
            newColorPatch.color = color;
            newColorPatch.coordinates = null;
            newColorPatch.active = index == 0 ? true : false;

            colorPatches.push(newColorPatch);
        })
        _colorPatches.set(colorPatches);
        document.body.style.cursor="crosshair";
        d3.select("#main-viewer").dispatch('color-linearization-started');
	};

    let isSendingCommand = false;

    const handleConfirm = async () => {
        const params = {
            colorPatches: $_colorPatches,
            windowSize: 15
        };

        await window.electron.database('db-log', {level:'DEBUG', text:'[ColorSettingForm.svelte][handleConfirm] starting color calibration'});
        isSendingCommand = true;
        const response = await window.electron.metis("msd-execute-linearization", params);

        if(response.result != 'OK'){
            notify.error("Si è verificato un errore durante la calibrazione del colore");
            await window.electron.database('db-log', {level:'ERROR', text:'[ColorSettingForm.svelte][handleConfirm] color calibration failed'});
        } else {
            notify.success("Calibrazione del colore completata");
            await window.electron.database('db-log', {level:'INFO', text:'[ColorSettingForm.svelte][handleConfirm] color calibration completed successfully'});
        }

        isSendingCommand = false;
        document.body.style.cursor="auto";
        _isSettingColors.set(false);
    }

    const handleCancel = () => {
        document.body.style.cursor="auto";
        _isSettingColors.set(false);

        /* send command to main viewer to show back overlays */
        d3.select("#main-viewer").dispatch('color-linearization-ended');
    }
</script>

<div class="container">
    <div class="paper-container">
        <Paper elevation=3>
            <Title>Calibrazione del colore</Title>
            <Content>
                {#await getLinearizationInfo() then }
                    <div class="flex-column-container">
                        <p class="content">Selezionare i {$_colorPatches.length} colori corrispondenti sul ColorChecker 24 per procedere</p>
                        <div class="flex-row">
                            {#each $_colorPatches as colorPatch}
                                {#if colorPatch.active}
                                    <div class="flex-inner-column">
                                        <div id={colorPatch.divId} class="color-patch active" style="background-color: rgb({colorPatch.color}, {colorPatch.color}, {colorPatch.color});"/>
                                        <InProgressIcon  width=25 height=25 color="var(--mdc-theme-primary)" />
                                    </div>
                                {:else if colorPatch.coordinates}
                                    <div class="flex-inner-column">
                                        <p class="small">{colorPatch.color}</p>
                                        <div id={colorPatch.divId} class="color-patch success" style="background-color: rgb({colorPatch.color}, {colorPatch.color}, {colorPatch.color});"/>
                                        <CheckIcon  width=25 height=25 color="var(--mdc-theme-success)" />
                                    </div>
                                {:else}
                                    <div id={colorPatch.divId} class="color-patch" style="background-color: rgb({colorPatch.color}, {colorPatch.color}, {colorPatch.color});"/>
                                {/if}
                            {/each}
                        </div>
                        <div class="flex-row confirm-cancel-section">
                            <Button variant="raised" on:click={handleConfirm} disabled={!$_colorPatches.every((colorPatch) => {return colorPatch.coordinates}) || isSendingCommand}>
                                <Label>Conferma</Label>
                            </Button>
                            <Button variant="raised" color="secondary" disabled={isSendingCommand} on:click={handleCancel} >
                                <Label>Annulla</Label>
                            </Button>
                        </div>
                        {#if isSendingCommand}
                            <CircularProgressIndicator loadingText='Linearizzazione colore in corso...'/>
                        {/if}
                    </div>
                {/await}
            </Content>
        </Paper>
    </div>
</div>


<style>
    .container {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 50%;
    }
    
    .flex-column-container {
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        align-items: center;
    }

    .flex-row {
        display: flex;
        justify-content: space-evenly;
        width: 100%;
        gap: 10px;
    }

    .flex-inner-column {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 10px;
    }

    .color-patch {
        width: 80px;
        height: 80px;
    }

    .color-patch.active {
        box-shadow: 0px 0px 10px 5px var(--mdc-theme-primary);
    }

    .color-patch.success {
        box-shadow: 0px 0px 10px 5px var(--mdc-theme-success);
    }

    .flex-row.confirm-cancel-section {
        margin-top: 40px;
    }

    p {
        color: var(--mdc-theme-secondary);
        font-size: medium;
        margin: 0px;
    }

    p.content {
        margin-bottom: 20px;
    }

    * :global(.smui-paper) {
        padding: 20px 40px 40px 40px;
    }
</style>