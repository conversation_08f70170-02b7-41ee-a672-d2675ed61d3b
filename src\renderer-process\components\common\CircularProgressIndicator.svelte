<script>
	//smui
	import CircularProgress from '@smui/circular-progress';

	export let loadingText;
</script>

<div class="flex-container">
    <CircularProgress style="height: 60px; width: 60px;" indeterminate />
	{#if loadingText}
		<p tabindex="0">{loadingText}</p>
	{/if}
</div>

<style>
    .flex-container {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		height: 50%;
    }

	p {
        color: var(--mdc-theme-secondary);
        font-size: medium;
    }
</style>