<script>
    import { onMount } from "svelte";
    import * as d3 from 'd3-selection';

    export let text = '';
    export let id;

    let posX = 0;
    let posY = 0;
    let mouseX = 0;
    let mouseY = 0;
    
    onMount(() => {
        d3.select('#'+ id).style("left", 0 + 'px');
        d3.select('#'+ id).style("top", 0 + 'px');
        d3.select('#'+ id).on('mousedown', mouseDownHandler);
        d3.select('body').on('mouseup', mouseUpHandler);
    });

    const mouseDownHandler = (e) => {
        e.preventDefault();
        const draggableDiv = d3.select('#'+ id).node();

        posX = e.clientX - draggableDiv.offsetLeft;
        posY = e.clientY - draggableDiv.offsetTop;
        d3.select('body').on('mousemove', mouseMoveHandler);
    }

    const mouseUpHandler = () => {
        d3.select('body').on('mousemove', null);
    }

    const mouseMoveHandler = (e) => {
        mouseX = e.clientX - posX;
        mouseY = e.clientY - posY;

        d3.select('#'+ id).style("left", mouseX + 'px');
        d3.select('#'+ id).style("top", mouseY + 'px');
    }
</script>

<div id={id} class="draggable">{text}</div>

<style>
    .draggable {
        position: absolute;
        min-width: 100px;
        height: min-content;
        padding: 5px;
        border-radius: 5px;
        box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.5);
        cursor: move;
        user-select: none;
        background-color: var(--mdc-theme-primary);
        color: white;
        z-index: 999;

        display: flex;
        justify-content: center;
        align-items: center;
    }
</style>