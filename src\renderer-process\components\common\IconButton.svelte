<script>
    //smui components
    import IconButton from '@smui/icon-button';
	import Tooltip, { Wrapper } from '@smui/tooltip';
    import Badge from '@smui-extra/badge';

    export let icon;
    export let iconWidth = 40;
    export let iconHeight = 40;
    export let iconViewbox = "0 0 24 24";
    export let iconColor = "var(--mdc-theme-secondary)";

    export let badgeText = "";

    export let label = "";
    export let tooltip;

    export let disabled = false;
    export let onclick = () => {};
</script>

<Wrapper>
    <div class="button-flex-group">
        <IconButton on:click={onclick} {disabled}>
            <svelte:component this={icon} width={iconWidth} height={iconHeight} viewBox={iconViewbox} color={disabled ? "var(--mdc-theme-disabled)": iconColor}/>
            {#if badgeText}
                <Badge color="secondary" position="inset">{badgeText}</Badge>
            {/if}
        </IconButton>
        {#if label}
            <span class="label">{label}</span>
        {/if}
    </div>
    <Tooltip showDelay=0 hideDelay=0 >{tooltip}</Tooltip>
</Wrapper>

<style>
    .button-flex-group {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 5px;
    }

    .label {
        font-size: small;
        font-weight: bold;
        text-align: center;
        color: var(--mdc-theme-secondary);
    }

    * :global(.mdc-icon-button) {
        padding: 10px 10px 5px 10px;
    }
</style>