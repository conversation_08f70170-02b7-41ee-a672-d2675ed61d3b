<script>
    import { _isAddingJob, _selectedJob } from '@store';

    //smui
    import Dialog, { Title, Content } from '@smui/dialog';
    import Button, { Label } from '@smui/button';
    import Textfield from '@smui/textfield';

    let jobName = '';
    const initJobName = () => {
        jobName = $_selectedJob?.identifier || '';
    }

    const handleConfirm = () => {
        /* TODO - capire come fare per gestire id_digit vuoto */
    }
   
    const handleClose = () => {
        _isAddingJob.set(false);
    }
</script>

<div>
    <Dialog bind:open={$_isAddingJob} scrimClickAction="" on:SMUIDialog:opening={initJobName}>
        <Title>Crea JOB di test</Title>
        <Content>
            <Textfield
                style="width: 300px;"
                bind:value={jobName}
                label="Nome Job"
            />
        </Content>
        <div class="flex-row-buttons">
            <Button on:click={handleConfirm} variant="raised" disabled={jobName == $_selectedJob?.identifier}>
                <Label>Conferma</Label>
            </Button>
            <Button on:click={handleClose} variant="raised" color="secondary">
                <Label>Chiudi</Label>
            </Button>
        </div>
    </Dialog>
</div>

<style>
    .flex-row-buttons {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 10px;
    }

    * :global(.mdc-dialog .mdc-dialog__surface) {
        min-width: 25%;
        min-height: 25%;
    }

    * :global(.mdc-dialog__content) {
        margin-top: 20px;
    }
</style>