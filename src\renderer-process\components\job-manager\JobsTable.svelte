<script>
  import { _user, _selectedJob, _showJobsInRecovery } from '@store';
  import { removeAllSelectedClass } from '@utility';
  import { createEventDispatcher } from 'svelte';

  //smui components
  import DataTable, { Head, Body, Row, Cell } from '@smui/data-table';
  import Tooltip, { Wrapper } from '@smui/tooltip';
  
  //icons
  import StampatoIcon from "svelte-material-icons/Bookshelf.svelte";
  import MssIcon from "svelte-material-icons/BookOpenVariant.svelte";

  export let options;

  const dispatch = createEventDispatcher();

  const handleSelection = (e, option) => {
    removeAllSelectedClass();

    /* Add class to selected element */
    e.target.classList.add('selected');

    /* Emit event to Job Manager */
    dispatch('jobSelected', option);
  }

  const getStatusLabel = (option) => {
    const statusMap = {
      "in pausa": "in pausa",
      "acquisizione": "in lavorazione",
      "acquisito": "acquisito",
      "validazione": "in validazione",
      "validato": "validato",
      "finalizzazione": "in finalizzazione",
      "finalizzato": "finalizzato"
    }

    let statusLabel = statusMap[option.status_digit];

    if ((option.is_in_recovery || option.is_in_rework) && !$_showJobsInRecovery) {
      statusLabel += ' (rifacimento)';
    }

    return statusLabel;
  }
</script>

<div class="table-container">
  <DataTable stickyHeader>
    <Head>
      <Row>
        <Cell><span class='unselectable'>Tipo</span></Cell>
        <Cell><span class='unselectable'>Segnatura</span></Cell>
        {#if $_user?.profile == 'Supertutor'}
          <Cell><span class='unselectable'>Società</span></Cell>
        {/if}
        {#if $_user?.profile == 'Tutor' || $_user?.profile == 'Supertutor'}
          <Cell><span class='unselectable'>Progetto</span></Cell>
        {/if}
        <Cell numeric><span class='unselectable'>Numero Acquisizioni</span></Cell>
        <Cell><span class='unselectable'>Stato Digitalizzazione</span></Cell>
        <Cell>Scanner</Cell>
      </Row>
    </Head>
    <Body>
      {#each options as option (option.id_digit)}
        <Row id={`row-${option?.id_digit}`} on:SMUIDataTableRow:click={(e) => handleSelection(e, option)}>
          <Cell>
            {#if option.is_stampato}
              <Wrapper>
                <StampatoIcon width=30 height=30 color="var(--mdc-theme-secondary)"/>
                <Tooltip showDelay=0 hideDelay=0>Stampato</Tooltip>
              </Wrapper>
            {:else}
              <Wrapper>
                <MssIcon width=30 height=30 color="var(--mdc-theme-secondary)"/>
                <Tooltip showDelay=0 hideDelay=0>Manoscritto</Tooltip>
              </Wrapper>
            {/if}
          </Cell>
          <Cell>
            <Wrapper>
              <span class='unselectable'>{option.is_stampato ? option.identifier + ' (' + option.segnatura + ')' : option.identifier}</span>
              <Tooltip showDelay=0 hideDelay=0>id_digit: {option.id_digit}</Tooltip>
            </Wrapper>
          </Cell>
          {#if $_user?.profile == 'Supertutor'}
            <Cell><span class='unselectable'>{option.label_societa}</span></Cell>
          {/if}
          {#if $_user?.profile == 'Tutor' || $_user?.profile == 'Supertutor'}
            <Cell><span class='unselectable'>{option.label_progetto}</span></Cell>
          {/if}
          <Cell numeric><span class='unselectable'>{option.numero_acquisizioni}</span></Cell>
          <Cell><span class='unselectable'>{getStatusLabel(option)}</span></Cell>
            <Cell><span class='unselectable'>{option.scanner}</span></Cell>
        </Row>
      {/each}
    </Body>
  </DataTable>
</div>

<style>
  .table-container {
    width: 100%;
    overflow-y: hidden;
  }

  .unselectable {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  
  * :global(.mdc-data-table::-webkit-scrollbar) {
    width: 15px;
  }

  * :global(.mdc-data-table::-webkit-scrollbar-track) {
    background-color: white;
    border-radius: 10px;
  }

  * :global(.mdc-data-table::-webkit-scrollbar-thumb) {
    background-color: var(--mdc-theme-primary);
    border-radius: 10px;
  }

  * :global(.mdc-data-table) {
    width: 100%;
    height: 700px;
    overflow-y: auto;
  }

  * :global(.mdc-data-table__row) {
    cursor: pointer;
  }

  * :global(.mdc-data-table__row.selected) {
    background-color: var(--mdc-theme-primary-selected);
  }
</style>