<script>
    import { _selected<PERSON>ob, _user, _isAddingJob, _isShowingNote, _shouldInit, _scannerId, _host, _isFinalizing, _isShowingLogs, _mainViewerDidMount, _showJobsInRecovery, _isRecovering } from "@store";
    import ConfirmationDialog from "@components/common/ConfirmationDialog.svelte";
    import IconButton from "@components/common/IconButton.svelte";
    import NoteDialog from "@components/note/NoteDialog.svelte";
    import LogsDialog from "@components/job-manager/LogsDialog.svelte";
    import AddJobDialog from "@components/job-manager/AddJobDialog.svelte";
    import { notify } from '@utility';
    import { replace } from 'svelte-spa-router';
    import * as d3 from 'd3-selection';

    //smui
    import Button, { Label } from '@smui/button';

    //icons
    import UnlockJobIcon from "svelte-material-icons/Restore.svelte";
    import RemoveIcon from "svelte-material-icons/Minus.svelte";
    import ShowInRecoveryIcon from "svelte-material-icons/ArchiveEye.svelte";
    import TableRefreshIcon from "svelte-material-icons/TableRefresh.svelte";
    import LogIcon from "svelte-material-icons/MathLog.svelte";
    import InformationIcon from "svelte-material-icons/BookInformationVariant.svelte";
    import RightIcon from "svelte-material-icons/ChevronRightCircle.svelte";
    import LeftIcon from "svelte-material-icons/ChevronLeftCircle.svelte";
    import CheckIcon from "svelte-material-icons/CheckCircle.svelte";
    
    let openConfirmationDialog;
    let confirmationDialogAction;

    const handleAnswer = async (e) => {
        const answer = e.detail;

        if(!answer) return;

        if(confirmationDialogAction == 'SBLOCCA JOB')
            doUnlockJob();
        else if(confirmationDialogAction == 'RIMUOVI JOB')
            await doRemove();
        else if(confirmationDialogAction == 'COMPLETA ACQUISIZIONE')
            await doCompleteAcquisition();
        else if(confirmationDialogAction == 'RESET ACQUISIZIONE')
            await doResetAcquisition();
        else if (confirmationDialogAction == 'COMPLETA VALIDAZIONE')
            await doCompleteValidation();
        else if (confirmationDialogAction == 'RESET VALIDAZIONE')
            await doResetValidation();
        else if (confirmationDialogAction == 'FINALIZZA JOB')
            await doCompleteFinalization();
        else if (confirmationDialogAction == 'RESET JOB')
            await doRecovery();
    }

    const handleShowInRecovery = () => {
        _showJobsInRecovery.set(!$_showJobsInRecovery);
        d3.select('#job-manager').dispatch('refresh-jobs-table');
    }

    const handleUnlockJob = async () => {
        confirmationDialogAction = 'SBLOCCA JOB';
        openConfirmationDialog = true;
    }

    const doUnlockJob = async () => {
        let params = {};
        params.statusDigit = 'in pausa';
        params.idDigit = $_selectedJob.id_digit;
        const response = await window.electron.database("db-set-status-digit", params);

        if(response.affectedRows > 0) {
            notify.success("Job riportato in pausa");
            d3.select('#job-manager').dispatch('refresh-jobs-table', {detail: { idDigit: $_selectedJob.id_digit }});
        }
    }

    const handleRemove = () => {
        confirmationDialogAction = 'RIMUOVI JOB';
        openConfirmationDialog = true;
    }

    const doRemove = async () => {
        let params = {};
        params.statusDigit = null;
        params.idDigit = $_selectedJob.id_digit;
        const response = await window.electron.database("db-set-status-digit", params);

        if(response.affectedRows > 0) {
            /* Delete job folder from SCANTEMP */
            await window.electron.filesystem("fs-delete-file-or-dir", $_selectedJob.scan_path);

            notify.success("Job rimosso con successo");
            d3.select('#job-manager').dispatch('refresh-jobs-table');
        }
    }

    const handleRefresh = () => {
        d3.select('#job-manager').dispatch('refresh-jobs-table', {detail: {idDigit: $_selectedJob?.id_digit}});
    }

    let logs;
    const handleViewLog = async () => {
        logs = '';
        const logRecords = await window.electron.database('db-get-logs', { idDigit: $_selectedJob.id_digit });

        for(const log of logRecords) {
            logs += log.text;
        }

        _isShowingLogs.set(true);
    }

    const handleNote = () => {
        _isShowingNote.set(true);
    }

    const handleResetAcquisition = () => {
        confirmationDialogAction = 'RESET ACQUISIZIONE';
        openConfirmationDialog = true;
    }

    const doResetAcquisition = async () => {
        let params = {};
        params.statusDigit = "in pausa";
        params.idDigit = $_selectedJob.id_digit;
        const response = await window.electron.database("db-set-status-digit", params);

        if(response.affectedRows > 0) {
            /* Update note_validazione adding "Rifacimento" */
            params.column = 'note_validazione';
            params.value = $_selectedJob?.note_validazione ? $_selectedJob.note_validazione + '\nRifacimento' : 'Rifacimento';
            await window.electron.database('db-update-note-job', params);

            /* Log Event */
            let eventText = '';
            eventText += `[${new Date().toLocaleString('it-IT').replace(',', '')}]\n`;
            eventText += `Evento: Riapertura JOB (passa JOB ad operatore per modifiche)\n`;
            eventText += `Gruppo: ${$_user.company} - Operatore: ${$_user.displayName || $_user.username} - Livello: ${$_user.profile}\n`;
            eventText += `ID : ${$_scannerId}\n\n`;
            await window.electron.database('db-log', {level:'EVENT', text: eventText, eventType: 'Riapertura JOB (passa JOB ad operatore per modifiche)'});

            /* Write log file (overwrite if exists) */
            await writeLogFile();
        
            notify.success("Reset effettuato, job pronto l'acquisizone");
            d3.select('#job-manager').dispatch('refresh-jobs-table', {detail: {idDigit: $_selectedJob?.id_digit}});
        }
    }

    const handleAcquisition = async () => {
        let params = {};
        params.statusDigit = "acquisizione";
        params.scanner = $_scannerId;
        params.host = $_host;
        params.idDigit = $_selectedJob.id_digit;

        const response = await window.electron.database("db-set-status-digit", params);

        if(response.affectedRows > 0) {
            $_selectedJob.status_digit = 'acquisizione';

            /* Ensure that the job folder exists */
		    await window.electron.filesystem("fs-ensure-dir", $_selectedJob.scan_path);

            /* Count files in job folder */
            const fileCount = await window.electron.filesystem("fs-get-dir-files", {path: $_selectedJob.scan_path}) || [];

            /* If job folder is empty, write log Creazione JOB */
            if(fileCount.length == 0){
                /* Log Event */
                let eventText = '';
                eventText += `[${new Date().toLocaleString('it-IT').replace(',', '')}]\n`;
                eventText += `Evento: Creazione JOB\n`;
                eventText += `Gruppo: ${$_user.company} - Operatore: ${$_user.displayName || $_user.username} - Livello: ${$_user.profile}\n`;
                eventText += `ID : ${$_scannerId}\n\n`;
                await window.electron.database('db-log', {level:'EVENT', text: eventText, eventType: 'Creazione JOB'});

                /* Write log file (overwrite if exists) */
                await writeLogFile();

                /* Update inizio_acquisizione */
                await window.electron.database('db-update-acquisizioni-column', {columnName:'inizio_acquisizione', value: parseInt(Date.now()/1000), idDigit: $_selectedJob.id_digit});
            }

            _shouldInit.set(true);
            _mainViewerDidMount.set(false);

            replace("/acquisizione");
        } else {
            notify.error("Errore durante l'aggiornamento dello stato del job");
        }
    }

    const handleCompleteAcquisition = () => {
        confirmationDialogAction = 'COMPLETA ACQUISIZIONE';
        openConfirmationDialog = true;
    }

    const doCompleteAcquisition = async () => {
        /* Update status_digit for the job and refresh table */
        let params = {};
        params.statusDigit = "acquisito";
        params.idDigit = $_selectedJob.id_digit;
        const response = await window.electron.database("db-set-status-digit", params);

        if(response.affectedRows > 0) {
            if($_selectedJob?.note_validazione?.includes("Rifacimento")){
               /* Update note_validazione removing "Rifacimento" */
                params.column = 'note_validazione';
                params.value = $_selectedJob?.note_validazione.replace('Rifacimento', '');
                await window.electron.database('db-update-note-job', params);
            }

            /* Log Event */
            let eventText = '';
            eventText += `[${new Date().toLocaleString('it-IT').replace(',', '')}]\n`;
            eventText += `Evento: Chiusura JOB (passa JOB a Tutor per verifica)\n`;
            eventText += `Gruppo: ${$_user.company} - Operatore: ${$_user.displayName || $_user.username} - Livello: ${$_user.profile}\n`;
            eventText += `ID : ${$_scannerId}\n\n`;
            await window.electron.database('db-log', {level:'EVENT', text: eventText, eventType: 'Chiusura JOB (passa JOB a Tutor per verifica)'});

            /* Write log file (overwrite if exists) */
            await writeLogFile();

            /* Update fine_acquisizione only if null */
            const fineAcquisizione = await window.electron.database('db-get-acquisizioni-value', {columnName:'fine_acquisizione', idDigit: $_selectedJob.id_digit});
            if(!fineAcquisizione)
                await window.electron.database('db-update-acquisizioni-column', {columnName:'fine_acquisizione', value: parseInt(Date.now()/1000), idDigit: $_selectedJob.id_digit});

            d3.select('#job-manager').dispatch('refresh-jobs-table', {detail: {idDigit: $_selectedJob?.id_digit}});
            notify.success("Acquisizione completata per il job " + $_selectedJob.identifier);
        }
    }

    const handleValidation = async () => {
        let params = {};
        params.statusDigit = "validazione";
        params.idDigit = $_selectedJob.id_digit;

        const response = await window.electron.database("db-set-status-digit", params);

        if(response.affectedRows > 0) {
            /* Log Event */
            let eventText = '';
            eventText += `[${new Date().toLocaleString('it-IT').replace(',', '')}]\n`;
            eventText += `Evento: Controllo JOB\n`;
            eventText += `Gruppo: ${$_user.company} - Operatore: ${$_user.displayName || $_user.username} - Livello: ${$_user.profile}\n`;
            eventText += `ID : ${$_scannerId}\n\n`;
            await window.electron.database('db-log', {level:'EVENT', text: eventText, eventType: 'Controllo JOB'});

            /* Write log file (overwrite if exists) */
            await writeLogFile();

            /* Update inizio_validazione */
            await window.electron.database('db-update-acquisizioni-column', {columnName:'inizio_validazione', value: parseInt(Date.now()/1000), idDigit: $_selectedJob.id_digit});

            $_selectedJob.status_digit = 'validazione';

            /* Ensure that the preview folder exists */
		    await window.electron.filesystem("fs-ensure-dir", $_selectedJob.preview_path);

            /* Go to previewVisualizer page */
            _mainViewerDidMount.set(false);
            replace("/previewVisualizer");
            
        } else {
            notify.error("Errore durante l'aggiornamento dello stato del job");
        }
    }

    const handleCompleteValidation = () => {
        confirmationDialogAction = 'COMPLETA VALIDAZIONE';
        openConfirmationDialog = true;
    }

    const doCompleteValidation = async () => {
        let params = {};
        params.statusDigit = "validato";
        params.idDigit = $_selectedJob.id_digit;
        const response = await window.electron.database("db-set-status-digit", params);

        if(response.affectedRows > 0) {
            /* Log Event */
            let eventText = '';
            eventText += `[${new Date().toLocaleString('it-IT').replace(',', '')}]\n`;
            eventText += `Evento: Validazione JOB\n`;
            eventText += `Gruppo: ${$_user.company} - Operatore: ${$_user.displayName || $_user.username} - Livello: ${$_user.profile}\n`;
            eventText += `ID : ${$_scannerId}\n\n`;
            await window.electron.database('db-log', {level:'EVENT', text: eventText, eventType: 'Validazione JOB'});

            /* Write log file (overwrite if exists) */
            await writeLogFile();

            /* Update fine_validazione */
            await window.electron.database('db-update-acquisizioni-column', {columnName:'fine_validazione', value: parseInt(Date.now()/1000), idDigit: $_selectedJob.id_digit});

            /* Update nominativo_tutor */
            await window.electron.database('db-update-acquisizioni-column', {columnName:'nominativo_tutor', value: $_user.displayName, idDigit: $_selectedJob.id_digit});

            d3.select('#job-manager').dispatch('refresh-jobs-table', {detail: {idDigit: $_selectedJob?.id_digit}});
            notify.success("Validazione completata");
        } else {
            notify.error("Errore durante l'aggiornamento dello stato del job");
        }
    }

    const handleResetValidation = () => {
        confirmationDialogAction = 'RESET VALIDAZIONE';
        openConfirmationDialog = true;
    }

    const doResetValidation = async () => {
        let params = {};
        params.statusDigit = "acquisito";
        params.idDigit = $_selectedJob.id_digit;
        const response = await window.electron.database("db-set-status-digit", params);

        if(response.affectedRows > 0) {
            /* Log Event */
            let eventText = '';
            eventText += `[${new Date().toLocaleString('it-IT').replace(',', '')}]\n`;
            eventText += `Evento: Riapertura JOB (Passa a Tutor da SuperTutor)\n`;
            eventText += `Gruppo: ${$_user.company} - Operatore: ${$_user.displayName || $_user.username} - Livello: ${$_user.profile}\n`;
            eventText += `ID : ${$_scannerId}\n\n`;
            await window.electron.database('db-log', {level:'EVENT', text: eventText, eventType: 'Riapertura JOB (Passa a Tutor da SuperTutor)'});

            /* Write log file (overwrite if exists) */
            await writeLogFile();

            notify.success("Reset effettuato, job pronto per la validazione");
        }

        d3.select('#job-manager').dispatch('refresh-jobs-table', {detail: {idDigit: $_selectedJob?.id_digit}});
    }

    const handleFinalization = async () => {
        if($_selectedJob?.status_digit != 'validato') {
            /* Ensure that the preview folder exists */
            await window.electron.filesystem("fs-ensure-dir", $_selectedJob.preview_path);
    
            /* Go to previewVisualizer page */
            _mainViewerDidMount.set(false);
            replace("/previewVisualizer");
        } else {
            let params = {};
            params.statusDigit = "finalizzazione";
            params.idDigit = $_selectedJob.id_digit;
    
            const response = await window.electron.database("db-set-status-digit", params);
    
            if(response.affectedRows > 0) {
                /* Log Event */
                let eventText = '';
                eventText += `[${new Date().toLocaleString('it-IT').replace(',', '')}]\n`;
                eventText += `Evento: Controllo JOB\n`;
                eventText += `Gruppo: ${$_user.company} - Operatore: ${$_user.displayName || $_user.username} - Livello: ${$_user.profile}\n`;
                eventText += `ID : ${$_scannerId}\n\n`;
                await window.electron.database('db-log', {level:'EVENT', text: eventText, eventType: 'Controllo JOB'});
    
                /* Write log file (overwrite if exists) */
                await writeLogFile();
    
                /* Update inizio_finalizzazione */
                await window.electron.database('db-update-acquisizioni-column', {columnName:'inizio_finalizzazione', value: parseInt(Date.now()/1000), idDigit: $_selectedJob.id_digit});
    
                $_selectedJob.status_digit = 'finalizzazione';
    
                /* Ensure that the preview folder exists */
                await window.electron.filesystem("fs-ensure-dir", $_selectedJob.preview_path);
    
                /* Go to previewVisualizer page */
                _mainViewerDidMount.set(false);
                replace("/previewVisualizer");
            } else {
                notify.error("Errore durante l'aggiornamento dello stato del job");
            }
        }
    }

    const handleCompleteFinalization = () => {
        confirmationDialogAction = 'FINALIZZA JOB';
        openConfirmationDialog = true;
    }

    const doCompleteFinalization = async () => {
        _isFinalizing.set(true);
        let params = {};
        params.statusDigit = "finalizzato";
        params.idDigit = $_selectedJob.id_digit;
        const response = await window.electron.database("db-set-status-digit", params);

        if(response.affectedRows > 0) {
            /* Log Event */
            let eventText = '';
            eventText += `[${new Date().toLocaleString('it-IT').replace(',', '')}]\n`;
            eventText += `Evento: Finalizzazione JOB\n`;
            eventText += `Gruppo: ${$_user.company} - Operatore: ${$_user.displayName || $_user.username} - Livello: ${$_user.profile}\n`;
            eventText += `ID : ${$_scannerId}\n\n`;
            await window.electron.database('db-log', {level:'EVENT', text: eventText, eventType: 'Finalizzazione JOB'});

            /* Update fine_finalizzazione */
            await window.electron.database('db-update-acquisizioni-column', {columnName:'fine_finalizzazione', value: parseInt(Date.now()/1000), idDigit: $_selectedJob.id_digit});

            /* Update nominativo_supertutor */
            await window.electron.database('db-update-acquisizioni-column', {columnName:'nominativo_supertutor', value: $_user.displayName, idDigit: $_selectedJob.id_digit});

            /* Check and clean job folder for orphan files */
            await cleanJobFolder();

            /* Rename files with final name: segnatura_sequenzialeFinale_displayName.tif */
            /* Get ordinatori list */
            const ordinatoriList = await window.electron.database('db-get-ordinatori');

            /* Get scan list and sort */
            let scanArray = await window.electron.database('db-get-scansioni', { idDigit: $_selectedJob.id_digit });
            scanArray = scanArray.sort((a, b) => {
                const ordinatoreA = ordinatoriList.find((ordinatore) => ordinatore.metis_code == a.codice_ordinatore_sx);
                const ordinatoreB = ordinatoriList.find((ordinatore) => ordinatore.metis_code == b.codice_ordinatore_sx);
                const visualizationSequenceA = $_selectedJob.is_stampato ? ordinatoreA?.visualization_sequence_stampati || 50 : ordinatoreA?.visualization_sequence || 50;
                const visualizationSequenceB = $_selectedJob.is_stampato ? ordinatoreB?.visualization_sequence_stampati || 50 : ordinatoreB?.visualization_sequence || 50;

                return visualizationSequenceA - visualizationSequenceB;
            });

            const scanPath = $_selectedJob.scan_path;
            let sequenceNum = 0;
            for(const scan of scanArray) {
                sequenceNum++;
                
                if(scan.pagina_doppia){
                    if(scan.ordinamento_arabo) {
                        await window.electron.filesystem('fs-rename-file-or-dir', {oldPath: `${scanPath}\\${scan.id}_1.tif`, newPath: `${scanPath}\\${scan.segnatura}_${sequenceNum.toString().padStart(4, '0')}_${scan.display_name_sx}.tif`});
                        sequenceNum++;
                        await window.electron.filesystem('fs-rename-file-or-dir', {oldPath: `${scanPath}\\${scan.id}_0.tif`, newPath: `${scanPath}\\${scan.segnatura}_${sequenceNum.toString().padStart(4, '0')}_${scan.display_name_dx}.tif`});
                    } else {
                        await window.electron.filesystem('fs-rename-file-or-dir', {oldPath: `${scanPath}\\${scan.id}_0.tif`, newPath: `${scanPath}\\${scan.segnatura}_${sequenceNum.toString().padStart(4, '0')}_${scan.display_name_sx}.tif`});
                        sequenceNum++;
                        await window.electron.filesystem('fs-rename-file-or-dir', {oldPath: `${scanPath}\\${scan.id}_1.tif`, newPath: `${scanPath}\\${scan.segnatura}_${sequenceNum.toString().padStart(4, '0')}_${scan.display_name_dx}.tif`});
                    }
                } else {
                    await window.electron.filesystem('fs-rename-file-or-dir', {oldPath: `${scanPath}\\${scan.id}_0.tif`, newPath: `${scanPath}\\${scan.segnatura}_${sequenceNum.toString().padStart(4, '0')}_${scan.display_name_sx}.tif`});

                    /* Delete right image because it shouldn't exist on filesystem */
                    await window.electron.filesystem("fs-delete-file-or-dir", `${scanPath}\\${scan.id}_1.tif`);
                }
            }

            /* Delete previews folder */
            await window.electron.filesystem("fs-delete-file-or-dir", $_selectedJob.preview_path);

            /* Log Event */
            eventText = '';
            eventText += `[${new Date().toLocaleString('it-IT').replace(',', '')}]\n`;
            eventText += `Evento: JOB Finalizzato\n`;
            eventText += `Gruppo: ${$_user.company} - Operatore: ${$_user.displayName || $_user.username} - Livello: ${$_user.profile}\n`;
            eventText += `ID : ${$_scannerId}\n\n`;
            await window.electron.database('db-log', {level:'EVENT', text: eventText, eventType: 'JOB Finalizzato'});

            /* Write log file (overwrite if exists) */
            await writeLogFile();
            
            /* Update finalizzato */
            await window.electron.database('db-update-acquisizioni-column', {columnName:'finalizzato', value: parseInt(Date.now()/1000), idDigit: $_selectedJob.id_digit});

            /* Set fields needed for stats on table Acquisizioni */
            await setAcquisizioniStatsFields();

            /* Move job folder in DWORKEND_NEW\FINALIZZATI */
            await window.electron.ssh('ssh-move-file-or-dir', {source: $_selectedJob.scan_path, destination: $_selectedJob.finalized_path});

            notify.success("Finalizzazione completata");
            _isFinalizing.set(false);
            d3.select('#job-manager').dispatch('refresh-jobs-table');
        } else {
            _isFinalizing.set(false);
            notify.error("Errore durante l'aggiornamento dello stato del job");
        }
    }

    const setAcquisizioniStatsFields = async () => {
        let dbRecords;
        const params = {
            idDigit: $_selectedJob.id_digit,
            acquisitionNumber: 0,
            substitutionNumber: 0,
            renameNumber: 0,
            deletionNumber: 0,
            fileNumber: 0,
            operatoriList: '',
            tutorList: '',
            supertutorList: '',
            scannerList: '',
            hostList: ''
        }

        /* Count event types from DB and update counter variables */
        dbRecords = await window.electron.database("db-get-events-number", { idDigit: $_selectedJob.id_digit });
        for (const event of dbRecords) {
            if(event.event_type == 'Acquisizione Immagine')
                params.acquisitionNumber = event.event_number;
            else if(event.event_type == 'Sostituzione Immagine')
                params.substitutionNumber = event.event_number;
            else if(event.event_type == 'Rinominazione Immagine')
                params.renameNumber = event.event_number;
            else if(event.event_type == 'Cancellazione Immagine')
                params.deletionNumber = event.event_number;
        }
        
        /* Get users (with profiles) involved in the job and populate lists */
        dbRecords = await window.electron.database("db-get-events-users", { idDigit: $_selectedJob.id_digit });
        for (const event of dbRecords) {
            if(event.user_profile == 'Operatore')
                params.operatoriList += event.user + ',';
            else if(event.user_profile == 'Tutor')
                params.tutorList += event.user + ',';
            else if(event.user_profile == 'Supertutor')
                params.supertutorList += event.user + ',';
        }
        params.operatoriList = params.operatoriList.slice(0, -1);
        params.tutorList = params.tutorList.slice(0, -1);
        params.supertutorList = params.supertutorList.slice(0, -1);

        /* Get scanners and hosts involved in the job and populate lists */
        dbRecords = await window.electron.database("db-get-events-scanners-and-hosts", { idDigit: $_selectedJob.id_digit });
        for (const event of dbRecords) {
            params.scannerList += event.scanner_id + ',';
            params.hostList += event.host + ',';
        }
        params.scannerList = params.scannerList.slice(0, -1);
        params.hostList = params.hostList.slice(0, -1);

        /* Get num of tif files in job folder */
        const fileList = await window.electron.filesystem("fs-get-dir-files", {path: $_selectedJob.scan_path, fileExtension: '.tif'});
        params.fileNumber = fileList.length;

        /* Perform DB updates for stats fields */
        await window.electron.database("db-update-acquisizioni-stats", params);
    }

    const handleRecovery = () => {
        confirmationDialogAction = 'RESET JOB';
        openConfirmationDialog = true;
    }

    const doRecovery = async () => {
        _isRecovering.set(true);
        let params = {};
        params.statusDigit = "in pausa";
        params.idDigit = $_selectedJob.id_digit;
        const response = await window.electron.database("db-set-status-digit", params);

        if(response.affectedRows > 0) {
            /* Copy job from RECOVERY to SCANTEMP */
            await window.electron.ssh('ssh-copy-dir', {source: $_selectedJob.recovery_path, destination: $_selectedJob.scan_path});
            
            /* Rename files from final name to scan id */
            /* Get ordinatori list */
            const ordinatoriList = await window.electron.database('db-get-ordinatori');

            /* Get scan list and sort */
            let scanArray = await window.electron.database('db-get-scansioni', { idDigit: $_selectedJob.id_digit });
            scanArray = scanArray.sort((a, b) => {
                const ordinatoreA = ordinatoriList.find((ordinatore) => ordinatore.metis_code == a.codice_ordinatore_sx);
                const ordinatoreB = ordinatoriList.find((ordinatore) => ordinatore.metis_code == b.codice_ordinatore_sx);
                const visualizationSequenceA = $_selectedJob.is_stampato ? ordinatoreA?.visualization_sequence_stampati || 50 : ordinatoreA?.visualization_sequence || 50;
                const visualizationSequenceB = $_selectedJob.is_stampato ? ordinatoreB?.visualization_sequence_stampati || 50 : ordinatoreB?.visualization_sequence || 50;

                return visualizationSequenceA - visualizationSequenceB;
            });
            
            const scanPath = $_selectedJob.scan_path;
            let sequenceNum = 0;
            for(const scan of scanArray) {
                sequenceNum++;
                await window.electron.filesystem('fs-rename-file-or-dir', {oldPath: `${scanPath}\\${scan.segnatura}_${sequenceNum.toString().padStart(4, '0')}_${scan.display_name_sx}.tif`, newPath: `${scanPath}\\${scan.id}_0.tif`});
                
                if(scan.pagina_doppia){
                    sequenceNum++;
                    await window.electron.filesystem('fs-rename-file-or-dir', {oldPath: `${scanPath}\\${scan.segnatura}_${sequenceNum.toString().padStart(4, '0')}_${scan.display_name_dx}.tif`, newPath: `${scanPath}\\${scan.id}_1.tif`});
                }
            }

            notify.success("Reset completato");
            _isRecovering.set(false);
            _showJobsInRecovery.set(false);
            d3.select('#job-manager').dispatch('refresh-jobs-table', {detail: {idDigit: $_selectedJob?.id_digit}});
        } else {
            _isRecovering.set(false);
            notify.error("Errore durante l'aggiornamento dello stato del job");
        }
    }

    const writeLogFile = async () => {
        let logString = '';
        const logRecords = await window.electron.database('db-get-logs', { idDigit: $_selectedJob.id_digit });

        for(const log of logRecords) {
            logString += log.text;
        }

        let params = {
            path: `${$_selectedJob.scan_path}\\${$_selectedJob.identifier}.log`,
            text: logString,
            mode: 'write'
        };

        await window.electron.filesystem('fs-write-file', params);
        await window.electron.database('db-log', {level:'INFO', text:'[JobsTableTools.svelte][writeLogFile] Log file written'});
    }

    const cleanJobFolder = async () => {
        /* Get all tif files in job folder */
        const fileList = await window.electron.filesystem("fs-get-dir-files", {path: $_selectedJob.scan_path, fileExtension: '.tif'}) || [];

        if(fileList.length == 0) return;
        
        /* Get scan list */
        const scanList = await window.electron.database('db-get-scansioni', { idDigit: $_selectedJob.id_digit });

        /* For each file, check if there is a scan record on DB */
        for (const file of fileList) {
            const fileId = file.slice(0, file.indexOf('_'));
            const dbRecord = scanList.find((scan) => scan.id == fileId);

            /* Delete tif file if scan record does not exist on DB */
            if(!dbRecord){
                await window.electron.filesystem("fs-delete-file-or-dir", `${$_selectedJob.scan_path}\\${file}`);
                await window.electron.database('db-log', {level:'INFO', text: `[JobstableTools.svelte][cleanJobFolder] Deleted file ${file} during finalization`});
            }
        }
    }
</script>

<ConfirmationDialog bind:open={openConfirmationDialog} action={confirmationDialogAction} on:answer={(e) => handleAnswer(e)}/>

<div class="flex-row">
    <div class="flex-inner-row">
        <IconButton icon={UnlockJobIcon} iconWidth=30 iconHeight=40  iconColor="var(--mdc-theme-secondary)" tooltip="Riporta in pausa" onclick={handleUnlockJob} disabled={$_selectedJob?.status_digit != 'acquisizione'}/>
        <IconButton icon={RemoveIcon} iconWidth=30 iconHeight=40  iconColor="var(--mdc-theme-error)" tooltip="Rimuovi" onclick={handleRemove} disabled={($_user?.profile != "Tutor" && $_user?.profile != "Supertutor") || $_selectedJob?.status_digit != 'in pausa'}/>
        {#if $_user?.profile == "Supertutor"}
            <IconButton icon={ShowInRecoveryIcon} iconWidth=30 iconHeight=40  iconColor={$_showJobsInRecovery ? "var(--mdc-theme-primary)" : "var(--mdc-theme-secondary)"} tooltip="Mostra Job in recovery" onclick={handleShowInRecovery}/>    
        {/if}
        <IconButton icon={TableRefreshIcon} iconWidth=30 iconHeight=40  iconColor="var(--mdc-theme-secondary)"  tooltip="Aggiorna Tabella" onclick={handleRefresh}/>
    </div>
    <hr />
    <div class="flex-inner-row">
        <IconButton icon={LogIcon} iconWidth=30 iconHeight=40  iconColor="var(--mdc-theme-secondary)" tooltip="Visualizza Log" onclick={handleViewLog} disabled={!$_selectedJob}/>
        <IconButton icon={InformationIcon} iconWidth=30 iconHeight=40  iconColor="var(--mdc-theme-secondary)" tooltip="Note" onclick={handleNote} disabled={!$_selectedJob}/>
    </div>
    <hr />
    <div class="flex-inner-row">
        <Button style="margin-left:15px;" variant="raised" disabled={$_selectedJob?.status_digit != 'in pausa'} on:click={handleAcquisition}>
            <Label>Acquisizione</Label>
        </Button>
        <IconButton icon={RightIcon} iconWidth=30 iconHeight=40  iconColor="var(--mdc-theme-primary)" tooltip="Fine acquisizione" onclick={handleCompleteAcquisition} disabled={$_selectedJob?.status_digit != 'in pausa'}/>
    </div>
    <hr />
    <div class="flex-inner-row">
        <IconButton icon={LeftIcon} iconWidth=30 iconHeight=40  iconColor="var(--mdc-theme-primary)" tooltip="Riporta in acquisizione" onclick={handleResetAcquisition} disabled={($_user?.profile != "Tutor" && $_user?.profile != "Supertutor") || $_selectedJob?.status_digit != 'acquisito'}/>
        <Button variant="raised" on:click={handleValidation} disabled={($_user?.profile != "Tutor" && $_user?.profile != "Supertutor") || $_selectedJob?.status_digit != 'acquisito'}>
            <Label>Validazione</Label>
        </Button>
        <IconButton icon={RightIcon} iconWidth=30 iconHeight=40  iconColor="var(--mdc-theme-primary)" tooltip="Fine validazione" onclick={handleCompleteValidation} disabled={($_user?.profile != "Tutor" && $_user?.profile != "Supertutor") || $_selectedJob?.status_digit != 'acquisito'}/>
    </div>
    <hr />
    <div class="flex-inner-row">
        <IconButton icon={LeftIcon} iconWidth=30 iconHeight=40  iconColor="var(--mdc-theme-primary)" tooltip="Riporta in validazione" onclick={handleResetValidation} disabled={($_user?.profile != "Tutor" && $_user?.profile != "Supertutor") || $_selectedJob?.status_digit != 'validato'}/>
        <Button variant="raised" on:click={handleFinalization} disabled={$_user?.profile != "Supertutor" || !$_selectedJob}>
            <Label>Controllo</Label>
        </Button>
        <IconButton icon={CheckIcon} iconWidth=30 iconHeight=40  iconColor="var(--mdc-theme-primary)" tooltip="Finalizza job" onclick={handleCompleteFinalization} disabled={$_user?.profile != "Supertutor" || $_selectedJob?.status_digit != 'validato'}/>
        {#if $_showJobsInRecovery}
            <Button variant="raised" on:click={handleRecovery} disabled={$_user?.profile != "Supertutor" || $_selectedJob?.status_digit != 'finalizzato'}>
                <Label>Reset</Label>
            </Button>
        {/if}
    </div>
</div>

<NoteDialog/>
<LogsDialog {logs}/>
<AddJobDialog />

<style>
    .flex-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .flex-inner-row {
        display: flex;
        justify-content: flex-start;
        align-items: center;
    }

    hr {
        height: 30px;
        border: 1.5px solid var(--mdc-theme-secondary);
        margin: 0;
        border-right: 0;
    }

    * :global(.mdc-button--raised) {
        padding-left: 10px;
        padding-right: 10px;
    }
</style>