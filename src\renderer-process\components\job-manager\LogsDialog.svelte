<script>
    import { _isShowingLogs } from '@store';

    //smui
    import Dialog, { Title, Content } from '@smui/dialog';
    import Button, { Label } from '@smui/button';

    export let logs = '';
   
    const handleClose = () => {
        _isShowingLogs.set(false);
    }
</script>

<div>
    <Dialog bind:open={$_isShowingLogs} scrimClickAction="">
        <Title>LOG</Title>
        <Content>
            <textarea bind:value={logs} placeholder="Nessun log disponibile" readonly rows="40"></textarea>
        </Content>
        <div class="flex-row-buttons">
            <Button on:click={handleClose} variant="raised">
                <Label>Chiudi</Label>
            </Button>
        </div>
    </Dialog>
</div>

<style>
    .flex-row-buttons {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 10px;
    }

    textarea{
        background-color: transparent;
        color: var(--mdc-theme-secondary);
        border-color: var(--mdc-theme-secondary);
        resize: none;
        border-radius: 3px;
        padding: 10px;
    }
    
    textarea:focus {
        outline: none !important;
        border-color: var(--mdc-theme-primary);
    }

    textarea::-webkit-scrollbar {
        width: 15px;
    }

    textarea::-webkit-scrollbar-track {
        background-color: white;
        border-radius: 10px;
    }

    textarea::-webkit-scrollbar-thumb {
        background-color: var(--mdc-theme-primary);
        border-radius: 10px;
    }

    * :global(.mdc-dialog .mdc-dialog__surface) {
        min-width: 50%;
        min-height: 70%;
    }

    * :global(.mdc-dialog__content) {
        display: flex;
        flex-direction: column;
        overflow-x: hidden;
    }
</style>