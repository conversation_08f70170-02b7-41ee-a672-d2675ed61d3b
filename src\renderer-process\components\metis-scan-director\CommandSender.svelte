<script>
    import { _pressureKg, _enablePlates, _enableBalance, _balanceFriction, _autoCalcThickRatio, _thickWeightRatio, _autoOpenAfterScan, _autoCloseOnScan, _waitForScanPedal, _waitForPressure, _autoOpenClose, _autoOpenMmDown, _lockGlassMove, _openAmountPercentage, _openGlassSpeed, _closeGlassSpeed, _glassAcceleration, _closingGlassPosition, _workWithContact, _contactDistance, _enableFinePressure, _pressureAccuracy, _upSpeed, _downSpeed, _stopScannerPressure, _opticalResolutionList, _opticalResolution, _lightLeft, _lightCenterLeft, _lightCenterRight, _lightRight, _iccProfileSourceList, _iccProfileDestinationList,  _iccProfileDisplayList,_iccProfileDestination, _iccProfileSource, _iccProfileDisplay, _thickness, _lightDistance } from "@store";
    import { onMount } from "svelte";
    import { waitForScanner, notify, delay } from "@utility";
    import CommandSenderDialog from "@components/metis-scan-director/CommandSenderDialog.svelte";
    import * as d3 from 'd3-selection';

    let open = false;
    let loadingText;

    onMount(() => {
        d3.select('#command-sender').on('send-commands', (e) => sendCommandsHandler(e));
    })

    const sendCommandsHandler = async (e) => {
        /* Open dialog */
        open = true;

        /* Get custom event parameters */
        const isInit = e.detail.isInit;
        const commandList = e.detail.commandList;

        let withErrors = false;

        /* Cycle through commands to be sent */
        for (const command of commandList) {
            /* Wait for the scanner to be ready to receive commands */
            loadingText = 'Attendo che lo scanner sia disponibile...';
            if(await window.electron.variables('get-app-variable', {variableName: 'isPackaged'})) await waitForScanner(3000, 10000);
            
            /* Send command */
            loadingText = command.loadingText || 'Invio del comando: ' + command.name;
            await window.electron.database('db-log', {level:'DEBUG', text:'[CommandSender.svelte][sendCommandsHandler] Sending command ' + command.name});
            
            const response = await window.electron.metis(command.name, command.params);

            if(response.result != 'OK'){
                withErrors = true;
                notify.error("Si è verificato un errore durante l'invio del comando: " + command.name);
                await window.electron.database('db-log', {level:'ERROR', text:'[CommandSender.svelte][sendCommandsHandler] ' + command.name + ' result: ' + response.result});
            } else {
                /* Command result OK, executing callback function if present */
                await window.electron.database('db-log', {level:'DEBUG', text:'[CommandSender.svelte][sendCommandsHandler] Command result: ' + response.result});
                if(command.hasCallbackFn) await runCallbackFn(command, response);
            }

            await delay(1000);
        }
        
        /* Dispatch event to inform that commands have been sent */
        if(isInit)
            d3.select("#acquisizione").dispatch("init-commands-sent");
        else
            d3.select('#acquisizione').dispatch('commands-sent', {detail: {withErrors: withErrors}});
        
        /* Close dialog */
        open = false;
    }

    const runCallbackFn = async (command, response) => {
        await window.electron.database('db-log', {level:'DEBUG', text:'[CommandSender.svelte][runCallbackFn] Running callback function for command: ' + command.name});
        switch (command.name) {
            case 'msd-get-book-cradle-parameters-and-status':
                _pressureKg.set(response.data.pressureKg);
                _enablePlates.set(response.data.enablePlates);
                _enableBalance.set(response.data.enableBalance);
                _balanceFriction.set(response.data.balanceFriction);
                _autoCalcThickRatio.set(response.data.autoCalcThickRatio);
                _thickWeightRatio.set(response.data.thickWeightRatio);
                _autoOpenAfterScan.set(response.data.autoOpenAfterScan);
                _autoCloseOnScan.set(response.data.autoCloseOnScan);
                _waitForScanPedal.set(response.data.waitForScanPedal);
                _waitForPressure.set(response.data.waitForPressure);
                _autoOpenClose.set(response.data.autoOpenClose);
                _autoOpenMmDown.set(response.data.autoOpenMmDown);
                _lockGlassMove.set(response.data.lockGlassMove);
                _openAmountPercentage.set(response.data.openAmountPercentage);
                _openGlassSpeed.set(response.data.openGlassSpeed);
                _closeGlassSpeed.set(response.data.closeGlassSpeed);
                _glassAcceleration.set(response.data.glassAcceleration);
                _closingGlassPosition.set(response.data.closingGlassPosition);
                _workWithContact.set(response.data.workWithContact);
                _contactDistance.set(response.data.contactDistance);
                _enableFinePressure.set(response.data.enableFinePressure);
                _pressureAccuracy.set(response.data.pressureAccuracy);
                _upSpeed.set(response.data.upSpeed);
                _downSpeed.set(response.data.downSpeed);
                _stopScannerPressure.set(response.data.stopScannerPressure);
                break;
            case 'msd-get-book-cradle-contact-parameters':
                _thickness.set(response.data.thickness);
                _lightDistance.set(response.data.lightDistance);
                break;
            case 'msd-get-available-optical-resolutions':
                _opticalResolutionList.set(response.data.availableResolutions.join(';'));
                _opticalResolution.set(response.data.selectedResolutionIndex);
                break;
            case 'msd-get-light-schematics':
                _lightLeft.set(response.data.currentValues.light1);
                _lightCenterLeft.set(response.data.currentValues.light2);
                _lightCenterRight.set(response.data.currentValues.light3);
                _lightRight.set(response.data.currentValues.light4);
                break;
            case 'msd-get-current-icc-profile-selection':
                if(command.params.iccProfileType == 0) {
                    _iccProfileSourceList.set(response.data.iccProfiles.join(";"));
                    _iccProfileSource.set(response.data.selectedIccProfile);
                }
                else if (command.params.iccProfileType == 1) {
                    _iccProfileDestinationList.set(response.data.iccProfiles.join(";"));
                    _iccProfileDestination.set(response.data.selectedIccProfile);
                }
                else if(command.params.iccProfileType == 2) {
                    _iccProfileDisplayList.set(response.data.iccProfiles.join(";"));
                    _iccProfileDisplay.set(response.data.selectedIccProfile);
                }
            break;
        }
        await window.electron.database('db-log', {level:'DEBUG', text:'[CommandSender.svelte][runCallbackFn] Callback function completed for command: ' + command.name});
    }

</script>

<div id='command-sender'>
    <CommandSenderDialog {open} {loadingText} />
</div>

<style>
</style>