<script>
    import CircularProgressIndicator from '@components/common/CircularProgressIndicator.svelte';

    //smui
    import Dialog, { Title, Content } from '@smui/dialog';

    export let open;
    export let loadingText;
</script>

<div>
    <Dialog {open} scrimClickAction="">
        <Title>Comunicazione con lo scanner</Title>
        <Content>
            <CircularProgressIndicator {loadingText}/>
        </Content>
    </Dialog>
</div>

<style>
    * :global(.mdc-dialog .mdc-dialog__surface) {
        min-width: 30%;
        min-height: 0%;
        max-height: 25%;
    }

    * :global(.mdc-dialog__content) {
        display: flex;
        flex-direction: column;
    }
</style>