<script>
    import { _isMSDConnected } from "@store";
    import { notify } from "@utility";

    //smui components
    import Paper, { Title, Content } from '@smui/paper';
    import Button, { Label } from '@smui/button';

    const connectToMSD = async () => {
        await window.electron.database('db-log', {level:'DEBUG', text:'[MSDConnector.svelte][connectToMSD] sending command to connect to Metis Scan Director'});
        const response = await window.electron.metis("msd-connect");

        if (response.result == "OK"){
            _isMSDConnected.set(true);
        } else {
            _isMSDConnected.set(false);
            notify.error("Connessione a Metis Scan Director fallita. Aprire Metis e premere due volte l'icona con l'orecchio blu (deve diventare prima grigia e poi blu di nuovo)");
        }
    }
</script>

<div class="container">
    <Paper elevation=3>
        <Title>Attenzione</Title>
        <Content>
            <div class="flex-container">
                <Label><PERSON><PERSON> Director non è connesso, assicurarsi di aver avviato il programma e cliccare su "Connetti"</Label>
                <Button variant="raised" on:click={connectToMSD}>
                    <Label>Connetti</Label>
                </Button>
            </div>
        </Content>
    </Paper>
</div>


<style>

    .container {
        margin-top: 100px;
        align-self: center;
    }

    .flex-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 20px;
        padding-left: 20px;
        padding-right: 20px;
    }

</style>