<script>
    import { _cropArea, _cropLeftAreaPixels, _cropRightAreaPixels, _isDoublePageEnabled, _isEbraicOrder, _selectedJob, _nextSequenziale, _nomenclaturaSx, _nomenclaturaDx, _isReplaceEnabled, _selectedScan, _isInsertEnabled, _cropAreaPixels, _user, _prescanImageBase64, _scannerId, _progressioneSx, _progressioneDx } from "@store";
    import { notify, delay } from "@utility";
    import ScanProgressDialog from "@components/metis-scan-director/ScanProgressDialog.svelte";
    import * as d3 from "d3-selection";
    import { onMount } from "svelte";

    let open = false;
    let action;
    let status = "Inizializzazione";
    let progress = 0;

    const statusMap = {
        "Undefined": "Scan/Prescan non in corso",
        "Initializing": "Inizializzazione",
        "Acquiring": "Acquisizione",
        "Waiting for pressure/pedal" : "Attesa di pressione/pedale",
        "Processing": "Processamento",
        "Saving": "Salvataggio",
        "Last scan completed": "Completato",
        "Last scan interrupted by user": "Interrotto dall'utente",
        "Last scan interrupted by error": "Interrotto da un errore"
    }

    onMount(() => {
        d3.select('#scan-command-manager').on('start-fast-prescan', fastPrescanHandler);
        d3.select('#scan-command-manager').on('start-total-prescan', totalPrescanHandler);
        d3.select('#scan-command-manager').on('start-scan', scanHandler);
    })

    const fastPrescanHandler = async () => {
        await window.electron.database('db-log', {level:'DEBUG', text:'[ScanCommandManager.svelte][fastPrescanHandler] Starting fast prescan'});
        
        /* Reset progress and open dialog */
        action = 'fast-prescan';
        status = "Inizializzazione";
        progress = 0;
        open = true;

        let params = {};
        params.topLeftX = $_cropAreaPixels.x;
        params.topLeftY = $_cropAreaPixels.y;
        params.bottomRightX = $_cropAreaPixels.x + $_cropAreaPixels.width;
        params.bottomRightY = $_cropAreaPixels.y + $_cropAreaPixels.height;

        await window.electron.database('db-log', {level:'DEBUG', text:'[ScanCommandManager.svelte][fastPrescanHandler] Setting prescan crop area'});
        //NOTE - commented for develop
        //await window.electron.metis("msd-set-prescan-crop-area", params);
        await startPrescan();
    }

    const totalPrescanHandler = async () => {
        await window.electron.database('db-log', {level:'DEBUG', text:'[ScanCommandManager.svelte][totalPrescanHandler] Starting total prescan'});
        
        /* Reset progress and open dialog */
        action = 'total-prescan';
        status = "Inizializzazione";
        progress = 0;
        open = true;

        let params = {};
        params.topLeftX = 0;
        params.topLeftY = 0;
        params.bottomRightX = 0;
        params.bottomRightY = 0;

        await window.electron.database('db-log', {level:'DEBUG', text:'[ScanCommandManager.svelte][totalPrescanHandler] Setting prescan crop area to full image'});
        //NOTE - commented for develop
        //await window.electron.metis("msd-set-prescan-crop-area", params);
        await startPrescan();
    }

    const startPrescan = async () => {
        /* Send command */
        const metisResponse = await sendCommand('msd-start-prescan');
        
        if(metisResponse != 'OK') {
            await handlePrescanFail();
            open=false;
            return;
        }

        /* Check progress until it's done */
        const checkProgress = setInterval(async () => {
            const acquisitionResponse = await window.electron.metis("msd-get-acquisition-progress");

            if (acquisitionResponse.data == 'Last scan completed') {
                status = statusMap[acquisitionResponse.data];
                clearInterval(checkProgress);

                await handlePrescanSuccess();
                open = false;
                return;
            } else if(acquisitionResponse.data == 'Undefined' || acquisitionResponse.data == 'Last scan interrupted by user' || acquisitionResponse.data == 'Last scan interrupted by error'){
                status = statusMap[acquisitionResponse.data];
                open = false;
                clearInterval(checkProgress);

                await handlePrescanFail();
                return;
            }

            status = statusMap[acquisitionResponse.data];
            const progressResponse = await window.electron.metis("msd-get-scan-progress");
            progress = progressResponse.data / 100;
        }, 2000);
    }

    const scanHandler = async () => {
        /* Perform preliminary scan checks */
        if(!preliminaryScanCheck()){
            return;
        }

        /* Reset progress and open dialog */
        action = 'scan';
        status = "Inizializzazione";
        progress = 0;
        open = true;

        /* Perform database operations and get inserted/modified record Id */
        const databaseId = await addScanToDatabase();
        
        /* Build file path with record Id */
        const filePath = await window.electron.filesystem("fs-join-path", [ $_selectedJob.scan_path, databaseId.toString() ]);

        /* NOTE - modified for develop */
        // await setFilenamesAndPaths(filePath);
        //await startScan(databaseId);
        await handleScanSuccess(databaseId);
        open = false;
    }

    const startScan = async (databaseId) => {
        /* Send command */
        const metisResponse = await sendCommand('msd-start-scan');
        
        if(metisResponse != 'OK') {
            await handleScanFail(databaseId);
            open = false;
            return;
        }

        /* Check progress until it's done */
        const checkProgress = setInterval(async () => {
            const acquisitionResponse = await window.electron.metis("msd-get-acquisition-progress");
            await window.electron.database('db-log', {level:'DEBUG', text:'[ScanCommandManager.svelte][checkProgress] acquisitionResponse: ' + acquisitionResponse.data});

            if (acquisitionResponse.data == 'Last scan completed') {
                status = statusMap[acquisitionResponse.data];
                clearInterval(checkProgress);

                await handleScanSuccess(databaseId);
                open = false;
                return;
            } else if(acquisitionResponse.data == 'Undefined' || acquisitionResponse.data == 'Last scan interrupted by user' || acquisitionResponse.data == 'Last scan interrupted by error'){
                status = statusMap[acquisitionResponse.data];
                clearInterval(checkProgress);

                await handleScanFail(databaseId);
                open = false;
                return;
            }

            status = statusMap[acquisitionResponse.data];
            const progressResponse = await window.electron.metis("msd-get-scan-progress");
            progress = progressResponse.data / 100;
        }, 2000);
    }

    const preliminaryScanCheck = () => {
        return true;
    }

    const addScanToDatabase = async () => {
        /* Salva dati scansione a DB e prendi id inserito */
        const paramsArr = [];

        /* Add to DB and build the path where to create the Tif file pointing to inserted/modified Id */
        if(!$_isReplaceEnabled && !$_isInsertEnabled){
            /* Prepare input array */
            paramsArr.push($_selectedJob.id_digit);
            paramsArr.push($_nextSequenziale);
            paramsArr.push($_selectedJob.identifier);
            paramsArr.push($_isDoublePageEnabled);
            paramsArr.push($_isEbraicOrder);
            paramsArr.push($_progressioneSx);
            paramsArr.push($_nomenclaturaSx.ordinatore.metis_code);
            paramsArr.push($_nomenclaturaSx.ordinatore.name_short);
            paramsArr.push($_nomenclaturaSx.progressioneIndex);
            paramsArr.push($_nomenclaturaSx.letteraPagina || '');
            paramsArr.push($_nomenclaturaSx.letteraPaginaEnd || '');
            paramsArr.push($_nomenclaturaSx.prefissoNumeroPagina || '');
            paramsArr.push($_nomenclaturaSx.numeroPagina || 0);
            paramsArr.push($_nomenclaturaSx.numeroPaginaEnd || 0);
            paramsArr.push($_nomenclaturaSx.internoPagina || 0);
            paramsArr.push($_nomenclaturaSx.tipoPagina);
            paramsArr.push($_nomenclaturaSx.eccezione ? $_nomenclaturaSx.eccezione.metis_code : null);
            paramsArr.push($_nomenclaturaSx.eccezione ? $_nomenclaturaSx.eccezione.name_short : null);
            paramsArr.push($_nomenclaturaSx.eccezione ? $_nomenclaturaSx.eccezione.valore : null);
            paramsArr.push($_nomenclaturaSx.eccezione ? $_nomenclaturaSx.eccezione.parte : null);
            paramsArr.push($_nomenclaturaSx.displayName);
            paramsArr.push($_isDoublePageEnabled ? $_progressioneDx : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.ordinatore.metis_code : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.ordinatore.name_short : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.progressioneIndex : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.letteraPagina || '' : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.letteraPaginaEnd || '' : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.prefissoNumeroPagina || '' : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.numeroPagina || 0 : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.numeroPaginaEnd || 0 : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.internoPagina || 0 : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.tipoPagina : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.eccezione ? $_nomenclaturaDx.eccezione.metis_code : null : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.eccezione ? $_nomenclaturaDx.eccezione.name_short : null : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.eccezione ? $_nomenclaturaDx.eccezione.valore : null : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.eccezione ? $_nomenclaturaDx.eccezione.parte : null : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.displayName : '');
            paramsArr.push($_isDoublePageEnabled ? $_cropLeftAreaPixels.width : $_cropAreaPixels.width);
            paramsArr.push($_isDoublePageEnabled ? $_cropLeftAreaPixels.height : $_cropAreaPixels.height);

            /* Add scan to DB */
            await window.electron.database('db-log', {level:'DEBUG', text:'[ScanCommandManager.svelte][addScanToDatabase] adding scan record on database'});
            const response = await window.electron.database("db-add-scansione", paramsArr);

            /* Return inserted record Id */
            return response.insertId;
        } else if($_isReplaceEnabled){
            await window.electron.database('db-log', {level:'INFO', text:'[ScanCommandManager.svelte][addScanToDatabase] Replace mode is enabled for this acquisition'});

            /* update scan dimensions with new crop area dimensions */
            let params = {};
            params.cropAreaWidthPixels = $_isDoublePageEnabled ? $_cropLeftAreaPixels.width : $_cropAreaPixels.width;
            params.cropAreaHeightPixels = $_isDoublePageEnabled ? $_cropLeftAreaPixels.height : $_cropAreaPixels.height;
            params.id = $_selectedScan.id;

            await window.electron.database("db-update-dimensioni-scansione", params);

            /* Return modified record Id */
            return $_selectedScan.id;
        } else if($_isInsertEnabled){
            await window.electron.database('db-log', {level:'INFO', text:'[ScanCommandManager.svelte][addScanToDatabase] Insert mode is enabled for this acquisition'});

            /* Increase sequenziale for all scans starting from the selected one (included) */
            let params = {};
            params.idDigit = $_selectedJob.id_digit;
            params.sequenzialeScansione = $_selectedScan.sequenziale_scansione - 1;
            params.step = 1;
            await window.electron.database("db-increase-seq-scansioni", params);

            /* Prepare input array with sequenziale equal to the selected one */
            paramsArr.push($_selectedJob.id_digit);
            paramsArr.push($_selectedScan.sequenziale_scansione);
            paramsArr.push($_selectedJob.identifier);
            paramsArr.push($_isDoublePageEnabled);
            paramsArr.push($_isEbraicOrder);
            paramsArr.push($_progressioneSx);
            paramsArr.push($_nomenclaturaSx.ordinatore.metis_code);
            paramsArr.push($_nomenclaturaSx.ordinatore.name_short);
            paramsArr.push($_nomenclaturaSx.progressioneIndex);
            paramsArr.push($_nomenclaturaSx.letteraPagina || '');
            paramsArr.push($_nomenclaturaSx.letteraPaginaEnd || '');
            paramsArr.push($_nomenclaturaSx.prefissoNumeroPagina || '');
            paramsArr.push($_nomenclaturaSx.numeroPagina || 0);
            paramsArr.push($_nomenclaturaSx.numeroPaginaEnd || 0);
            paramsArr.push($_nomenclaturaSx.internoPagina || 0);
            paramsArr.push($_nomenclaturaSx.tipoPagina);
            paramsArr.push($_nomenclaturaSx.eccezione ? $_nomenclaturaSx.eccezione.metis_code : null);
            paramsArr.push($_nomenclaturaSx.eccezione ? $_nomenclaturaSx.eccezione.name_short : null);
            paramsArr.push($_nomenclaturaSx.eccezione ? $_nomenclaturaSx.eccezione.valore : null);
            paramsArr.push($_nomenclaturaSx.eccezione ? $_nomenclaturaSx.eccezione.parte : null);
            paramsArr.push($_nomenclaturaSx.displayName);
            paramsArr.push($_isDoublePageEnabled ? $_progressioneDx : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.ordinatore.metis_code : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.ordinatore.name_short : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.progressioneIndex : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.letteraPagina || '' : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.letteraPaginaEnd || '' : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.prefissoNumeroPagina || '' : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.numeroPagina || 0 : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.numeroPaginaEnd || 0 : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.internoPagina || 0 : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.tipoPagina : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.eccezione ? $_nomenclaturaDx.eccezione.metis_code : null : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.eccezione ? $_nomenclaturaDx.eccezione.name_short : null : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.eccezione ? $_nomenclaturaDx.eccezione.valore : null : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.eccezione ? $_nomenclaturaDx.eccezione.parte : null : null);
            paramsArr.push($_isDoublePageEnabled ? $_nomenclaturaDx.displayName : '');
            paramsArr.push($_isDoublePageEnabled ? $_cropLeftAreaPixels.width : $_cropAreaPixels.width);
            paramsArr.push($_isDoublePageEnabled ? $_cropLeftAreaPixels.height : $_cropAreaPixels.height);

            /* Add scan to DB */
            await window.electron.database('db-log', {level:'DEBUG', text:'[ScanCommandManager.svelte][addScanToDatabase] adding scan record on database'});
            const response = await window.electron.database("db-add-scansione", paramsArr);

            /* Return inserted record Id */
            return response.insertId;
        }
    }

    const setFilenamesAndPaths = async (filePath) => {
        let params = {};

        if($_isDoublePageEnabled){
            params.cropAreas = [
                { topLeftX: $_cropLeftAreaPixels.x, topLeftY: $_cropLeftAreaPixels.y, bottomRightX: $_cropLeftAreaPixels.x + $_cropLeftAreaPixels.width, bottomRightY: $_cropLeftAreaPixels.y + $_cropLeftAreaPixels.height, path: filePath + '_0' },
                { topLeftX: $_cropRightAreaPixels.x, topLeftY: $_cropRightAreaPixels.y, bottomRightX: $_cropRightAreaPixels.x + $_cropRightAreaPixels.width, bottomRightY: $_cropRightAreaPixels.y + $_cropRightAreaPixels.height, path: filePath + '_1' }
            ];
            await window.electron.database('db-log', {level:'INFO', text:'[ScanCommandManager.svelte][setFilenamesAndPaths] first file path: ' + filePath + '_0.tif'});
            await window.electron.database('db-log', {level:'INFO', text:'[ScanCommandManager.svelte][setFilenamesAndPaths] second file path: ' + filePath + '_1.tif'});
        } else {
            params.cropAreas = [
                { topLeftX: $_cropAreaPixels.x, topLeftY: $_cropAreaPixels.y, bottomRightX: $_cropAreaPixels.x + $_cropAreaPixels.width, bottomRightY: $_cropAreaPixels.y + $_cropAreaPixels.height, path: filePath + '_0' }
            ];
            await window.electron.database('db-log', {level:'INFO', text:'[ScanCommandManager.svelte][setFilenamesAndPaths] double page is disabled, unique file path: ' + filePath + '_0'});
        }

        await window.electron.database('db-log', {level:'DEBUG', text:'[ScanCommandManager.svelte][setFilenamesAndPaths] setting file names and paths'});
        await window.electron.metis("msd-set-crops-filename-paths", params);
        await delay(1500);
    }

    const sendCommand = async (command) => {
        await window.electron.database('db-log', {level:'DEBUG', text:'[ScanCommandManager.svelte][sendCommand] Sending command: ' + command});
        const response = await window.electron.metis(command);
        await window.electron.database('db-log', {level:'DEBUG', text:'[ScanCommandManager.svelte][sendCommand] ' + command + ' result is: ' + response.result});

        return response.result;
    }

    const handlePrescanFail = async () => {
        /* Show error toast */
        notify.error("Si è verificato un errore durante la prescansione");
    }
    
    const handleScanFail = async (databaseId) => {
        /* Show error toast */
        notify.error("Si è verificato un errore durante la scansione");

        /* If not replace mode, delete scan DB record */
        if(!$_isReplaceEnabled){
            await window.electron.database("db-delete-scansione", { id: databaseId });

            /* If insert mode, decrease all sequenziali to restore correct order */
            if($_isInsertEnabled){
                let params = {};
                params.idDigit = $_selectedJob.id_digit;
                params.sequenzialeScansione = $_selectedScan.sequenziale_scansione;
                params.step = 1;
                await window.electron.database("db-decrease-seq-scansioni", params);
            }
        }
    }

    const handlePrescanSuccess = async () => {
        const response = await window.electron.metis("msd-get-preview-image-data");

        if(response.result == 'OK') {
            /* Update prescan image to trigger mainViewer refresh if image if new, otherwise await and call it again */
            if(response.data != $_prescanImageBase64)
                _prescanImageBase64.set(response.data);
            else {
                await window.electron.database('db-log', {level:'INFO', text:'[ScanCommandManager.svelte][handlePrescanSuccess] Prescan image not updated, waiting and calling metis again'});
                await delay(1000);
                await handlePrescanSuccess();
            }
        } else {
            notify.error("Si è verificato un errore durante la creazione dell'immagine di prescan");
        }
    }

    const handleScanSuccess = async (databaseId) => {
        /* Save job settings */
        d3.select("#acquisizione").dispatch('scan-completed');

        /* Delete old preview if is replacing */
        if($_isReplaceEnabled){
            const imageToDeletePath = await window.electron.filesystem("fs-join-path", [ $_selectedJob.preview_path, databaseId.toString() ]);
            await window.electron.filesystem("fs-delete-file-or-dir", imageToDeletePath + '_0.dzi');
            await window.electron.filesystem("fs-delete-file-or-dir", imageToDeletePath + '_0_files');
            await window.electron.filesystem("fs-delete-file-or-dir", imageToDeletePath + '_1.dzi');
            await window.electron.filesystem("fs-delete-file-or-dir", imageToDeletePath + '_1_files');
        }

        /* NOTE - commented for develop */
        /* Create previews to be shown in preview visualizer */
        //await window.electron.worker('create-scan-preview', {scanId: databaseId, isDoublePage: $_isDoublePageEnabled, scanPath: $_selectedJob.scan_path, previewPath: $_selectedJob.preview_path});

        /* Refresh scan table and select the created/modified scan */
        d3.select('#scan-visualizer-table').dispatch('refresh-scan-table', {detail: {scanId: databaseId}});

        /* Save crop area position */
        await saveCropAreaPosition();

        /* Save crop_risguardia in inside if ordinatore is ba_controguardia.anteriore o be_contropiatto.anteriore */
        if($_nomenclaturaSx.ordinatore?.metis_code == 33 || $_nomenclaturaSx.ordinatore?.metis_code == 36) {
            const params = {
                idDigit: $_selectedJob.id_digit,
                cropAreaPixels: $_isDoublePageEnabled ? $_cropLeftAreaPixels : $_cropAreaPixels
            }

            await window.electron.database('db-save-crop-area-risguardia', params);
        }

        /* Insert event log into database */
        await insertEventLog();

        /* Update Nomenclature automatically for next scan */
        if (!$_isReplaceEnabled)
            d3.select('#nomenclature').dispatch('scan-completed');

        /* remove special acquisition modes when scan is completed */
        if($_isInsertEnabled) d3.select('#scan-visualizer-table-tools').dispatch('scan-insert-completed');
        if($_isReplaceEnabled) d3.select('#scan-visualizer-table-tools').dispatch('scan-replace-completed');

        /* Refresh prescan image */
        /* NOTE - commented for develop */
        //await handlePrescanSuccess();
    }

    const saveCropAreaPosition = async () => {
        await window.electron.database('db-log', {level:'DEBUG', text:'[ScanCommandManager.svelte][saveCropAreaPosition] Saving crop position'});

        const params = {
            idDigit: $_selectedJob.id_digit,
            cropArea: $_cropArea.rect
        }

        await window.electron.database('db-save-crop-area-position', params);
    }

    const insertEventLog = async () => {
        if(!$_isInsertEnabled && !$_isReplaceEnabled){
            /* Log Event for normal scan */
            let eventText = '';
            eventText += `[${new Date().toLocaleString('it-IT').replace(',', '')}]\n`;
            eventText += `Evento: Acquisizione Immagine\n`;
            eventText += `Gruppo: ${$_user.company} - Operatore: ${$_user.displayName || $_user.username} - Livello: ${$_user.profile}\n`;
            eventText += `ID : ${$_scannerId}\n`;
            eventText += `Indice acquisizione #${$_nextSequenziale}\n`;
            eventText += $_isDoublePageEnabled ? `${$_nomenclaturaSx.displayName}, ${$_nomenclaturaDx.displayName}\n\n` : `${$_nomenclaturaSx.displayName}\n\n`;
            await window.electron.database('db-log', {level:'EVENT', text: eventText, eventType: 'Acquisizione Immagine'});
        } else if($_isReplaceEnabled) {
            /* Log Event for scan with replace mode */
            let eventText = '';
            eventText += `[${new Date().toLocaleString('it-IT').replace(',', '')}]\n`;
            eventText += `Evento: Sostituzione Immagine\n`;
            eventText += `Gruppo: ${$_user.company} - Operatore: ${$_user.displayName || $_user.username} - Livello: ${$_user.profile}\n`;
            eventText += `ID : ${$_scannerId}\n`;
            eventText += `Indice acquisizione #  (sostituisce indice  ${$_selectedScan.sequenziale_scansione} )\n`;
            eventText += $_isDoublePageEnabled ? `${$_nomenclaturaSx.displayName}, ${$_nomenclaturaDx.displayName}\n\n` : `${$_nomenclaturaSx.displayName}\n\n`;
            await window.electron.database('db-log', {level:'EVENT', text: eventText, eventType: 'Sostituzione Immagine'});
        } else if($_isInsertEnabled) {
            /* Log Event for scan with insert mode */
            let eventText = '';
            eventText += `[${new Date().toLocaleString('it-IT').replace(',', '')}]\n`;
            eventText += `Evento: Inserimento Immagine\n`;
            eventText += `Gruppo: ${$_user.company} - Operatore: ${$_user.displayName || $_user.username} - Livello: ${$_user.profile}\n`;
            eventText += `ID : ${$_scannerId}\n`;
            eventText += `Indice acquisizione # ${$_nextSequenziale} (inserita prima di ${$_selectedScan.sequenziale_scansione} )\n`;
            eventText += $_isDoublePageEnabled ? `${$_nomenclaturaSx.displayName} (inserita prima di ${$_selectedScan.display_name_sx}), ${$_nomenclaturaDx.displayName} (inserita prima di ${$_selectedScan.display_name_dx})\n\n` : `${$_nomenclaturaSx.displayName} (inserita prima di ${$_selectedScan.display_name_sx})\n\n`;
            await window.electron.database('db-log', {level:'EVENT', text: eventText, eventType: 'Inserimento Immagine'});
        }
    }
</script>

<div id='scan-command-manager'>
    <ScanProgressDialog {open} {action} {status} {progress}/>
</div>

<style>
</style>