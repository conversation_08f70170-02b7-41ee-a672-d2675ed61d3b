<script>
    //smui
    import Dialog, { Title, Content } from '@smui/dialog';
    import Button, { Label } from '@smui/button';
    import LinearProgress from '@smui/linear-progress';

    export let open;
    export let action;
    export let status;
    export let progress;

    $: dialogTitle = () => {
        let title;
        switch (action) {
            case 'fast-prescan':
                title = 'Prescan Veloce';
                break;
            case 'total-prescan':
                title = 'Prescan Totale';
                break;
            case 'scan':
                title = 'Scansione';
                break;
            default:
                break;
        }
        return title;
    }

    const stopScan = async () => {
        await window.electron.database('db-log', {level:'DEBUG', text:'[ScanProgressDialog.svelte][stopScan] Stopping acquisition'});
        await window.electron.metis("msd-stop-acquisition");
    }
</script>

<div>
    <Dialog {open} scrimClickAction="">
        <Title>{dialogTitle()}</Title>
        <Content>
            <div class="flex-container">
                <div class="flex-row">
                    <LinearProgress {progress} />
                    <p>{Math.round(progress * 100)}%</p>
                </div>
                <p>{status}</p>
            </div>
        </Content>
        <div class="flex-button-row">
            <Button on:click={stopScan} variant="raised">
                <Label>Stop</Label>
            </Button>
        </div>
    </Dialog>
</div>

<style>
    .flex-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 20px;
    }

    .flex-row{
        width: 100%;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    p {
        margin: 0px;
        color: var(--mdc-theme-secondary);
    }

    .flex-button-row {
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }

    * :global(.mdc-dialog .mdc-dialog__surface) {
        min-width: 40%;
        min-height: 0%;
        max-height: 25%;
    }

    * :global(.mdc-dialog__content) {
        display: flex;
        flex-direction: column;
    }
</style>