<script>
    import { _selectedJob } from '@store';
    
    export let isFromSettings = false;
</script>

<textarea class:isFromSettings bind:value={$_selectedJob.note_acquisizione} placeholder="Nessuna nota di acquisizione inserita..."></textarea>


<style>
    textarea{
        background-color: transparent;
        color: var(--mdc-theme-secondary);
        border-color: var(--mdc-theme-secondary);
        min-height: 250px;
        min-width: calc(100% - 24px);
        border-radius: 3px;
        padding: 10px;
    }

    textarea.isFromSettings {
        min-height: 100px;
    }
    
    textarea:focus {
        outline: none !important;
        border-color: var(--mdc-theme-primary);
    }

    textarea::-webkit-scrollbar {
        width: 8px;
    }

    textarea::-webkit-scrollbar-track {
        background-color: white;
        border-radius: 10px;
    }

    textarea::-webkit-scrollbar-thumb {
        background-color: var(--mdc-theme-primary);
        border-radius: 10px;
    }
</style>