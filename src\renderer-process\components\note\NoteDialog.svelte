<script>
    import { _isShowingNote, _activeTabNote, _user, _selectedJob } from '@store';
    import NoteTab from '@components/note/NoteTab.svelte';
    import { notify } from '@utility';

    //smui
    import Dialog, { Title, Content } from '@smui/dialog';
    import Button, { Label } from '@smui/button';
   
    const handleSave = async () => {
        let params = {
            idDigit: $_selectedJob.id_digit
        };

        switch ($_activeTabNote) {
            case 'Acquisizione':
                await window.electron.database('db-log', {level:'DEBUG', text:'[NoteDialog.svelte][handleSave] Updating note acquisizione'});
                params.column = 'note_acquisizione';
                params.value = $_selectedJob?.note_acquisizione || null
                break;
            case 'Digitalizzazione':
                await window.electron.database('db-log', {level:'DEBUG', text:'[NoteDialog.svelte][handleSave] Updating note digitalizzazione'});
                params.column = 'note_fotografico';
                params.value = $_selectedJob?.note_digitalizzazione || null
                break;
            case 'Validazione':
                await window.electron.database('db-log', {level:'DEBUG', text:'[NoteDialog.svelte][handleSave] Updating note validazione'});
                params.column = 'note_validazione';
                params.value = $_selectedJob?.note_validazione || null
                break;
            default:
                break;
        }
        
        const response = await window.electron.database('db-update-note-job', params);

        if(response.affectedRows > 0){
            notify.success("Note aggiornate correttamente");
        } else {
            notify.error("Errore durante l'aggiornamente delle note");
        }
    }

    const handleClose = () => {
        _isShowingNote.set(false);
    }
</script>

<div>
    <Dialog bind:open={$_isShowingNote} scrimClickAction="">
        <Title>Note</Title>
        <Content>
            <NoteTab />
        </Content>
        <div class="flex-row-buttons">
            {#if $_activeTabNote == 'Acquisizione' || (($_activeTabNote == 'Digitalizzazione' || $_activeTabNote == 'Validazione') && ($_user?.profile == 'Tutor' || $_user?.profile == 'Supertutor'))}
                <Button on:click={handleSave} variant="raised">
                    <Label>Salva</Label>
                </Button>
            {/if}
            <Button on:click={handleClose} variant="raised" color="secondary">
                <Label>Chiudi</Label>
            </Button>
        </div>
    </Dialog>
</div>

<style>
    .flex-row-buttons {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 10px;
    }

    * :global(.mdc-dialog .mdc-dialog__surface) {
        min-width: 60%;
        min-height: 50%;
    }

    * :global(.mdc-dialog__content) {
        display: flex;
        flex-direction: column;
    }
</style>