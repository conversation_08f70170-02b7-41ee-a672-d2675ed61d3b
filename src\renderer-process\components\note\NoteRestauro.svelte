<script>
    import { _selectedJob } from '@store';
</script>

<div>
    {#if $_selectedJob?.note_restauro}
        {#each $_selectedJob.note_restauro.split(';') as nota}
            <div><span class="name">{nota.substring(0, nota.indexOf(':')+1)}</span><span class="value">{nota.substring(nota.indexOf(':')+1)}</span></div>
        {/each}
    {:else}
        <div><span class="value">Nesusna nota inserita dal restauro</span></div>
    {/if}
</div>

<style>
    .name {
        color: var(--mdc-theme-primary);
    }

    .value {
        color: var(--mdc-theme-secondary);
    }
</style>