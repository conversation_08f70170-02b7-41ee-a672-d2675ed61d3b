<script>
    import * as d3 from 'd3-selection';

    export let scanObj;

    const handleSave = async () => {
        await window.electron.database('db-update-note-scansione', { id: scanObj.id, notes: scanObj.testo_nota });
        d3.select('#preview-visualizer').dispatch('refresh-scan-table');
    }
</script>

{#if scanObj}
    <div class="flex-column">
        <p>Note scansione:</p>
        <textarea bind:value={scanObj.testo_nota} placeholder="Nessuna nota ancora inserita..." on:change={handleSave}></textarea>
    </div>
{/if}

<style>
    .flex-column {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    p {
        color: var(--mdc-theme-secondary);
        font-size: 1.2rem;
        margin: 0;
    }

    textarea{
        background-color: transparent;
        color: var(--mdc-theme-secondary);
        border-color: var(--mdc-theme-secondary);
        min-height: 100px;
        min-width: calc(100% - 24px);
        border-radius: 3px;
        padding: 10px;
    }
    
    textarea:focus {
        outline: none !important;
        border-color: var(--mdc-theme-primary);
    }

    textarea::-webkit-scrollbar {
        width: 8px;
    }

    textarea::-webkit-scrollbar-track {
        background-color: white;
        border-radius: 10px;
    }

    textarea::-webkit-scrollbar-thumb {
        background-color: var(--mdc-theme-primary);
        border-radius: 10px;
    }
</style>