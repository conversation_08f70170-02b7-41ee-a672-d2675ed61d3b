<script>
    import { _activeTabNote, _selectedJob } from '@store';
    import NoteRestauro from '@components/note/NoteRestauro.svelte';
    import NoteAcquisizione from '@components/note/NoteAcquisizione.svelte';
    import NoteDigitalizzazione from '@components/note/NoteDigitalizzazione.svelte';
    import NoteValidazione from '@components/note/NoteValidazione.svelte';
    import NotePalinsesto from '@components/note/NotePalinsesto.svelte';

    //smui 
    import Tab, { Label } from '@smui/tab';
    import TabBar from '@smui/tab-bar';
   
    let tabs = $_selectedJob?.is_palinsesto ? ['Restauro', 'Acquisizione', 'Digitalizzazione', 'Validazione', 'Palinsesto'] : ['Restauro', 'Acquisizione', 'Digitalizzazione', 'Validazione'];
</script>

<div class="tab-container">
    <TabBar tabs={tabs} let:tab bind:active={$_activeTabNote}>
        <Tab {tab}>
        <Label>{tab}</Label>
        </Tab>
    </TabBar>

    {#if $_activeTabNote == 'Restauro'}
        <NoteRestauro />
    {:else if $_activeTabNote == 'Acquisizione'}
        <NoteAcquisizione />
    {:else if $_activeTabNote == 'Digitalizzazione'}
        <NoteDigitalizzazione />
    {:else if $_activeTabNote == 'Validazione'}
        <NoteValidazione />
    {:else if $_activeTabNote == 'Palinsesto'}
        <NotePalinsesto />
    {/if}
</div>

<style>
    .tab-container {
        display: flex;
        flex-direction: column;
        gap: 20px;
        overflow: hidden;
    }

    * :global(.mdc-tab-bar) {
        overflow: hidden;
        flex-shrink: 0;
    }
</style>