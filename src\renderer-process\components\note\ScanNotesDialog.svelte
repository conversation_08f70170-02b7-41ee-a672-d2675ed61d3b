<script>
    //smui
    import Dialog, { Title, Content } from '@smui/dialog';
    import Button, { Label } from '@smui/button';

    export let openScanNotesDialog = false;
    export let scanObj;
   
    const handleClose = () => {
        openScanNotesDialog = false;
    }

    const handleSave = async () => {
        await window.electron.database('db-update-note-scansione', { id: scanObj.id, notes: scanObj.testo_nota });
        openScanNotesDialog = false;
    }
</script>

<div>
    <Dialog bind:open={openScanNotesDialog} scrimClickAction="">
        <Title>Note scansione</Title>
        <Content>
            {#if scanObj}
                <textarea bind:value={scanObj.testo_nota} placeholder="Nessuna nota ancora inserita, scrivi qui e clicca salva..."></textarea>
            {/if}
        </Content>
        <div class="flex-row-buttons">
            <Button on:click={handleSave} variant="raised">
                <Label>Salva</Label>
            </Button>
            <Button on:click={handleClose} variant="raised" color={'secondary'}>
                <Label>Chiudi</Label>
            </Button>
        </div>
    </Dialog>
</div>

<style>
    .flex-row-buttons {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 10px;
    }

    textarea{
        background-color: transparent;
        color: var(--mdc-theme-secondary);
        border-color: var(--mdc-theme-secondary);
        min-height: 200px;
        border-radius: 3px;
        padding: 10px;
    }
    
    textarea:focus {
        outline: none !important;
        border-color: var(--mdc-theme-primary);
    }

    * :global(.mdc-dialog .mdc-dialog__surface) {
        min-width: 40%;
        min-height: 40%;
    }

    * :global(.mdc-dialog__content) {
        display: flex;
        flex-direction: column;
    }
</style>