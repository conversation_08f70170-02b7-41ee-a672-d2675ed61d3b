<script>
  import { _mainViewer, _cropArea, _cropCentralArea, _cropCentralMargin, _respectMargin, _isCropLocked, _isDoublePageEnabled, _isSettingColors, _cropLeftAreaPixels, _cropRightAreaPixels, _nomenclaturaSx, _nomenclaturaDx, _selectedJob, _cropAreaPixels, _colorPatches, _isEbraicOrder, _cropCentralAreaPixels, _isEditingScan, _isSettingsFormOpen, _mainViewerDidMount, _shouldUpdateSavedDimensions, _savedCentralMargin, _savedRespectMargin, _savedCropAreaPixels, _mainViewerBounds, _prescanImageBase64 } from "@store";
  import MainViewersToolbar from "@components/open-sea-dragon/MainViewersToolbar.svelte";
  import { delay } from '@utility';
  import { onMount } from "svelte";
  import * as d3 from "d3-selection";
  import Mousetrap from '@ottozz/mousetrap';

  let viewer, overlay;

  let selectionOptions = {
    showSelectionControl: false,
    showConfirmDenyButtons: false,
    allowRotation: false,
    restrictToImage: true,
    keyboardShortcut: null,
    rect: new OpenSeadragon.SelectionRect($_cropArea.rect.x, $_cropArea.rect.y, $_cropArea.rect.width, $_cropArea.rect.height),
    handleStyle: {
      background: '#ff3e00',
      border: '1px solid #ff3e00'
    },
    cornersStyle: {
      background: '#ff3e00',
      border: '1px solid #ff3e00'
    }
  }

  onMount(async () => {
    viewer = OpenSeadragon({
        id: "main-viewer",
        tileSources: {
            type: 'image',
            url: $_prescanImageBase64,
        },
        showNavigationControl: false,
        visibilityRatio: 0.8,
        maxZoomLevel: 20,
        constrainDuringPan: true,
        gestureSettingsMouse: { clickToZoom: false },
        preserveViewport: true
    });

    _mainViewer.set(viewer);

    /* init Crop Area */
    const cropAreaElt = document.createElement('div');
    cropAreaElt.id = "crop-area";
    cropAreaElt.className = "overlay-crop-area";

    selectionOptions.element = cropAreaElt;
    _cropArea.set(viewer.selection(selectionOptions));

    /* init overlay plugin */
    overlay = viewer.HTMLelements();

    /* Perform first updates of all cropAreas and Overlays */
    /* Subsequent upates will be called in drag and dragEnd handlers defined below */
    await delay(1000);
    getCropAreasDimensions();
    updateLeftAndRightCropAreaDimensions();
    updateRespectMargin();

    /* Create drag and click handlers to update viewers */
    createHandlers();
    Mousetrap.bind('ctrl+left', () => moveCropArea('left'));
    Mousetrap.bind('ctrl+right', () => moveCropArea('right'));
    Mousetrap.bind('ctrl+up', () => moveCropArea('up'));
    Mousetrap.bind('ctrl+down', () => moveCropArea('down'));

    /* Create event listeners on the main viewer div for external components */
    createEventListeners();

    /* Init zoom and position */
    if($_mainViewerBounds){
      viewer.viewport.fitBounds($_mainViewerBounds, true);
      _mainViewerBounds.set(null);
    }

    /* set cropArea handles cursor */
    manageCropAreaCursors();

    /* Grid Overlay */
    createGridOverlay();

    /* Update viewer on image change */
    _prescanImageBase64.subscribe(async (value) => {
      if(viewer && value) {
        viewer.close();
        await delay(100);
        viewer.open({type: 'image', url: value});
      }
    })

    _mainViewerDidMount.set(true);
  });

  const getCropAreasDimensions = () => {
    /* Get the crop rect dimensions in pixels */
    const cropAreaRect = viewer.viewport.viewportToImageRectangle($_cropArea.rect)
    cropAreaRect.x = Math.round(cropAreaRect.x);
    cropAreaRect.y = Math.round(cropAreaRect.y);
    cropAreaRect.width = Math.round(cropAreaRect.width);
    cropAreaRect.height = Math.round(cropAreaRect.height);
    _cropAreaPixels.set(cropAreaRect);
  }

  const updateRespectMargin = () => {
    //reset Respect Margin before recalculating
    if($_cropArea.rect.respectMargin) {
      $_cropAreaPixels.x += $_cropArea.rect.respectMargin;
      $_cropAreaPixels.y += $_cropArea.rect.respectMargin;
      $_cropAreaPixels.width -= 2*$_cropArea.rect.respectMargin;
      $_cropAreaPixels.height -= 2*$_cropArea.rect.respectMargin;
    }

    //increase crop area by Respect Margin
    $_cropAreaPixels.x -= $_respectMargin;
    $_cropAreaPixels.y -= $_respectMargin;
    $_cropAreaPixels.width += 2*$_respectMargin;
    $_cropAreaPixels.height += 2*$_respectMargin;

    //get the main rect in viewport coordinates
    let cropRect = viewer.viewport.imageToViewportRectangle($_cropAreaPixels.x, $_cropAreaPixels.y, $_cropAreaPixels.width, $_cropAreaPixels.height);

    //update the main crop rect with the calculated values
    $_cropArea.rect.x = cropRect.x;
    $_cropArea.rect.y = cropRect.y;
    $_cropArea.rect.width = cropRect.width;
    $_cropArea.rect.height = cropRect.height;
    $_cropArea.rect.respectMargin = $_respectMargin;

    //refresh the main crop area
    $_cropArea.draw();
  }

  const updateLeftAndRightCropAreaDimensions = () => {
      //calculate the two crop areas in pixels to be used to crop the image
      //for the left one, only the width needs to be adjusted starting fron the main crop area
      let leftCropAreaPixels = Object.assign({}, $_cropAreaPixels);
      leftCropAreaPixels.width = ($_cropAreaPixels.width/2) + ($_cropCentralMargin/2);

      //for the right one, only the width needs to be adjusted starting fron the central crop area
      let rightCropAreaPixels = Object.assign({}, $_cropAreaPixels);
      rightCropAreaPixels.x = ($_cropAreaPixels.x + ($_cropAreaPixels.width/2)) - ($_cropCentralMargin/2);
      rightCropAreaPixels.width = ($_cropAreaPixels.width/2) + ($_cropCentralMargin/2);

      // Round values
      leftCropAreaPixels.x = Math.round(leftCropAreaPixels.x);
      leftCropAreaPixels.y = Math.round(leftCropAreaPixels.y);
      leftCropAreaPixels.width = Math.round(leftCropAreaPixels.width);
      leftCropAreaPixels.height = Math.round(leftCropAreaPixels.height);

      rightCropAreaPixels.x = Math.round(rightCropAreaPixels.x);
      rightCropAreaPixels.y = Math.round(rightCropAreaPixels.y);
      rightCropAreaPixels.width = Math.round(rightCropAreaPixels.width);
      rightCropAreaPixels.height = Math.round(rightCropAreaPixels.height);

      _cropLeftAreaPixels.set(leftCropAreaPixels);
      _cropRightAreaPixels.set(rightCropAreaPixels);
  }

  const manageCentralAreaOverlay = (action) => {
    /* Remove overlay if exists and return */
    if(action == 'remove' && overlay.getElementById("overlay-central-area")){
      return overlay.removeElementById("overlay-central-area");
    }

    if(action == 'upsert'){
      if(overlay.getElementById("overlay-central-area")){
        /* If overlay already exists, update its position and width/height */
        overlay.moveElement("overlay-central-area",
          $_cropAreaPixels.x + ($_cropAreaPixels.width/2) - ($_cropCentralMargin/2),
          $_cropAreaPixels.y,
          $_cropCentralMargin,
          $_cropAreaPixels.height
        );
      } else {
        /* If overlay does not exist, create it */
        /* Create HTML element */
        const elt = document.createElement("div");
        elt.id = "overlay-central-area";
        elt.className = "overlay-central-area";

        /* Create the overlay */
        overlay.addElement({
          id: "overlay-central-area",
          element: elt,
          x: $_cropAreaPixels.x + ($_cropAreaPixels.width/2) - ($_cropCentralMargin/2),
          y: $_cropAreaPixels.y,
          width: $_cropCentralMargin,
          height: $_cropAreaPixels.height
        })
      }
    }
  }

  const updateCentralAreaOverlay = () => {
    if($_isDoublePageEnabled)
      manageCentralAreaOverlay('upsert');
    else
      manageCentralAreaOverlay('remove');
  }

  const manageTextOverlay = (type, action) => {
    if(type == 'left'){
      /* Remove overlay if exists and return */
      if(action == 'remove' && overlay.getElementById("overlay-text-left")){
        return overlay.removeElementById("overlay-text-left");
      }

      if(action == 'upsert'){
        if(overlay.getElementById("overlay-text-left")){
          /* If overlay already exists, update its position and text */
          overlay.moveElement("overlay-text-left",
            $_cropAreaPixels.x,
            $_cropAreaPixels.y - 180,
            $_cropAreaPixels.width / 2,
            100
          );

          if($_isDoublePageEnabled)
            overlay.getElementById("overlay-text-left").element.children[0].innerHTML = `${!$_isEbraicOrder ? $_nomenclaturaSx.displayName : $_nomenclaturaDx.displayName}<br>${Math.round($_cropLeftAreaPixels?.width)}x${Math.round($_cropLeftAreaPixels?.height)}px`;
          else
            overlay.getElementById("overlay-text-left").element.children[0].innerHTML = `${!$_isEbraicOrder ? $_nomenclaturaSx.displayName : $_nomenclaturaDx.displayName}<br>${Math.round($_cropAreaPixels.width)}x${Math.round($_cropAreaPixels.height)}px`;
        } else {
          /* If overlay does not exist, create it */
          /* Create HTML element */
          const elt = document.createElement("span");
          elt.id = "overlay-text-left";
          elt.className = "overlay-text";

          if($_isDoublePageEnabled)
            elt.innerHTML = `${!$_isEbraicOrder ? $_nomenclaturaSx.displayName : $_nomenclaturaDx.displayName}<br>${Math.round($_cropLeftAreaPixels?.width)}x${Math.round($_cropLeftAreaPixels?.height)}px`;
          else
            elt.innerHTML = `${!$_isEbraicOrder ? $_nomenclaturaSx.displayName : $_nomenclaturaDx.displayName}<br>${Math.round($_cropAreaPixels.width)}x${Math.round($_cropAreaPixels.height)}px`;

          /* Create the overlay */
          overlay.addElement({
            id: "overlay-text-left",
            element: elt,
            x: $_cropAreaPixels.x,
            y: $_cropAreaPixels.y - 180,
            width: $_cropAreaPixels.width / 2,
            height: 100,
            fontSize: 12
          })
        }
      }
    } else if(type == 'right'){
      /* Remove overlay if exists and return */
      if(action == 'remove' && overlay.getElementById("overlay-text-right")){
        return overlay.removeElementById("overlay-text-right");
      }

      if(action == 'upsert'){
        if(overlay.getElementById("overlay-text-right")){
          /* If overlay already exists, update its position and text */
          overlay.moveElement("overlay-text-right",
            $_cropAreaPixels.x + $_cropAreaPixels.width - 400,
            $_cropAreaPixels.y - 180,
            $_cropAreaPixels.width / 2,
            100
          );
          overlay.getElementById("overlay-text-right").element.children[0].innerHTML = `${!$_isEbraicOrder ? $_nomenclaturaDx?.displayName : $_nomenclaturaSx.displayName}<br>${Math.round($_cropLeftAreaPixels?.width)}x${Math.round($_cropLeftAreaPixels?.height)}px`;
        } else {
          /* Create HTML element */
          const elt = document.createElement("span");
          elt.id = "overlay-text-right";
          elt.className = "overlay-text";
          elt.innerHTML = `${!$_isEbraicOrder ? $_nomenclaturaDx?.displayName : $_nomenclaturaSx.displayName}<br>${Math.round($_cropLeftAreaPixels?.width)}x${Math.round($_cropLeftAreaPixels?.height)}px`;

          /* Create the overlay */
          overlay.addElement({
            id: "overlay-text-right",
            element: elt,
            x: $_cropAreaPixels.x + $_cropAreaPixels.width - 400,
            y: $_cropAreaPixels.y - 180,
            width: $_cropAreaPixels.width / 2,
            height: 100,
            fontSize: 12
          })
        }
      }
    } else if(type == 'central'){
      /* Remove overlay if exists and return */
      if(action == 'remove' && overlay.getElementById("overlay-text-central")){
        return overlay.removeElementById("overlay-text-central");
      }

      if(action == 'upsert'){
        if(overlay.getElementById("overlay-text-central")){
          /* If overlay already exists, update its position and text */
          overlay.moveElement("overlay-text-central",
            $_cropAreaPixels.x + ($_cropAreaPixels.width/2) - ($_cropCentralMargin/2),
            $_cropAreaPixels.y - 90,
            $_cropAreaPixels.width / 4,
            100
          );
          overlay.getElementById("overlay-text-central").element.children[0].innerHTML = `${$_cropCentralMargin}px`;
        } else {
          /* Create HTML element */
          const elt = document.createElement("span");
          elt.id = "overlay-text-central";
          elt.className = "overlay-text";
          elt.innerHTML = `${$_cropCentralMargin}px`;

          /* Create the overlay */
          overlay.addElement({
            id: "overlay-text-central",
            element: elt,
            x: $_cropAreaPixels.x + ($_cropAreaPixels.width/2)- ($_cropCentralMargin/2),
            y: $_cropAreaPixels.y - 90,
            width: $_cropAreaPixels.width / 4,
            height: 100,
            fontSize: 12
          })
        }
      }
    }
  }

  const updateTextOverlay = () => {
    manageTextOverlay('left', 'upsert');

    if($_isDoublePageEnabled){
      manageTextOverlay('right', 'upsert');
      manageTextOverlay('central', 'upsert');
    } else {
      manageTextOverlay('right', 'remove');
      manageTextOverlay('central', 'remove');
    }
  }

  const createGridOverlay = () => {
    const imageDimensions = viewer.world.getItemAt(0).getContentSize();

    /* Create HTML element */
    const elt = document.createElement("div");
    elt.id = "overlay-grid";
    elt.className = 'overlay-grid';

    /* Create the overlay */
    overlay.addElement({
      id: "overlay-grid",
      element: elt,
      x: 0,
      y: 0,
      width: imageDimensions.x,
      height: imageDimensions.y
    })
  }

  const manageCropAreaCursors = () => {
    /* crop area */
    d3.select("#crop-area").style("cursor", "move");

    /* top-left and bottom-right corner handles */
    d3.select("#crop-area").selectChild(".corner-0-handle").style("cursor", "se-resize");
    d3.select("#crop-area").selectChild(".corner-2-handle").style("cursor", "se-resize");

    /* top-right and bottom-left corner handles */
    d3.select("#crop-area").selectChild(".corner-1-handle").style("cursor", "ne-resize");
    d3.select("#crop-area").selectChild(".corner-3-handle").style("cursor", "ne-resize");

    /* top handle and border */
    d3.select("#crop-area").selectChild(".border-0").style("cursor", "n-resize");
    d3.select("#crop-area").selectChild(".border-0-handle").style("cursor", "n-resize");

    /* bottom handle and border */
    d3.select("#crop-area").selectChild(".border-2").style("cursor", "s-resize");
    d3.select("#crop-area").selectChild(".border-2-handle").style("cursor", "s-resize");

    /* right handle and border */
    d3.select("#crop-area").selectChild(".border-1").style("cursor", "e-resize");
    d3.select("#crop-area").selectChild(".border-1-handle").style("cursor", "e-resize");

    /* left handle and border */
    d3.select("#crop-area").selectChild(".border-3").style("cursor", "w-resize");
    d3.select("#crop-area").selectChild(".border-3-handle").style("cursor", "w-resize");
  }

  const dragHandler = () => {
    getCropAreasDimensions();

    // Check and adjust if the crop area is out of bounds
    const adjustmentsMade = checkIfOutOfBorder();

    // If adjustments were made, get the updated dimensions
    if (adjustmentsMade) {
      getCropAreasDimensions();
    }

    updateLeftAndRightCropAreaDimensions();
    updateCentralAreaOverlay();
    updateTextOverlay();
  }

  const dragEndHandler = () => {
    // Remove the pointermove event listener
    d3.select('#crop-area').on('pointermove', null);

    // Final check to ensure the crop area is within bounds
    const adjustmentsMade = checkIfOutOfBorder();

    // If adjustments were made, update all related components
    if (adjustmentsMade) {
      getCropAreasDimensions();
      updateLeftAndRightCropAreaDimensions();
      updateCentralAreaOverlay();
      updateTextOverlay();
    }
  }

  const createHandlers = () => {
    d3.select('#crop-area').on('pointerdown', () => {
      d3.select('#crop-area').on('pointermove', () => dragHandler())
      d3.select('#crop-area').on('pointerup', () => dragEndHandler(), {once: true});
    });
  }

  const getCoordinates = event => {
    /* Return if all coordinates have already been selected */
    if($_colorPatches.every((colorPatch) => { return colorPatch.coordinates })) return;

    const point = new OpenSeadragon.Point(event.x, event.y - 30);
    const viewportPoint = viewer.viewport.pointFromPixel(point);
    const imagePoint = viewer.viewport.viewportToImageCoordinates(viewportPoint.x, viewportPoint.y);

    let activeIndex;

    /* Set coordinates for selected patch */
    $_colorPatches.forEach((colorPatch, index) => {
      if(colorPatch.active){
        activeIndex = index;
        colorPatch.coordinates = imagePoint;
        colorPatch.active = false;
      }
    });

    /* Increase active patch to the next one */
    if(activeIndex < $_colorPatches.length - 1){
      $_colorPatches[activeIndex + 1].active = true;
    } else {
      /* This set is needed to refresh the component for the last patch */
      _colorPatches.set($_colorPatches);

      /* Remove click event listener on main viewer */
      d3.select("#main-viewer").on('click', null);

      /* Re-show Overlays */
      if($_isDoublePageEnabled){
        d3.select("#crop-area").style("display", "block");
        updateCentralAreaOverlay();
        manageTextOverlay('left', 'upsert');
        manageTextOverlay('right', 'upsert');
        manageTextOverlay('central', 'upsert');
      } else {
        d3.select("#crop-area").style("display", "block");
        manageCentralAreaOverlay('remove');
        manageTextOverlay('left', 'upsert');
      }
    }
  }

  const moveCropArea = async (direction) => {
    switch (direction) {
      case 'left':
        $_cropAreaPixels.x -= 5;
        break;
      case 'right':
        $_cropAreaPixels.x += 5;
        break;
      case 'up':
        $_cropAreaPixels.y -= 5;
        break;
      case 'down':
        $_cropAreaPixels.y += 5;
        break;
      default:
        break;
    }

    //get the main crop area in viewport coordinates
    const cropAreaViewport = viewer.viewport.imageToViewportRectangle($_cropAreaPixels.x, $_cropAreaPixels.y, $_cropAreaPixels.width, $_cropAreaPixels.height);

    //update the main crop rect with the calculated values
    $_cropArea.rect.x = cropAreaViewport.x;
    $_cropArea.rect.y = cropAreaViewport.y;
    $_cropArea.rect.width = cropAreaViewport.width;
    $_cropArea.rect.height = cropAreaViewport.height;

    /* Check if out of border */
    checkIfOutOfBorder();

    getCropAreasDimensions();
    updateRespectMargin();
    updateLeftAndRightCropAreaDimensions();
    updateCentralAreaOverlay();
    updateTextOverlay();
  }

  const checkIfOutOfBorder = () => {
    const imageWidth = viewer.world.getItemAt(0).getContentSize().x;
    const imageHeight = viewer.world.getItemAt(0).getContentSize().y;
    let cropAreaViewport;
    let adjustmentsMade = false;
    const buffer = 1; // Small buffer to prevent touching the exact edge

    /* Width check */
    if($_cropAreaPixels.x + $_cropAreaPixels.width > imageWidth - buffer){
      // Adjust x position to keep the crop area within the image bounds
      $_cropAreaPixels.x = imageWidth - $_cropAreaPixels.width - buffer;
      cropAreaViewport = viewer.viewport.imageToViewportRectangle($_cropAreaPixels.x, $_cropAreaPixels.y, $_cropAreaPixels.width, $_cropAreaPixels.height);

      /* Update crop area x */
      $_cropArea.rect.x = cropAreaViewport.x;
      adjustmentsMade = true;
    }

    if($_cropAreaPixels.x < buffer){
      // Adjust x position to keep the crop area within the image bounds
      $_cropAreaPixels.x = buffer;
      cropAreaViewport = viewer.viewport.imageToViewportRectangle($_cropAreaPixels.x, $_cropAreaPixels.y, $_cropAreaPixels.width, $_cropAreaPixels.height);

      /* Update crop area x */
      $_cropArea.rect.x = cropAreaViewport.x;
      adjustmentsMade = true;
    }

    /* Height check */
    if($_cropAreaPixels.y + $_cropAreaPixels.height > imageHeight - buffer){
      // Adjust y position to keep the crop area within the image bounds
      $_cropAreaPixels.y = imageHeight - $_cropAreaPixels.height - buffer;
      cropAreaViewport = viewer.viewport.imageToViewportRectangle($_cropAreaPixels.x, $_cropAreaPixels.y, $_cropAreaPixels.width, $_cropAreaPixels.height);

      /* Update crop area y */
      $_cropArea.rect.y = cropAreaViewport.y;
      adjustmentsMade = true;
    }

    if($_cropAreaPixels.y < buffer){
      // Adjust y position to keep the crop area within the image bounds
      $_cropAreaPixels.y = buffer;
      cropAreaViewport = viewer.viewport.imageToViewportRectangle($_cropAreaPixels.x, $_cropAreaPixels.y, $_cropAreaPixels.width, $_cropAreaPixels.height);

      /* Update crop area y */
      $_cropArea.rect.y = cropAreaViewport.y;
      adjustmentsMade = true;
    }

    // If adjustments were made, redraw the crop area
    if (adjustmentsMade) {
      $_cropArea.draw();
    }

    return adjustmentsMade;
  }

  const createEventListeners = () => {
    d3.select("#main-viewer").on("switch-to-single-page", (e) => {
      if(!e?.detail?.isInit){
        $_cropAreaPixels.width = $_cropAreaPixels.width/2 + ($_cropCentralMargin/2);

        //get the main crop area in viewport coordinates
        const cropAreaViewport = viewer.viewport.imageToViewportRectangle($_cropAreaPixels.x, $_cropAreaPixels.y, $_cropAreaPixels.width, $_cropAreaPixels.height);

        /* Update crop area and redraw */
        $_cropArea.rect.width = cropAreaViewport.width;
        $_cropArea.draw();

        getCropAreasDimensions();

        /* Check if out of border */
        checkIfOutOfBorder();

        getCropAreasDimensions();
        updateRespectMargin();
        updateLeftAndRightCropAreaDimensions();
      }

      /* Hide Crop Central Area and Text Overlay */
      manageCentralAreaOverlay('remove');
      updateTextOverlay();
    });

    d3.select("#main-viewer").on("switch-to-double-page", (e) => {
      if(!e?.detail?.isInit){
        $_cropAreaPixels.width = $_cropAreaPixels.width*2 - $_cropCentralMargin;

        //get the main crop area in viewport coordinates
        const cropAreaViewport = viewer.viewport.imageToViewportRectangle($_cropAreaPixels.x, $_cropAreaPixels.y, $_cropAreaPixels.width, $_cropAreaPixels.height);

        /* Update crop area and redraw */
        $_cropArea.rect.width = cropAreaViewport.width;
        $_cropArea.draw();

        getCropAreasDimensions();

        /* Check if out of border */
        checkIfOutOfBorder();

        getCropAreasDimensions();
        updateRespectMargin();
        updateLeftAndRightCropAreaDimensions();
      }

      /* Show Crop Central Area and Text Overlay */
      manageCentralAreaOverlay('upsert');
      updateTextOverlay();
    });

    d3.select("#main-viewer").on("lock-crop-area", (e) => {
      /* Avoid resizing of crop area by inhibit dragging */
      d3.select("#crop-area").selectChildren().style("pointer-events", "none");
    });

    d3.select("#main-viewer").on("unlock-crop-area", (e) => {
      /* Allow resizing of crop area */
      d3.select("#crop-area").selectChildren().style("pointer-events", null);
    });

    d3.select("#main-viewer").on("dimensions-updated", (e) => {
      let cropAreaViewport;

      if($_isDoublePageEnabled)
        /* Get the left crop area rect in viewport coordinates */
        cropAreaViewport = viewer.viewport.imageToViewportRectangle($_cropLeftAreaPixels.x, $_cropLeftAreaPixels.y, $_cropLeftAreaPixels.width*2 - $_cropCentralMargin, $_cropLeftAreaPixels.height);
      else
        /* Get the main rect in viewport coordinates */
        cropAreaViewport = viewer.viewport.imageToViewportRectangle($_cropAreaPixels.x, $_cropAreaPixels.y, $_cropAreaPixels.width, $_cropAreaPixels.height);

      $_cropArea.rect.width = cropAreaViewport.width;
      $_cropArea.rect.height = cropAreaViewport.height;
      $_cropArea.draw();

      getCropAreasDimensions();

      /* Check if out of border */
      checkIfOutOfBorder();

      /* Update all elements */
      getCropAreasDimensions();
      updateRespectMargin();
      updateLeftAndRightCropAreaDimensions();
      updateCentralAreaOverlay();
      updateTextOverlay();

      /* Check if out of border */
      checkIfOutOfBorder();
    });

    d3.select("#main-viewer").on("load-default-dimensions", (e) => {
      if(!$_savedCropAreaPixels) return;

      /* Update central margin and respect margin */
      _cropCentralMargin.set($_savedCentralMargin);
      _respectMargin.set($_savedRespectMargin);

      let cropAreaViewport;

      if($_isDoublePageEnabled)
        cropAreaViewport = viewer.viewport.imageToViewportRectangle($_cropLeftAreaPixels.x, $_cropLeftAreaPixels.y, $_savedCropAreaPixels.width*2 - $_cropCentralMargin, $_savedCropAreaPixels.height);
      else
        cropAreaViewport = viewer.viewport.imageToViewportRectangle($_cropAreaPixels.x, $_cropAreaPixels.y, $_savedCropAreaPixels.width, $_savedCropAreaPixels.height);

      /* Update crop area and redraw */
      $_cropArea.rect.width = cropAreaViewport.width;
      $_cropArea.rect.height = cropAreaViewport.height;
      $_cropArea.draw();

      getCropAreasDimensions();

      /* Check if out of border */
      checkIfOutOfBorder();

      /* Update all elements */
      getCropAreasDimensions();
      updateRespectMargin();
      updateLeftAndRightCropAreaDimensions();
      updateCentralAreaOverlay();
      updateTextOverlay();
    });

    d3.select("#main-viewer").on("refresh-all-elements", (e) => {
      /* Update all elements */
      getCropAreasDimensions();
      updateRespectMargin();
      updateLeftAndRightCropAreaDimensions();
      updateCentralAreaOverlay();
      updateTextOverlay();
    });

    d3.select("#main-viewer").on('update-text-overlay', (e) => {
      updateTextOverlay();
    });

    d3.select("#main-viewer").on('color-linearization-started', (e) => {
      /* Add click event listener on main viewer */
      d3.select("#main-viewer").on('click', getCoordinates);

      /* Hide all overlays */
      d3.select("#crop-area").style("display", "none");
      manageCentralAreaOverlay('remove');
      manageTextOverlay('left', 'remove');
      manageTextOverlay('right', 'remove');
      manageTextOverlay('central', 'remove');
    });

    d3.select("#main-viewer").on('color-linearization-ended', (e) => {
      /* Remove click event listener on main viewer */
      d3.select("#main-viewer").on('click', null);

      /* Re-show Overlays */
      if($_isDoublePageEnabled){
        d3.select("#crop-area").style("display", "block");
        manageTextOverlay('left', 'upsert');
        manageTextOverlay('right', 'upsert');
      } else {
        d3.select("#crop-area").style("display", "block");
        manageCentralAreaOverlay('remove');
        manageTextOverlay('left', 'upsert');
      }
    });

    d3.select("#main-viewer").on('rotate-viewer', (e) => {
      const rotation = e.detail.rotation;
      viewer.viewport.setRotation(rotation);
    });
  }
</script>

<div class="osd-container" >
  <MainViewersToolbar />
  <div id="main-viewer" />
</div>

  <style>
    .osd-container {
        width: 50%;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    #main-viewer {
      height: 100%;
      margin-top: 10px;
    }

    * :global(.overlay-crop-area div[class^='border-']) {
      background: var(--mdc-theme-primary) !important;
    }

    * :global(.overlay-crop-area::before) {
      content: "+";
      position: absolute;
      top: 50%;
      left: 50%;
      z-index: -1; /* places the ::before element underneath the main div content, as a background */
      font-size: 20px;
      color: var(--mdc-theme-background);
      transform: translate(-50%, -50%); /* Scale from the center of the text */
    }

    * :global(.overlay-central-area) {
      border-left: 1px solid var(--mdc-theme-primary);
      border-right: 1px solid var(--mdc-theme-primary);
    }

    * :global(.overlay-text) {
      color: var(--mdc-theme-primary);
      font-weight: bold;
    }

    * :global(.overlay-grid) {
      background-image: url('/grid_12x12.svg');
    }

    * :global(.overlay-cross) {
      color: var(--mdc-theme-background);
      transform-origin: center center;
    }
  </style>