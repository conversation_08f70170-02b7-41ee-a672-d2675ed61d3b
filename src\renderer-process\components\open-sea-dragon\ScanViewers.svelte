<script>
    import { _selectedJob, _selectedScan, _selectedScanArr, _scanViewerRgb, _showDraggableDivOnViewers, _isEbraicOrder } from "@store";
    import ScanViewersToolbar from "@components/open-sea-dragon/ScanViewersToolbar.svelte";
    import DraggableDiv from '@components/common/DraggableDiv.svelte';
    import { delay } from "@utility";
    import { onMount, createEventDispatcher } from "svelte";
    import * as d3 from 'd3-selection';
    
    let leftViewer, rightViewer;
    export let fullWidth = false;
    export let isFromPreview = false;

    const dispatch = createEventDispatcher();

    onMount(async () => {
        leftViewer = OpenSeadragon({
            id: 'scan-viewer-left',
            showNavigationControl: false,
            showZoomControl: false,
            prefixUrl: '/modules/openseadragon/images/',
            visibilityRatio: 1.0,
            constrainDuringPan: true,
            gestureSettingsMouse: { clickToZoom: false },
            preserveViewport: false,
            homeButton: 'home-button'
        });

        rightViewer = OpenSeadragon({
            id: 'scan-viewer-right',
            showNavigationControl: false,
            showZoomControl: false,
            prefixUrl: '/modules/openseadragon/images/',
            visibilityRatio: 1.0,
            constrainDuringPan: true,
            gestureSettingsMouse: { clickToZoom: false },
            preserveViewport: false,
            homeButton: 'home-button'
        });

        await delay(500);

        /* Initialize RGB plugin */
        leftViewer.rgb({
            onCanvasHover: (color) => _scanViewerRgb.set(color)
        });

        rightViewer.rgb({
            onCanvasHover: (color) => _scanViewerRgb.set(color)
        });

        if($_selectedScan)
            await updateScanImages($_selectedScan);
        else if($_selectedScanArr.length > 0)
            await updateScanImages($_selectedScanArr[0]);

        createEventListeners();

        /* Emit event to notify that viewers are ready */
        dispatch('ready');
    });

    const updateScanImages = async (scan) => {
        /* Remove previous images */
        leftViewer.close();
        rightViewer.close();

        /* build base file name with id */
        const baseFileName = scan.id.toString();
        const leftFileName = scan.pagina_doppia && scan.ordinamento_arabo ? baseFileName + '_1' : baseFileName + '_0';
        const rightFileName = scan.pagina_doppia && scan.ordinamento_arabo ? baseFileName + '_0' : baseFileName + '_1';

        /* LEFT VIEWER */
        const dziPathLeft = await window.electron.filesystem("fs-join-path", [ $_selectedJob.preview_path, leftFileName + '.dzi' ]);

        /* Ensure that the file exists */
        const dziLeftExists = await window.electron.filesystem("fs-path-exists", dziPathLeft);

        /* If not exists, create dzi image */
        if(dziLeftExists === false) {
            let params = {};
            params.inputPath = await window.electron.filesystem("fs-join-path", [ $_selectedJob.scan_path, leftFileName + '.tif' ]);
            params.outputPath = await window.electron.filesystem("fs-join-path", [ $_selectedJob.preview_path, leftFileName + '.dz' ]);
            await window.electron.sharp('build-dzi', params);
        }

        /* Add new image to the viewer */
        leftViewer.open('isilon://' + dziPathLeft);

        if(scan.pagina_doppia) {
            d3.select('#scan-viewer-right').style('display', null);

            /* RIGHT VIEWER */
            const dziPathRight = await window.electron.filesystem("fs-join-path", [ $_selectedJob.preview_path, rightFileName + '.dzi' ]);

            /* Ensure that the file exists */
            const dziRightExists = await window.electron.filesystem("fs-path-exists", dziPathRight);

            /* If not exists, create dzi image */
            if(dziRightExists === false) {
                let params = {};
                params.inputPath = await window.electron.filesystem("fs-join-path", [ $_selectedJob.scan_path, rightFileName + '.tif' ]);
                params.outputPath = await window.electron.filesystem("fs-join-path", [ $_selectedJob.preview_path, rightFileName + '.dz' ]);
                await window.electron.sharp('build-dzi', params);
            }

            /* Add new image to the viewer */
            rightViewer.open('isilon://' + dziPathRight);
        } else {
            d3.select('#scan-viewer-right').style('display', 'none');
        }

        if(!leftViewer.preserveViewport && !rightViewer.preserveViewport){
            await delay(100);
            leftViewer.viewport.goHome(true);
            rightViewer.viewport.goHome(true);
        }
    }

    const createEventListeners = () => {
        d3.select('#scan-viewers').on('update-scan-images', async (e) => {
            const scan = e.detail.scan;

            if(!scan) return;
            
            await updateScanImages(scan);
        });

        d3.select('#scan-viewers').on('destroy-viewers', async (e) => {
            /* Destroy viewers */
            leftViewer.destroy();
            leftViewer = null;

            rightViewer.destroy();
            rightViewer = null;

            /* Clear selected scans */
            _selectedScan.set(null);
            $_selectedScanArr.length = 0;
        });

        d3.select('#scan-viewers').on('go-home', () => {
            leftViewer.viewport.goHome(false);
            rightViewer.viewport.goHome(false);
        });

        d3.select('#scan-viewers').on('preserve-viewport', () => {
            leftViewer.preserveViewport = !leftViewer.preserveViewport;
            rightViewer.preserveViewport = !rightViewer.preserveViewport;
        });
    }
  </script>
  
  <div class="osd-container" class:fullWidth>
    <ScanViewersToolbar {isFromPreview}/>
    <div id='scan-viewers' >
        <div id='scan-viewer-left' class="viewer">
            {#if $_showDraggableDivOnViewers}
                <DraggableDiv id='left-draggable-div' text={$_selectedScan?.display_name_sx || $_selectedScanArr[0]?.display_name_sx}/>
            {/if}
        </div>
        <div id='scan-viewer-right' class="viewer">
            {#if $_showDraggableDivOnViewers}
                <DraggableDiv id='right-draggable-div' text={$_selectedScan?.display_name_dx || $_selectedScanArr[0]?.display_name_dx}/>
            {/if}
        </div>
    </div>
</div>
  
  <style>
    .osd-container {
        width: 50%;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .osd-container.fullWidth {
        width: 100%;
    }

    #scan-viewers {
        display: flex;
        gap: 5px;
        width: 100%;
        height: 100%;
        margin-top: 10px;
    }

    div.viewer {
        flex-grow: 1;
        height: 100%;
        position: relative;
    }
  </style>