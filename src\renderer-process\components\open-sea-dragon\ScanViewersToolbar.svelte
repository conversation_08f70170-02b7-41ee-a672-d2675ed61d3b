<script>
    import { _selectedJob, _selectedScan, _selectedScanArr, _scanViewerRgb, _showDraggableDivOnViewers } from "@store";
    import IconButton from "@components/common/IconButton.svelte";
    import * as d3 from 'd3-selection';

    //smui components
    import Paper, { Content } from '@smui/paper';

    //icons
	import ResetViewPortIcon from "svelte-material-icons/EyeRefresh.svelte";
    import PreserveViewPortIcon from "svelte-material-icons/EyeLock.svelte";
    import showDraggableDivIcon from "svelte-material-icons/CommentEye.svelte";
    import RefreshPreviewsIcon from "svelte-material-icons/ImageRefresh.svelte";

    export let isFromPreview;

    $: scan = $_selectedScan || $_selectedScanArr[0];
    
    const goHome = () => {
        d3.select('#scan-viewers').dispatch('go-home');
    }

    let isPreserveViewport = false;
    const preserveViewport = () => {
        isPreserveViewport = !isPreserveViewport;
        d3.select('#scan-viewers').dispatch('preserve-viewport');
    }

    const showDraggableDiv = () => {
        _showDraggableDivOnViewers.set(!$_showDraggableDivOnViewers);
    }

    const updateScanPreviews = async () => {
        /* Delete previews for selected scan */
        const baseFileName = $_selectedScan?.id.toString() || $_selectedScanArr[0]?.id.toString();
        const dziPathLeft = await window.electron.filesystem("fs-join-path", [ $_selectedJob.preview_path, baseFileName + '_0.dzi' ]);
        const dziPathRight = await window.electron.filesystem("fs-join-path", [ $_selectedJob.preview_path, baseFileName + '_1.dzi' ]);

        await window.electron.filesystem("fs-delete-file-or-dir", dziPathLeft);
        await window.electron.filesystem("fs-delete-file-or-dir", dziPathRight);

        /* Update scan images */
        d3.select('#scan-viewers').dispatch('update-scan-images', {detail: { scan: $_selectedScan || $_selectedScanArr[0]}});
    }
</script>

<div id='osd-toolbar'>
    <Paper elevation=5>
        <Content>
            <div class="button-container">
                <IconButton icon={ResetViewPortIcon} iconWidth=25 iconHeight=25 iconViewbox="0 2 24 24" iconColor="var(--mdc-theme-secondary)" tooltip="Reimposta zoom e posizione" onclick={goHome}/>
                <IconButton icon={PreserveViewPortIcon} iconWidth=25 iconHeight=25 iconViewbox="0 2 24 24" iconColor={isPreserveViewport ? "var(--mdc-theme-primary)" : "var(--mdc-theme-secondary)"} tooltip="Mantieni zoom" onclick={preserveViewport}/>
                <IconButton icon={showDraggableDivIcon} iconWidth=25 iconHeight=25 iconViewbox="0 2 24 24" iconColor={$_showDraggableDivOnViewers ? "var(--mdc-theme-primary)" : "var(--mdc-theme-secondary)"} tooltip="Mostra nomi in finestra" onclick={showDraggableDiv}/>
                <IconButton icon={RefreshPreviewsIcon} iconWidth=25 iconHeight=25 iconViewbox="0 2 24 24" iconColor="var(--mdc-theme-secondary)" tooltip="Aggiorna anteprime" onclick={updateScanPreviews}/>
            </div>

            {#if scan}
                <div class="text-container">
                    <div class="text">
                        {#if scan.pagina_doppia}
                            <p><span class="title">Nomi:</span> {scan.display_name_sx} - {scan.display_name_dx}</p>
                        {:else}
                            <p><span class="title">Nome:</span> {scan.display_name_sx}</p>
                        {/if}
                    </div>
                    <div class="text">
                        <p><span class="title">Crop:</span> {scan.crop_area_width}x{scan.crop_area_height}px</p>
                    </div>
                    <div class="text">
                        <p><span class="title">Data:</span> {new Date(scan.created_at).toLocaleString()}</p>
                    </div>
                    {#if isFromPreview}
                        <div class="text">
                            <p><span class="title">RGB:</span> {`(${$_scanViewerRgb.r}, ${$_scanViewerRgb.g}, ${$_scanViewerRgb.b})`}</p>
                        </div>
                        <div class="text">
                            <p><span class="title">Id:</span> {scan.id}</p>
                        </div>
                    {/if}
                </div>
            {/if}
        </Content>
    </Paper>
</div>

<style>
    #osd-toolbar {
        width: 100%;
        height: 30px;
    }

    .text-container {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 15px;
        width: 100%;
    }

    .text {
        background-color: var(--mdc-theme-background);
        padding: 0px 10px;
        box-shadow: 0px 0px 10px 0px var(--mdc-theme-background) inset;
        border-radius: 5px;
        white-space: nowrap;
    }

    p {
        color: var(--mdc-theme-secondary);
        font-size: small;
        margin: 0px;
    }

    .title {
        color: var(--mdc-theme-primary);
    }

    .button-container{
        display: flex;
        align-items: center;
    }

    * :global(.smui-paper) {
        height: 100%;
        display: flex;
        align-items: center;
        padding: 5px;
    }

    * :global(.smui-paper__content) {
        display: flex;
        align-items: center;
        width: 100%;
    }
</style>