<script>
    import { _isCreatingPreviews, _selectedJob, _shouldRecreatePreviews } from '@store';
    import CircularProgressIndicator from '@components/common/CircularProgressIndicator.svelte';
    import { notify } from '@utility';
    import * as d3 from 'd3-selection';

    //smui
    import Dialog, { Title, Content } from '@smui/dialog';
    import Button, { Label } from '@smui/button';

    const createMissingJobPreviews = async () => {
        /* Create previews for the job */
        await window.electron.worker('create-job-preview', {scanList: $_selectedJob.scanWithMissingPreviews, scanPath: $_selectedJob.scan_path, previewPath: $_selectedJob.preview_path});

        /* When preview creation is completed, set information on the job */
        window.electron.onJobPreviewCreationCompleted(() => {
            $_selectedJob.scanWithMissingPreviews.length = 0;
            _selectedJob.set($_selectedJob);

            notify.success("Anteprime create con successo per il job " + $_selectedJob.identifier);
            _isCreatingPreviews.set(false);
            _shouldRecreatePreviews.set(false);

            d3.select('#preview-visualizer').dispatch('refresh-scan-table');
        });
    }

    const handleConfirm = async () => {
        _isCreatingPreviews.set(true);
        await createMissingJobPreviews();
    }
   
    const handleClose = () => {
        _shouldRecreatePreviews.set(false);
    }
</script>

<div>
    <Dialog bind:open={$_shouldRecreatePreviews} scrimClickAction="">
        <Title>Aggiornamento Anteprime</Title>
        {#if $_isCreatingPreviews}
            <CircularProgressIndicator loadingText='Aggiornamento anteprime in corso...'/>
        {:else}
            <Content>
                Ci sono {$_selectedJob?.scanWithMissingPreviews?.length} scansioni con anteprime non aggiornate.
                Come procedere?
            </Content>
            <div class="flex-row-buttons">
                <Button on:click={handleConfirm} variant="raised">
                    <Label>Aggiorna</Label>
                </Button>
                <Button on:click={handleClose} variant="raised" color="secondary">
                    <Label>Non Aggiornare</Label>
                </Button>
            </div>
        {/if}
    </Dialog>
</div>

<style>
    .flex-row-buttons {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 10px;
    }

    * :global(.mdc-dialog .mdc-dialog__surface) {
        min-width: 25%;
        min-height: 20%;
    }

    * :global(.mdc-dialog__content) {
        margin-top: 20px;
    }
</style>