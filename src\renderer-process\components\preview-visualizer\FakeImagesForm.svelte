<script>
    import { _cropAreaPixels, _cropLeftAreaPixels, _isAddingFakeImages, _selectedJob, _nomenclaturaSxOT, _nomenclaturaDxOT, _isDoublePageEnabledOT, _isEbraicOrderOT } from '@store'
    import NomenclatureOT from '@components/toolbar/nomenclature/edit-scan/NomenclatureOT.svelte';
    import { notify } from '@utility';
    import * as d3 from 'd3-selection';

    /* Function to convert numbers to roman numerals */
    const romanize = (num) => {
        if (isNaN(num))
            return NaN;
        var digits = String(+num).split(""),
            key = ["",'C','CC','CCC','CD','D','DC','DCC','DCCC','CM',
                   "",'X','XX','XXX','XL','L','LX','LXX','LXXX','XC',
                   "",'I','II','III','IV','V','VI','VII','VIII','IX'],
            roman = "",
            i = 3;
        while (i--)
            roman = (key[+digits.pop() + (i * 10)] || "") + roman;
        return Array(+digits.join("") + 1).join("M") + roman;
    };

    //smui
    import Paper, { Title, Content } from '@smui/paper';
    import Button, { Label } from '@smui/button';
    import Textfield from '@smui/textfield';
    import FormField from '@smui/form-field';
    import Radio from '@smui/radio';

    export let selectedScan, ordinatoriList;
    let position = "above";
    let numberOfImages = 1;

    const handleConfirm = async () => {
        if(!$_nomenclaturaSxOT.ordinatore || (!$_nomenclaturaDxOT.ordinatore && $_isDoublePageEnabledOT)) {
            notify.warning("Specificare gli ordinatori per rinominare la scansione");
            return;
        }

        /* Insert fake images */
        await insertFakeImages();

        /* Refresh scan table */
        d3.select('#preview-visualizer').dispatch('refresh-scan-table');

        /* Close form */
        handleClose();
    }

    const insertFakeImages = async () => {
        let params = {};
        params.idDigit = $_selectedJob.id_digit;
        params.sequenzialeScansione = position == 'below' ? selectedScan.sequenziale_scansione : selectedScan.sequenziale_scansione - 1;
        params.step = numberOfImages;

        await window.electron.database("db-increase-seq-scansioni", params);

        let startingSequenziale = position == 'below' ? selectedScan.sequenziale_scansione + 1 : selectedScan.sequenziale_scansione;

        /* Crea file con immagine fittizia */
        for(let i = 0; i < numberOfImages; i++){
            const paramsArr = [];

            paramsArr.push($_selectedJob.id_digit);
            paramsArr.push(startingSequenziale + i);
            paramsArr.push($_selectedJob.identifier);
            paramsArr.push($_isDoublePageEnabledOT);
            paramsArr.push($_isEbraicOrderOT);
            paramsArr.push(null);
            paramsArr.push($_nomenclaturaSxOT.ordinatore.metis_code);
            paramsArr.push($_nomenclaturaSxOT.ordinatore.name_short);
            paramsArr.push($_nomenclaturaSxOT.progressioneIndex);
            paramsArr.push($_nomenclaturaSxOT.letteraPagina || '');
            paramsArr.push($_nomenclaturaSxOT.letteraPaginaEnd || '');
            paramsArr.push($_nomenclaturaSxOT.prefissoNumeroPagina || '');
            paramsArr.push($_nomenclaturaSxOT.ordinatore.is_alpha ? 0 : $_nomenclaturaSxOT.numeroPagina + i);
            paramsArr.push($_nomenclaturaSxOT.numeroPaginaEnd || 0);
            paramsArr.push($_nomenclaturaSxOT.ordinatore.is_alpha ? 0 : $_nomenclaturaSxOT.internoPagina);
            paramsArr.push($_nomenclaturaSxOT.ordinatore.is_alpha ? null : $_nomenclaturaSxOT.tipoPagina);
            paramsArr.push($_nomenclaturaSxOT.eccezione ? $_nomenclaturaSxOT.eccezione.metis_code : null);
            paramsArr.push($_nomenclaturaSxOT.eccezione ? $_nomenclaturaSxOT.eccezione.name_short : null);
            paramsArr.push($_nomenclaturaSxOT.eccezione ? $_nomenclaturaSxOT.eccezione.valore : null);
            paramsArr.push($_nomenclaturaSxOT.eccezione ? $_nomenclaturaSxOT.eccezione.parte : null);
            paramsArr.push(buildDisplayName($_nomenclaturaSxOT, i));
            paramsArr.push(null);
            paramsArr.push($_isDoublePageEnabledOT ? $_nomenclaturaDxOT.ordinatore?.metis_code : null);
            paramsArr.push($_isDoublePageEnabledOT ? $_nomenclaturaDxOT.ordinatore?.name_short : null);
            paramsArr.push($_isDoublePageEnabledOT ? $_nomenclaturaDxOT.progressioneIndex : null);
            paramsArr.push($_isDoublePageEnabledOT ? $_nomenclaturaDxOT.letteraPagina || '' : '');
            paramsArr.push($_isDoublePageEnabledOT ? $_nomenclaturaDxOT.letteraPaginaEnd || '' : '');
            paramsArr.push($_isDoublePageEnabledOT ? $_nomenclaturaDxOT.prefissoNumeroPagina || '' : null);
            paramsArr.push($_isDoublePageEnabledOT ? $_nomenclaturaDxOT.numeroPagina + i : null);
            paramsArr.push($_isDoublePageEnabledOT ? $_nomenclaturaDxOT.numeroPaginaEnd || 0 : 0);
            paramsArr.push($_isDoublePageEnabledOT ? $_nomenclaturaDxOT.internoPagina : null);
            paramsArr.push($_isDoublePageEnabledOT ? $_nomenclaturaDxOT.tipoPagina : null);
            paramsArr.push($_isDoublePageEnabledOT && $_nomenclaturaDxOT.eccezione ? $_nomenclaturaDxOT.eccezione.metis_code : null);
            paramsArr.push($_isDoublePageEnabledOT && $_nomenclaturaDxOT.eccezione ? $_nomenclaturaDxOT.eccezione.name_short : null);
            paramsArr.push($_isDoublePageEnabledOT && $_nomenclaturaDxOT.eccezione ? $_nomenclaturaDxOT.eccezione.valore : null);
            paramsArr.push($_isDoublePageEnabledOT && $_nomenclaturaDxOT.eccezione ? $_nomenclaturaDxOT.eccezione.parte : null);
            paramsArr.push($_isDoublePageEnabledOT ? buildDisplayName($_nomenclaturaDxOT, i) : '');
            paramsArr.push($_isDoublePageEnabledOT ? $_cropLeftAreaPixels?.width || null : $_cropAreaPixels?.width || null);
            paramsArr.push($_isDoublePageEnabledOT ? $_cropLeftAreaPixels?.height || null : $_cropAreaPixels?.height || null);

            const response = await window.electron.database("db-add-scansione", paramsArr);

            const destinationPath = await window.electron.filesystem("fs-join-path", [ $_selectedJob.scan_path, response.insertId.toString() ]);
            await window.electron.filesystem("fs-copy-file-from-public", {source: "fake_image.tif", destination: destinationPath + '_0.tif'});
            if($_isDoublePageEnabledOT)
                await window.electron.filesystem("fs-copy-file-from-public", {source: "fake_image.tif", destination: destinationPath + '_1.tif'});

            /* If is an alpha, update ordinatore with the next one */
            if($_isDoublePageEnabledOT){
                const ordinatoreSxToSet = ordinatoriList.find((ordinatore) => ordinatore.metis_code == $_nomenclaturaSxOT.ordinatore.next_ordinatore);
                if(ordinatoreSxToSet) $_nomenclaturaSxOT.ordinatore = ordinatoreSxToSet;

                const ordinatoreDxToSet = ordinatoriList.find((ordinatore) => ordinatore.metis_code == $_nomenclaturaDxOT.ordinatore.next_ordinatore);
                if(ordinatoreDxToSet) $_nomenclaturaDxOT.ordinatore = ordinatoreDxToSet;
            } else {
                const ordinatoreSxToSet = ordinatoriList.find((ordinatore) => ordinatore.metis_code == $_nomenclaturaSxOT.ordinatore.next_ordinatore);
                if(ordinatoreSxToSet) $_nomenclaturaSxOT.ordinatore = ordinatoreSxToSet;
            }
        }
    }

    const buildDisplayName = (nomenclatura, pageNumberIncrement) => {
        if(!nomenclatura?.ordinatore) return '';

        let displayName = '';

        /* INTERNO */
        if(nomenclatura.internoPagina > 0)
            displayName = `-sub.${String(nomenclatura.internoPagina).padStart(4, '0')}_`;

        /* ORDINATORE */
        displayName += nomenclatura.ordinatore.name_short;

        /* PAGE NUMBER */
        /* Prefix */
        if(nomenclatura.ordinatore.page_number_prefix)
            displayName += `${nomenclatura.ordinatore.page_number_prefix}`;
        /* Value */
        switch (nomenclatura.ordinatore.page_number_type) {
            case 'number':
                if(nomenclatura.ordinatore.name_short == 'nf' || nomenclatura.ordinatore.name_short == 'np')
                    displayName += `[${String(nomenclatura.numeroPagina + pageNumberIncrement)}${nomenclatura.tipoPagina?.charAt(0) || ''}]`;
                else if(nomenclatura.ordinatore.roman_number)
                    displayName += `${romanize(nomenclatura.numeroPagina + pageNumberIncrement)}`;
                else
                    displayName += `${String(nomenclatura.numeroPagina + pageNumberIncrement).padStart(nomenclatura.ordinatore.page_number_pad, '0')}`;
                break;
            case 'text':
                displayName += `${String(nomenclatura.letteraPagina)}`;
                break;
            case 'letter':
                displayName += `${nomenclatura.letteraPagina == 'pi' ? 'pi.greco' : nomenclatura.letteraPagina}${Number.isFinite(Number(nomenclatura.letteraPagina)) ? '.' : ''}${nomenclatura.numeroPagina ? String(nomenclatura.numeroPagina + pageNumberIncrement).padStart(nomenclatura.ordinatore.page_number_pad, '0') : ''}`;
                break;
            case 'column_number':
                if(nomenclatura.ordinatore.roman_number)
                    displayName += `${romanize(nomenclatura.numeroPagina + pageNumberIncrement)}-${romanize(nomenclatura.numeroPaginaEnd + pageNumberIncrement)}`;
                else
                    displayName += `${String(nomenclatura.numeroPagina + pageNumberIncrement).padStart(nomenclatura.ordinatore.page_number_pad, '0')}-${String(nomenclatura.numeroPaginaEnd + pageNumberIncrement).padStart(nomenclatura.ordinatore.page_number_pad, '0')}`;
                break;
            case 'column_letter':
                displayName += `${nomenclatura.letteraPagina == 'pi' ? 'pi.greco' : nomenclatura.letteraPagina}-${nomenclatura.letteraPaginaEnd}`;
                break;
            default:
                break;
        }

        /* PAGE TYPE */
        if(nomenclatura.ordinatore.page_type_enabled && nomenclatura.ordinatore.name_short != 'nf')
            displayName += nomenclatura.tipoPagina?.charAt(0) || '';

        /* ECCEZIONE */
        if(nomenclatura.eccezione){
            /* Valore */
            displayName += `.[${String(nomenclatura.eccezione.valore).padStart(2, '0')}`;

            /* Codice */
            displayName += `.${nomenclatura.eccezione.name_short}`;

            /* Parte */
            if(nomenclatura.eccezione.parte == 0 || nomenclatura.eccezione.parte_as_number)
                displayName += `.${String(nomenclatura.eccezione.parte).padStart(4, '0')}]`;
            else
                displayName += `.${String.fromCharCode(nomenclatura.eccezione.parte + 96).padEnd(4, '0')}]`;
        }

        return displayName;
    }

    const handleClose = () => {
        d3.select('#preview-visualizer-table-tools').dispatch('unselect-tool', {detail: {name: 'Immagine fittizia'}});

        _isAddingFakeImages.set(false);
    }
</script>

<div class="container">
    <div class="paper-container">
        <Paper elevation=3>
            <Title>Inserimento Immagini Fittizie</Title>
            <Content>
                <div class="content-container">
                    <div class="controls-column-container">
                        <div class="controls-row">
                            <div class="control-column">
                                <p>Dove inserire l'immagine/i rispetto a quella selezionata?</p>
                                <div class="control-inner-row">
                                    <FormField>
                                        <Radio
                                            bind:group={position}
                                            value='above'
                                        />
                                        <span slot="label">
                                            Sopra
                                        </span>
                                    </FormField>
                                    <FormField>
                                        <Radio
                                            bind:group={position}
                                            value='below'
                                        />
                                        <span slot="label">
                                            Sotto
                                        </span>
                                    </FormField>
                                </div>
                            </div>
                            <div class="control-column">
                                <p>Quante immagini inserire?</p>
                                <Textfield
                                    style="max-width: 140px;"
                                    bind:value={numberOfImages}
                                    label="Numero di immagini"
                                    type="number"
                                    input$min="1"
                                />
                            </div>
                        </div>
                        <div class="control-column">
                            <p>Definire di seguito la nomenclatura da utilizzare</p>
                            <NomenclatureOT {selectedScan}/>
                        </div>
                    </div>
                    <div class="buttons-container">
                        <Button on:click={handleConfirm} variant="raised">
                            <Label>Conferma</Label>
                        </Button>
                        <Button on:click={handleClose} variant="raised" color="secondary">
                            <Label>Chiudi</Label>
                        </Button>
                    </div>
                </div>
            </Content>
        </Paper>
    </div>
</div>

<style>
    .container {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
    }

    .paper-container {
        min-width: 50%;
    }

    .content-container {
        display: flex;
        flex-direction: column;
        gap: 50px;
    }

    .controls-column-container {
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        align-items: center;
        width: 100%;
        gap: 20px;
    }

    .controls-row {
        display: flex;
        align-items: flex-start;
        gap: 40px;
    }

    .control-inner-row {
        display: flex;
        gap: 20px;
    }

    .control-column {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .buttons-container {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 10px;
    }

    * :global(.mdc-form-field) {
        color: var(--mdc-theme-secondary);
    }

    * :global(.smui-paper) {
        padding: 30px;
    }
</style>