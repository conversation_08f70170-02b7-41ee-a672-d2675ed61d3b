<script>
    import { _isMultiEditingScan, _user, _scannerId } from '@store';
    import { notify } from '@utility';
    import { onMount } from 'svelte';
    import * as d3 from 'd3-selection';

    //smui
    import Paper, { Title, Content } from '@smui/paper';
    import Button, { Label } from '@smui/button';
    import Tab, { Label as TabLabel } from '@smui/tab';
    import TabBar from '@smui/tab-bar';
    import Textfield from '@smui/textfield';
    import Select, { Option } from '@smui/select';
    import Autocomplete from '@smui-extra/autocomplete';
    import Checkbox from '@smui/checkbox';
    import Radio from '@smui/radio';
    import FormField from '@smui/form-field';

    export let selectedScanArr;
    let tabs = ['Pagina', 'Ordinatore', 'Eccezione', 'Interni'];
    let activeTab = tabs[0];

    let componentDidMount;
    let ordinatoriList = [];
    let eccezioniList = [];
    
    let selectedSide = 'both';
    let pagina = 1;
    let pageType;
    let ordinatore;
    let eccezione;
    let valoreEccezione = 1;
    let valoreIncrementa = false;
    let parteEccezione = 0;
    let parteIncrementa = false;
    let interni = 0;

    onMount(async () => {
        /* get ordinatori list */
        await getOrdinatori();

        /* get eccezioni list */
        await getEccezioni();

        componentDidMount = true;
    });

    const getOrdinatori = async () => {
        /* Get ordinatori */
        ordinatoriList = await window.electron.database('db-get-ordinatori');
    }

    const getEccezioni = async () => {
        /* Get eccezioni */
        eccezioniList = await window.electron.database('db-get-eccezioni');
    }

    const handleEccezioneChange = () => {
        valoreEccezione = eccezione.starting_value;
        parteEccezione = 0;
    }

    const handleConfirm = async () => {
        /* Preliminary checks to verify that some changes are to be made */
        const [shouldProceed, notificationMessage] = preliminaryChecks();

        if(!shouldProceed) return notify.warning(notificationMessage);

        /* If checks are successful, proceed and rename scan */
        await applyChanges();

        /* Refresh scan table */
        d3.select('#preview-visualizer').dispatch('refresh-scan-table');

        /* Close form */
        handleClose();
    }

    const preliminaryChecks = () => {
        let shouldProceed = true;
        let notificationMessage;

        switch (activeTab) {
            case 'Pagina':
                if(pagina == 0 && !pageType){
                    shouldProceed = false;
                    notificationMessage = 'Nessuna modifica da fare, impostare l\'incremento di pagina e/o cambiare tipo di pagina (recto/verso)';
                }
                break;
            case 'Ordinatore':
                if(!ordinatore){
                    shouldProceed = false;
                    notificationMessage = 'Nessuna modifica da fare, selezionare un ordinatore';
                }
                break;
            default:
                break;
        }

        return [shouldProceed, notificationMessage];
    }

    const applyChanges = async () => {
        if(activeTab == 'Pagina') await updatePagina();
        if(activeTab == 'Ordinatore') await updateOrdinatore();
        if(activeTab == 'Eccezione') await updateEccezione();
        if(activeTab == 'Interni') await updateInterni();
    }

    const updatePagina = async () => {
        for(const scan of selectedScanArr) {
            let newPaginaSx, newPageTypeSx, displayNameSx, newPaginaDx, newPageTypeDx, displayNameDx;

            if(selectedSide != 'dx'){
                /* Calcolo nuovi valori per pagina sinistra  */
                newPaginaSx = scan.numero_pagina_sx + pagina;
                newPageTypeSx = !pageType ? scan.tipo_pagina_sx : pageType;
                displayNameSx = buildDisplayNameForPageUpdate(scan, 'sx', newPaginaSx, newPageTypeSx);
            }

            if(selectedSide != 'sx'){
                /* Calcolo nuovi valori per pagina destra */
                newPaginaDx = scan.pagina_doppia ? scan.numero_pagina_dx + pagina : null;
                newPageTypeDx = scan.pagina_doppia ? !pageType ? scan.tipo_pagina_dx : pageType : null;
                displayNameDx = scan.pagina_doppia ? buildDisplayNameForPageUpdate(scan, 'dx', newPaginaDx, newPageTypeDx) : '';
            }
            
            /* populate params and send command */
            let params = {};
            params.scanId = scan.id;
            params.paginaSx = selectedSide != 'dx' ? newPaginaSx : scan.numero_pagina_sx;
            params.pageTypeSx = selectedSide != 'dx' ? newPageTypeSx : scan.tipo_pagina_sx;
            params.displayNameSx = selectedSide != 'dx' ? displayNameSx : scan.display_name_sx;
            params.paginaDx = selectedSide != 'sx' ? newPaginaDx : scan.numero_pagina_dx;
            params.pageTypeDx = selectedSide != 'sx' ? newPageTypeDx : scan.tipo_pagina_dx;
            params.displayNameDx = selectedSide != 'sx' ? displayNameDx : scan.display_name_dx;

            await window.electron.database('db-log', {level:'INFO', text:'[MultiEditScanForm.svelte][updatePagina] Modifying page of scan with id: ' + scan.id});
            const response = await window.electron.database("db-update-pagina-scansione", params);

            if(response.changedRows > 0) {
                /* Log Event */
                let eventText = '';
                eventText += `[${new Date().toLocaleString('it-IT').replace(',', '')}]\n`;
                eventText += `Evento: Rinominazione Immagine\n`;
                eventText += `Gruppo: ${$_user.company} - Operatore: ${$_user.displayName || $_user.username} - Livello: ${$_user.profile}\n`;
                eventText += `ID : ${$_scannerId}\n`;
                eventText += `Indice acquisizione modificato #${scan.sequenziale_scansione}\n`;
                eventText += scan.pagina_doppia ? `${displayNameSx}, ${displayNameDx} (era: ${scan.display_name_sx}, ${scan.display_name_dx} )\n\n` : `${displayNameSx} (era: ${scan.display_name_sx} )\n\n`;
                await window.electron.database('db-log', {level:'EVENT', text: eventText, eventType: 'Rinominazione Immagine'});
            }
        }
    }

    const updateOrdinatore = async () => {
        for(const scan of selectedScanArr) {
            let codiceOrdinatoreSx, nomeOrdinatoreSx, displayNameSx, codiceOrdinatoreDx, nomeOrdinatoreDx, displayNameDx;

            if(selectedSide != 'dx'){
                /* Calcolo nuovi valori per pagina sinistra  */
                codiceOrdinatoreSx = ordinatore.metis_code;
                nomeOrdinatoreSx = ordinatore.name_short;
                displayNameSx = buildDisplayNameForOrdinatoreUpdate(scan, 'sx', ordinatore);
            }

            if(selectedSide != 'sx'){
                /* Calcolo nuovi valori per pagina sinistra  */
                codiceOrdinatoreDx = scan.pagina_doppia ? ordinatore.metis_code : null;
                nomeOrdinatoreDx = scan.pagina_doppia ? ordinatore.name_short : null;
                displayNameDx = scan.pagina_doppia ? buildDisplayNameForOrdinatoreUpdate(scan, 'dx', ordinatore) : '';
            }

            /* populate params and send command */
            let params = {};
            params.scanId = scan.id;
            params.codiceOrdinatoreSx = selectedSide != 'dx' ? codiceOrdinatoreSx : scan.codice_ordinatore_sx;
            params.nomeOrdinatoreSx = selectedSide != 'dx' ? nomeOrdinatoreSx : scan.nome_ordinatore_sx;
            params.displayNameSx = selectedSide != 'dx' ? displayNameSx : scan.display_name_sx;
            params.codiceOrdinatoreDx = selectedSide != 'sx' ? codiceOrdinatoreDx : scan.codice_ordinatore_dx;
            params.nomeOrdinatoreDx = selectedSide != 'sx' ? nomeOrdinatoreDx : scan.nome_ordinatore_dx;
            params.displayNameDx = selectedSide != 'sx' ? displayNameDx : scan.display_name_dx;

            await window.electron.database('db-log', {level:'INFO', text:'[MultiEditScanForm.svelte][updateOrdinatore] Modifying ordinatore of scan with id: ' + scan.id});
            const response = await window.electron.database("db-update-ordinatore-scansione", params);

            if(response.changedRows > 0) {
                /* Log Event */
                let eventText = '';
                eventText += `[${new Date().toLocaleString('it-IT').replace(',', '')}]\n`;
                eventText += `Evento: Rinominazione Immagine\n`;
                eventText += `Gruppo: ${$_user.company} - Operatore: ${$_user.displayName || $_user.username} - Livello: ${$_user.profile}\n`;
                eventText += `ID : ${$_scannerId}\n`;
                eventText += `Indice acquisizione modificato #${scan.sequenziale_scansione}\n`;
                eventText += scan.pagina_doppia ? `${displayNameSx}, ${displayNameDx} (era: ${scan.display_name_sx}, ${scan.display_name_dx} )\n\n` : `${displayNameSx} (era: ${scan.display_name_sx} )\n\n`;
                await window.electron.database('db-log', {level:'EVENT', text: eventText, eventType: 'Rinominazione Immagine'});
            }
        }
    }

    const updateEccezione = async () => {
        for(const scan of selectedScanArr) {
            /* Set valore and parte */
            if(eccezione){
                eccezione.valore = valoreEccezione;
                eccezione.parte = parteEccezione;
            }

            let codiceEccezioneSx, nomeEccezioneSx, valoreEccezioneSx, parteEccezioneSx, displayNameSx;
            let codiceEccezioneDx, nomeEccezioneDx, valoreEccezioneDx, parteEccezioneDx, displayNameDx;

            if(selectedSide != 'dx'){
                /* Calcolo nuovi valori per pagina sinistra  */
                codiceEccezioneSx = eccezione ? eccezione.metis_code : null;
                nomeEccezioneSx = eccezione ? eccezione.name_short : null;
                valoreEccezioneSx = eccezione ? eccezione.valore : null;
                parteEccezioneSx = eccezione ? eccezione.parte : null;
                displayNameSx = buildDisplayNameForEccezioneUpdate(scan, 'sx', eccezione);
            }

            if(selectedSide != 'sx'){
                /* Calcolo nuovi valori per pagina sinistra  */
                codiceEccezioneDx = scan.pagina_doppia ? eccezione ? eccezione.metis_code : null : null;
                nomeEccezioneDx = scan.pagina_doppia ? eccezione ? eccezione.name_short : null : null;
                valoreEccezioneDx = eccezione ? eccezione.valore : null;
                parteEccezioneDx = eccezione ? eccezione.parte : null;
                displayNameDx = scan.pagina_doppia ? buildDisplayNameForEccezioneUpdate(scan, 'dx', eccezione) : '';
            }

            /* populate params and send command */
            let params = {};
            params.scanId = scan.id;
            params.codiceEccezioneSx = selectedSide != 'dx' ? codiceEccezioneSx : scan.codice_eccezione_sx;
            params.nomeEccezioneSx = selectedSide != 'dx' ? nomeEccezioneSx : scan.nome_eccezione_sx;
            params.valoreEccezioneSx = selectedSide != 'dx' ? valoreEccezioneSx : scan.valore_eccezione_sx;
            params.parteEccezioneSx = selectedSide != 'dx' ? parteEccezioneSx : scan.parte_eccezione_sx;
            params.displayNameSx = selectedSide != 'dx' ? displayNameSx : scan.display_name_sx;
            params.codiceEccezioneDx = selectedSide != 'sx' ? codiceEccezioneDx : scan.codice_eccezione_dx;
            params.nomeEccezioneDx = selectedSide != 'sx' ? nomeEccezioneDx : scan.nome_eccezione_dx;
            params.valoreEccezioneDx = selectedSide != 'sx' ? valoreEccezioneDx : scan.valore_eccezione_dx;
            params.parteEccezioneDx = selectedSide != 'sx' ? parteEccezioneDx : scan.parte_eccezione_dx;
            params.displayNameDx = selectedSide != 'sx' ? displayNameDx : scan.display_name_dx;

            await window.electron.database('db-log', {level:'INFO', text:'[MultiEditScanForm.svelte][updateEccezione] Modifying eccezione of scan with id: ' + scan.id});
            const response = await window.electron.database("db-update-eccezione-scansione", params);

            if(response.changedRows > 0) {
                /* Log Event */
                let eventText = '';
                eventText += `[${new Date().toLocaleString('it-IT').replace(',', '')}]\n`;
                eventText += `Evento: Rinominazione Immagine\n`;
                eventText += `Gruppo: ${$_user.company} - Operatore: ${$_user.displayName || $_user.username} - Livello: ${$_user.profile}\n`;
                eventText += `ID : ${$_scannerId}\n`;
                eventText += `Indice acquisizione modificato #${scan.sequenziale_scansione}\n`;
                eventText += scan.pagina_doppia ? `${displayNameSx}, ${displayNameDx} (era: ${scan.display_name_sx}, ${scan.display_name_dx} )\n\n` : `${displayNameSx} (era: ${scan.display_name_sx} )\n\n`;
                await window.electron.database('db-log', {level:'EVENT', text: eventText, eventType: 'Rinominazione Immagine'});
            }

            if(valoreIncrementa) valoreEccezione++;
            if(parteIncrementa) parteEccezione++;
        }
    }

    const updateInterni = async () => {
        for(const scan of selectedScanArr) {
            let newInternoSx, displayNameSx, newInternoDx, displayNameDx;

            if(selectedSide != 'dx'){
                /* Calcolo nuovi valori per pagina sinistra  */
                newInternoSx = interni;
                displayNameSx = buildDisplayNameForInterniUpdate(scan, 'sx', newInternoSx);
            }

            if(selectedSide != 'sx'){
                /* Calcolo nuovi valori per pagina destra */
                newInternoDx = scan.pagina_doppia ? interni : null;
                displayNameDx = scan.pagina_doppia ? buildDisplayNameForInterniUpdate(scan, 'dx', newInternoDx) : '';
            }
            
            /* populate params and send command */
            let params = {};
            params.scanId = scan.id;
            params.internoSx = selectedSide != 'dx' ? newInternoSx : scan.interno_pagina_sx;
            params.displayNameSx = selectedSide != 'dx' ? displayNameSx : scan.display_name_sx;
            params.internoDx = selectedSide != 'sx' ? newInternoDx : scan.interno_pagina_dx;
            params.displayNameDx = selectedSide != 'sx' ? displayNameDx : scan.display_name_dx;

            await window.electron.database('db-log', {level:'INFO', text:'[MultiEditScanForm.svelte][updateInterni] Modifying interni of scan with id: ' + scan.id});
            const response = await window.electron.database("db-update-interni-scansione", params);

            if(response.changedRows > 0) {
                /* Log Event */
                let eventText = '';
                eventText += `[${new Date().toLocaleString('it-IT').replace(',', '')}]\n`;
                eventText += `Evento: Rinominazione Immagine\n`;
                eventText += `Gruppo: ${$_user.company} - Operatore: ${$_user.displayName || $_user.username} - Livello: ${$_user.profile}\n`;
                eventText += `ID : ${$_scannerId}\n`;
                eventText += `Indice acquisizione modificato #${scan.sequenziale_scansione}\n`;
                eventText += scan.pagina_doppia ? `${displayNameSx}, ${displayNameDx} (era: ${scan.display_name_sx}, ${scan.display_name_dx} )\n\n` : `${displayNameSx} (era: ${scan.display_name_sx} )\n\n`;
                await window.electron.database('db-log', {level:'EVENT', text: eventText, eventType: 'Rinominazione Immagine'});
            }
        }
    }

    const buildDisplayNameForPageUpdate = (scan, type, pageNumber, pageType) => {
        /* Get ordinatori, eccezioni and interni of the selected scan, needed to build the display name */
        const ordinatoreSx = ordinatoriList.find((ordinatore) => ordinatore.metis_code == scan.codice_ordinatore_sx);
        const ordinatoreDx = ordinatoriList.find((ordinatore) => ordinatore.metis_code == scan.codice_ordinatore_dx);
        const eccezioneSx = eccezioniList.find((eccezione) => eccezione.metis_code == scan.codice_eccezione_sx);
        const eccezioneDx = eccezioniList.find((eccezione) => eccezione.metis_code == scan.codice_eccezione_dx);
        const interniSx = scan.interno_pagina_sx;
        const interniDx = scan.interno_pagina_dx;
        const pageTypeSx = type == 'sx' ? pageType : null;
        const pageTypeDx = type == 'dx' ? pageType : null;

        const tipoPaginaSx = pageTypeSx?.charAt(0) || '';
        const tipoPaginaDx = pageTypeDx?.charAt(0) || '';

        let displayName = '';

        if (type == 'sx' && ordinatoreSx) {
            if(ordinatoreSx.is_alpha)
                displayName = ordinatoreSx.name_short
            else if(interniSx > 0)
                displayName = '-sub.' + String(interniSx).padStart(4, '0') + '_' + ordinatoreSx.name_short + '_' + String(pageNumber).padStart(4, '0') + tipoPaginaSx;
            else
                displayName = ordinatoreSx.name_short + '_' + String(pageNumber).padStart(4, '0') + tipoPaginaSx;
            
            if(eccezioneSx){
                if(eccezioneSx.parte > 0){
                    if(eccezioneSx.parte_as_number)
                        displayName += '.[' + String(eccezioneSx.valore).padStart(2, '0') + '.' + eccezioneSx.name_short + '.' + String(eccezioneSx.parte).padStart(4, '0') + ']';
                    else
                        displayName += '.[' + String(eccezioneSx.valore).padStart(2, '0') + '.' + eccezioneSx.name_short + '.' + String.fromCharCode(eccezioneSx.parte + 96).padEnd(4, '0') + ']';
                } else
                    displayName = displayName + '.[' + String(eccezioneSx.valore).padStart(2, '0') + '.' + eccezioneSx.name_short + '.0000]';  
            }
        } else if (type == 'dx' && ordinatoreDx) {
            if(interniDx > 0)
                displayName = '-sub.' + String(interniDx).padStart(4, '0') + '_' + ordinatoreDx.name_short + '_' + String(pageNumber).padStart(4, '0') + tipoPaginaDx;
            else
                displayName = ordinatoreDx.name_short + '_' + String(pageNumber).padStart(4, '0') + tipoPaginaDx;
            
            if(eccezioneDx){
                if(eccezioneDx.parte > 0){
                    if(eccezioneDx.parte_as_number)
                        displayName += '.[' + String(eccezioneDx.valore).padStart(2, '0') + '.' + eccezioneDx.name_short + '.' + String(eccezioneDx.parte).padStart(4, '0') + ']';
                    else
                        displayName += '.[' + String(eccezioneDx.valore).padStart(2, '0') + '.' + eccezioneDx.name_short + '.' + String.fromCharCode(eccezioneDx.parte + 96).padEnd(4, '0') + ']';
                } else
                    displayName = displayName + '.[' + String(eccezioneDx.valore).padStart(2, '0') + '.' + eccezioneDx.name_short + '.0000]';
            }
        }

        return displayName;
    }

    const buildDisplayNameForOrdinatoreUpdate = (scan, type, ordinatore) => {
        /* Get ordinatori, eccezioni and interni of the selected scan, needed to build the display name */
        const ordinatoreSx = type == 'sx' ? ordinatore : null;
        const ordinatoreDx = type == 'dx' ? ordinatore : null;
        const eccezioneSx = eccezioniList.find((eccezione) => eccezione.metis_code == scan.codice_eccezione_sx);
        const eccezioneDx = eccezioniList.find((eccezione) => eccezione.metis_code == scan.codice_eccezione_dx);
        const interniSx = scan.interno_pagina_sx;
        const interniDx = scan.interno_pagina_dx;
        const paginaSx = scan.numero_pagina_sx;
        const paginaDx = scan.numero_pagina_dx;
        const pageTypeSx = scan.tipo_pagina_sx;
        const pageTypeDx = scan.tipo_pagina_dx;

        const tipoPaginaSx = pageTypeSx?.charAt(0) || '';
        const tipoPaginaDx = pageTypeDx?.charAt(0) || '';

        let displayName = '';

        if (type == 'sx' && ordinatoreSx) {
            if(ordinatoreSx.is_alpha)
                displayName = ordinatoreSx.name_short
            else if(interniSx > 0)
                displayName = '-sub.' + String(interniSx).padStart(4, '0') + '_' + ordinatoreSx.name_short + '_' + String(paginaSx).padStart(4, '0') + tipoPaginaSx;
            else
                displayName = ordinatoreSx.name_short + '_' + String(paginaSx).padStart(4, '0') + tipoPaginaSx;
            
            if(eccezioneSx){
                if(eccezioneSx.parte > 0){
                    if(eccezioneSx.parte_as_number)
                        displayName += '.[' + String(eccezioneSx.valore).padStart(2, '0') + '.' + eccezioneSx.name_short + '.' + String(eccezioneSx.parte).padStart(4, '0') + ']';
                    else
                        displayName += '.[' + String(eccezioneSx.valore).padStart(2, '0') + '.' + eccezioneSx.name_short + '.' + String.fromCharCode(eccezioneSx.parte + 96).padEnd(4, '0') + ']';
                } else
                    displayName = displayName + '.[' + String(eccezioneSx.valore).padStart(2, '0') + '.' + eccezioneSx.name_short + '.0000]';  
            }
        } else if (type == 'dx' && ordinatoreDx) {
            if(interniDx > 0)
                displayName = '-sub.' + String(interniDx).padStart(4, '0') + '_' + ordinatoreDx.name_short + '_' + String(paginaDx).padStart(4, '0') + tipoPaginaDx;
            else
                displayName = ordinatoreDx.name_short + '_' + String(paginaDx).padStart(4, '0') + tipoPaginaDx;
            
            if(eccezioneDx){
                if(eccezioneDx.parte > 0){
                    if(eccezioneDx.parte_as_number)
                        displayName += '.[' + String(eccezioneDx.valore).padStart(2, '0') + '.' + eccezioneDx.name_short + '.' + String(eccezioneDx.parte).padStart(4, '0') + ']';
                    else
                        displayName += '.[' + String(eccezioneDx.valore).padStart(2, '0') + '.' + eccezioneDx.name_short + '.' + String.fromCharCode(eccezioneDx.parte + 96).padEnd(4, '0') + ']';
                } else
                    displayName = displayName + '.[' + String(eccezioneDx.valore).padStart(2, '0') + '.' + eccezioneDx.name_short + '.0000]';
            }
        }

        return displayName;
    }

    const buildDisplayNameForEccezioneUpdate = (scan, type, eccezione) => {
        /* Get ordinatori, eccezioni and interni of the selected scan, needed to build the display name */
        const ordinatoreSx = ordinatoriList.find((ordinatore) => ordinatore.metis_code == scan.codice_ordinatore_sx);
        const ordinatoreDx = ordinatoriList.find((ordinatore) => ordinatore.metis_code == scan.codice_ordinatore_dx);
        const eccezioneSx = type == 'sx' ? eccezione : null;
        const eccezioneDx = type == 'dx' ? eccezione : null;
        const interniSx = scan.interno_pagina_sx;
        const interniDx = scan.interno_pagina_dx;
        const paginaSx = scan.numero_pagina_sx;
        const paginaDx = scan.numero_pagina_dx;
        const pageTypeSx = scan.tipo_pagina_sx;
        const pageTypeDx = scan.tipo_pagina_dx;
        
        const tipoPaginaSx = pageTypeSx?.charAt(0) || '';
        const tipoPaginaDx = pageTypeDx?.charAt(0) || '';

        let displayName = '';

        if (type == 'sx' && ordinatoreSx) {
            if(ordinatoreSx.is_alpha)
                displayName = ordinatoreSx.name_short
            else if(interniSx > 0)
                displayName = '-sub.' + String(interniSx).padStart(4, '0') + '_' + ordinatoreSx.name_short + '_' + String(paginaSx).padStart(4, '0') + tipoPaginaSx;
            else
                displayName = ordinatoreSx.name_short + '_' + String(paginaSx).padStart(4, '0') + tipoPaginaSx;
            
            if(eccezioneSx){
                if(eccezioneSx.parte > 0){
                    if(eccezioneSx.parte_as_number)
                        displayName += '.[' + String(eccezioneSx.valore).padStart(2, '0') + '.' + eccezioneSx.name_short + '.' + String(eccezioneSx.parte).padStart(4, '0') + ']';
                    else
                        displayName += '.[' + String(eccezioneSx.valore).padStart(2, '0') + '.' + eccezioneSx.name_short + '.' + String.fromCharCode(eccezioneSx.parte + 96).padEnd(4, '0') + ']';
                } else
                    displayName = displayName + '.[' + String(eccezioneSx.valore).padStart(2, '0') + '.' + eccezioneSx.name_short + '.0000]';  
            }
        } else if (type == 'dx' && ordinatoreDx) {
            if(interniDx > 0)
                displayName = '-sub.' + String(interniDx).padStart(4, '0') + '_' + ordinatoreDx.name_short + '_' + String(paginaDx).padStart(4, '0') + tipoPaginaDx;
            else
                displayName = ordinatoreDx.name_short + '_' + String(paginaDx).padStart(4, '0') + tipoPaginaDx;
            
            if(eccezioneDx){
                if(eccezioneDx.parte > 0){
                    if(eccezioneDx.parte_as_number)
                        displayName += '.[' + String(eccezioneDx.valore).padStart(2, '0') + '.' + eccezioneDx.name_short + '.' + String(eccezioneDx.parte).padStart(4, '0') + ']';
                    else
                        displayName += '.[' + String(eccezioneDx.valore).padStart(2, '0') + '.' + eccezioneDx.name_short + '.' + String.fromCharCode(eccezioneDx.parte + 96).padEnd(4, '0') + ']';
                } else
                    displayName = displayName + '.[' + String(eccezioneDx.valore).padStart(2, '0') + '.' + eccezioneDx.name_short + '.0000]';
            }
        }

        return displayName;
    }

    const buildDisplayNameForInterniUpdate = (scan, type, interni) => {
        /* Get ordinatori, eccezioni and interni of the selected scan, needed to build the display name */
        const ordinatoreSx = ordinatoriList.find((ordinatore) => ordinatore.metis_code == scan.codice_ordinatore_sx);
        const ordinatoreDx = ordinatoriList.find((ordinatore) => ordinatore.metis_code == scan.codice_ordinatore_dx);
        const eccezioneSx = eccezioniList.find((eccezione) => eccezione.metis_code == scan.codice_eccezione_sx);
        const eccezioneDx = eccezioniList.find((eccezione) => eccezione.metis_code == scan.codice_eccezione_dx);
        const interniSx = type == 'sx' ? interni : null;
        const interniDx = type == 'dx' ? interni : null;
        const paginaSx = scan.numero_pagina_sx;
        const paginaDx = scan.numero_pagina_dx;
        const pageTypeSx = scan.tipo_pagina_sx;
        const pageTypeDx = scan.tipo_pagina_dx;
         
        const tipoPaginaSx = pageTypeSx?.charAt(0) || '';
        const tipoPaginaDx = pageTypeDx?.charAt(0) || '';

        let displayName = '';

        if (type == 'sx' && ordinatoreSx) {
            if(ordinatoreSx.is_alpha)
                displayName = ordinatoreSx.name_short
            else if(interniSx > 0)
                displayName = '-sub.' + String(interniSx).padStart(4, '0') + '_' + ordinatoreSx.name_short + '_' + String(paginaSx).padStart(4, '0') + tipoPaginaSx;
            else
                displayName = ordinatoreSx.name_short + '_' + String(paginaSx).padStart(4, '0') + tipoPaginaSx;
            
            if(eccezioneSx){
                if(eccezioneSx.parte > 0){
                    if(eccezioneSx.parte_as_number)
                        displayName += '.[' + String(eccezioneSx.valore).padStart(2, '0') + '.' + eccezioneSx.name_short + '.' + String(eccezioneSx.parte).padStart(4, '0') + ']';
                    else
                        displayName += '.[' + String(eccezioneSx.valore).padStart(2, '0') + '.' + eccezioneSx.name_short + '.' + String.fromCharCode(eccezioneSx.parte + 96).padEnd(4, '0') + ']';
                } else
                    displayName = displayName + '.[' + String(eccezioneSx.valore).padStart(2, '0') + '.' + eccezioneSx.name_short + '.0000]';  
            }
        } else if (type == 'dx' && ordinatoreDx) {
            if(interniDx > 0)
                displayName = '-sub.' + String(interniDx).padStart(4, '0') + '_' + ordinatoreDx.name_short + '_' + String(paginaDx).padStart(4, '0') + tipoPaginaDx;
            else
                displayName = ordinatoreDx.name_short + '_' + String(paginaDx).padStart(4, '0') + tipoPaginaDx;
            
            if(eccezioneDx){
                if(eccezioneDx.parte > 0){
                    if(eccezioneDx.parte_as_number)
                        displayName += '.[' + String(eccezioneDx.valore).padStart(2, '0') + '.' + eccezioneDx.name_short + '.' + String(eccezioneDx.parte).padStart(4, '0') + ']';
                    else
                        displayName += '.[' + String(eccezioneDx.valore).padStart(2, '0') + '.' + eccezioneDx.name_short + '.' + String.fromCharCode(eccezioneDx.parte + 96).padEnd(4, '0') + ']';
                } else
                    displayName = displayName + '.[' + String(eccezioneDx.valore).padStart(2, '0') + '.' + eccezioneDx.name_short + '.0000]';
            }
        }

        return displayName;
    }

    const handleClose = () => {
        d3.select('#preview-visualizer-table-tools').dispatch('unselect-tool', {detail: {name: 'Modifica massiva'}});

        _isMultiEditingScan.set(false);
    }
</script>

<div class="container">
    <div class="paper-container">
        <Paper elevation=3>
            <Title>Rinomina Massiva Scansioni</Title>
            <Content>
                <div class="content-container">
                    <div class="control-column">
                        <div class="flex-row">
                            <p>Scegliere dove effettuare le modifiche:</p>
                            <FormField>
                                <Radio bind:group={selectedSide} value={'sx'}/>
                                <span class="radio-label" slot="label">Sinistra</span>
                            </FormField>
                            <FormField>
                                <Radio bind:group={selectedSide} value={'dx'}/>
                                <span class="radio-label" slot="label">Destra</span>
                            </FormField>
                            <FormField>
                                <Radio bind:group={selectedSide} value={'both'}/>
                                <span class="radio-label" slot="label">Entrambi</span>
                            </FormField>
                        </div>
                        <TabBar tabs={tabs} let:tab bind:active={activeTab}>
                            <Tab {tab}>
                                <TabLabel>{tab}</TabLabel>
                            </Tab>
                        </TabBar>

                        {#if activeTab == 'Pagina'}
                            <p style="align-self: flex-start;">Impostare l'incremento/decremento di pagina e/o il tipo di pagina (verso/recto)</p>
                            <div class="flex-row">
                                <Textfield 
                                    style="max-width: 60px;"
                                    helperLine$style="max-width: 60px;"
                                    bind:value={pagina}
                                    label="Pagina"
                                    type="number"
                                    prefix={pagina > 0 ? "+" : ""}
                                />
                                <Select bind:value={pageType} label="Tipo">
                                    <Option value=''>--</Option>
                                    <Option value="verso">verso</Option>
                                    <Option value="recto">recto</Option>
                                </Select>
                            </div>
                            <div class="flex-column-description">
                                <p>Modifiche:</p>
                                {#if pagina != 0 || pageType}
                                    {#if pagina > 0}
                                        <p>- Il numero pagina verrà incrementato di <span>{pagina}</span></p>
                                    {:else if pagina < 0}
                                        <p>- Il numero pagina verrà decrementato di <span>{pagina.toString().slice(1)}</span></p>
                                    {/if}
                                    {#if pageType}
                                        <p>- Il tipo di pagina verrà impostato a <span>{pageType}</span></p>
                                    {/if}
                                {:else}
                                    <p>- Non verrà effettuata nessuna modifica</p>
                                {/if}
                            </div>
                        {:else if activeTab == 'Ordinatore'}
                            <p style="align-self: flex-start;">Selezionare l'ordinatore da impostare:</p>
                            <Autocomplete
                                options={ordinatoriList}
                                textfield$variant="outlined"
                                getOptionLabel={(option) => option ? `${option.name_long}` : ''}
                                bind:value={ordinatore}
                                label="Ordinatore"
                                on:focus={() => ordinatore = null}
                            />
                            <div class="flex-column-description">
                                <p>Modifiche:</p>
                                {#if ordinatore}
                                    <p>- L'ordinatore verrà impostato a <span>{ordinatore.name_short}</span></p>
                                {:else}
                                    <p>- Non verrà effetuata nessuna modifica</p>
                                {/if}
                            </div>
                        {:else if activeTab == 'Eccezione'}
                            <p style="align-self: flex-start;">Selezionare l'eccezione da impostare:</p>
                            <div class="flex-row">
                                <Autocomplete
                                    options={eccezioniList}
                                    textfield$variant="outlined"
                                    getOptionLabel={(option) => option ? `${option.name_long}` : ''}
                                    bind:value={eccezione}
                                    label="Eccezione"
                                    on:focus={() => eccezione = null}
                                    on:SMUIAutocomplete:selected={() => handleEccezioneChange()}
                                />
                                {#if eccezione}
                                    <Textfield 
                                        style="max-width: 60px;"
                                        helperLine$style="max-width: 60px;"
                                        bind:value={valoreEccezione}
                                        label="Valore"
                                        type="number"
                                        input$min="{eccezione.starting_value}"
                                    />
                                    <Textfield 
                                        style="max-width: 60px;"
                                        helperLine$style="max-width: 60px;"
                                        bind:value={parteEccezione}
                                        label="Parte"
                                        type="number"
                                        input$min="0"
                                    />
                                {/if}
                            </div>
                            {#if eccezione}
                                <div class="flex-row self-start">
                                    <p>Cosa deve incrementare automaticamente?</p>
                                    <div class="flex-inner-column">
                                        <p style="transform: scale(0.95);">Valore</p>
                                        <Checkbox bind:checked={valoreIncrementa}/>
                                    </div>
                                    <div class="flex-inner-column">
                                        <p style="transform: scale(0.95);">Parte</p>
                                        <Checkbox bind:checked={parteIncrementa}/>
                                    </div>
                                </div>
                            {/if}
                            <div class="flex-column-description">
                                <p>Modifiche:</p>
                                {#if eccezione}
                                    {#if parteEccezione > 0}
                                        <p>- L'eccezione verrà impostata a <span>{'[' + String(valoreEccezione).padStart(2, '0') + '.' + eccezione.name_short + '.' + String.fromCharCode(parteEccezione + 96).padEnd(4, '0') + ']'}</span></p>
                                    {:else}
                                        <p>- L'eccezione verrà impostata a <span>{'[' + String(valoreEccezione).padStart(2, '0') + '.' + eccezione.name_short + '.0000]'}</span></p>
                                    {/if}
                                    {#if valoreIncrementa}
                                        <p>- Il <span>valore</span> incrementerà automaticamente ad ogni scansione</p>
                                    {/if}
                                    {#if parteIncrementa}
                                        <p>- La <span>parte</span> incrementerà automaticamente ad ogni scansione</p>
                                    {/if}
                                {:else}
                                    <p>- L'eccezione verrà <span>rimossa</span> dalla nomenclatura della scansione</p>
                                {/if}
                            </div>
                        {:else if activeTab == 'Interni'}
                            <p style="align-self: flex-start;">Selezionare gli interni da impostare:</p>
                            <Textfield 
                                style="max-width: 60px;"
                                helperLine$style="max-width: 60px;"
                                bind:value={interni}
                                label="Interni"
                                type="number"
                                input$min="0"
                            />
                            <div class="flex-column-description">
                                <p>Modifiche:</p>
                                {#if interni > 0}
                                    <p>- Gli interni verranno impostati a <span>{'-sub.' + String(interni).padStart(4, '0')}</span></p>
                                {:else}
                                    <p>- Gli interni verranno <span>rimossi</span> dalla nomenclatura della scansione</p>
                                {/if}
                            </div>
                        {/if}
                    </div>
                    <div class="buttons-container">
                        <Button on:click={handleConfirm} variant="raised">
                            <Label>Conferma</Label>
                        </Button>
                        <Button on:click={handleClose} variant="raised" color="secondary">
                            <Label>Chiudi</Label>
                        </Button>
                    </div>
                </div>
            </Content>
        </Paper>
    </div>
</div>

<style>
    .container {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
    }

    .paper-container {
        min-width: 50%;
    }

    .content-container {
        display: flex;
        flex-direction: column;
        gap: 40px;
        margin-top: 20px;
    }

    .flex-row {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-evenly;
    }

    .flex-row.self-start {
        justify-self: flex-start;
    }

    .flex-column-description {
        display: flex;
        flex-direction: column;
        gap: 5px;
        margin-top: 20px;
        align-self: flex-start;
    }

    .flex-inner-column {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: 10px;
        margin-bottom: 10px;
    }

    p {
        color: var(--mdc-theme-secondary);
        margin: 0px;
    }

    span {
        color: var(--mdc-theme-primary);
    }

    .radio-label {
        color: var(--mdc-theme-secondary);
    }

    .control-column {
        display: flex;
        flex-direction: column;
        align-self: center;
        align-items: center;
        gap: 20px;
    }

    .buttons-container {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 10px;
    }

    * :global(.mdc-radio) {
        padding-right: 0px;
    }

    * :global(.mdc-form-field) {
        color: var(--mdc-theme-secondary);
    }

    * :global(.smui-paper) {
        padding: 30px;
    }
    
    * :global(.mdc-select__anchor) {
        width: fit-content;
        min-width: 90px;
    }

    * :global(.mdc-menu) {
        max-height: 500px;
    }

    * :global(.mdc-select--outlined .mdc-select__anchor) {
        width: 280px;
        height: 40px;
    }

    * :global(.smui-autocomplete__menu::-webkit-scrollbar) {
        width: 15px;
    }

    * :global(.smui-autocomplete__menu::-webkit-scrollbar-track) {
        background-color: white;
        border-radius: 10px;
    }

    * :global(.smui-autocomplete__menu::-webkit-scrollbar-thumb) {
        background-color: var(--mdc-theme-primary);
        border-radius: 10px;
    }

    * :global(.mdc-text-field--outlined) {
        height: 45px;
        width: 100%;
    }
</style>