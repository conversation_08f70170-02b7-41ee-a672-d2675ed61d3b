<script>
  import { _isEditingScan, _isMultiEditingScan, _selectedScanArr, _isShiftPressed, _isCtrlPressed } from '@store';
  import { removeAllSelectedClass, scrollToElement, scrollUpOrDown } from '@utility';
  import * as d3 from 'd3-selection';
  import Mousetrap from '@ottozz/mousetrap';
  import { onMount, createEventDispatcher } from 'svelte';

  //icons
  import InformationIcon from "svelte-material-icons/Information.svelte";

  //smui components
  import DataTable, { Head, Body, Row, Cell } from '@smui/data-table';
    
  export let options;

  const dispatch = createEventDispatcher();

  onMount(() => {
    /* Define keyboard shortcuts */
    Mousetrap.bind('down', (e) => handleArrowSelection(e, 'down'));
    Mousetrap.bind('up', (e) => handleArrowSelection(e, 'up'));

    /* Create event listeners */
    createEventListeners();

    /* notify that component is ready */
    dispatch('ready');
  })

  const createEventListeners = () => {
    d3.select('#preview-table').on('set-selected-class-and-scroll-to-element', setSelectedClassAndScrollToElement);
  }

  const setSelectedClassAndScrollToElement = () => {
    removeAllSelectedClass();
    const selectedIndex = options.findIndex(e => e.id === $_selectedScanArr[0]?.id);

    if(selectedIndex >= 0) {
      d3.select(`#row-${options[selectedIndex]?.id}`).classed('selected', true);
      scrollToElement(d3.select(".mdc-data-table").node(), d3.select(".selected").node());
    }
  }

  const setSelectedClassAndScrollUpOrDown = () => {
    removeAllSelectedClass();
    const selectedIndex = options.findIndex(e => e.id === $_selectedScanArr[0]?.id);

    if(selectedIndex >= 0) {
      d3.select(`#row-${options[selectedIndex]?.id}`).classed('selected', true);
      scrollUpOrDown(d3.select(".mdc-data-table").node(), d3.select(".selected").node());
    }
  }

  const setSelectedClassWithoutScroll = () => {
    removeAllSelectedClass();
    const selectedIndex = options.findIndex(e => e.id === $_selectedScanArr[0]?.id);

    if(selectedIndex >= 0)
      d3.select(`#row-${options[selectedIndex]?.id}`).classed('selected', true);
  }

  const handleSelection = (e, selectedScan) => {    
    if($_selectedScanArr.length > 0 && $_isShiftPressed){
      handleShiftSelection(selectedScan);
    } else if($_selectedScanArr.length > 0 && $_isCtrlPressed) {
      handleCtrlSelection(e, selectedScan);
    } else {
      handleSingleSelection(selectedScan);
    }
  }

  const handleShiftSelection = (selectedScan) => {
    /* Remove selected class to all elements */
    removeAllSelectedClass();

    /* get index of currently selected element */
    const startIndex = options.findIndex(el => el.id === $_selectedScanArr[0].id);
    const endIndex = options.findIndex(el => el.id === selectedScan.id);

    $_selectedScanArr.length = 0;
    if(startIndex < endIndex) {
      for (let index = startIndex; index <= endIndex; index++) {
        $_selectedScanArr.push(options[index]);
        d3.select(`#row-${options[index].id}`).classed('selected', true);
      }
    } else {
      for (let index = startIndex; index >= endIndex; index--) {
        $_selectedScanArr.push(options[index]);
        d3.select(`#row-${options[index].id}`).classed('selected', true);
      }
    }
  }

  const handleCtrlSelection = (e, selectedScan) => {
    const optionIndex = options.findIndex(el => el.id === selectedScan.id);

    if($_selectedScanArr.includes(options[optionIndex])){
      /* Do not allow removal if only one scan is selected */
      if($_selectedScanArr.length == 1) return;

      const selectedIndex = $_selectedScanArr.findIndex(el => el.id === selectedScan.id);

      /* Remove class to selected element */
      e.target.classList.remove("selected");

      /* Add element to selected array */
      $_selectedScanArr.splice(selectedIndex, 1);
    } else {
      /* Add class to selected element */
      e.target.classList.add('selected');

      $_selectedScanArr.push(selectedScan);
    }
      
  }
    
  const handleSingleSelection = (selectedScan) => {
    /* Clear array and add scan to it */
    $_selectedScanArr.length = 0;
    $_selectedScanArr.push(selectedScan);

    /* Set selected class without scrolling the table */
    setSelectedClassWithoutScroll();

    /* Update scan images */
    d3.select('#scan-viewers').dispatch('update-scan-images', {detail: { scan: $_selectedScanArr[0]}});
  }

  const handleArrowSelection = (e, direction) => {
    e.preventDefault();

    const selectedIndex = options.findIndex(el => el.id === $_selectedScanArr[0].id);
    const newIndex = direction == 'up' ? selectedIndex - 1 : selectedIndex + 1;

    /* If already selected row is first or last, return */
    if (!options[newIndex]) return;

    /* Clear array and add scan to it */
    $_selectedScanArr.length = 0;
    $_selectedScanArr.push(options[newIndex]);

    /* Set selected class and scroll table */
    setSelectedClassAndScrollUpOrDown();

    /* Update scan images */
    d3.select('#scan-viewers').dispatch('update-scan-images', {detail: { scan: $_selectedScanArr[0]}});
  }
</script>

<div id="preview-table" class="table-container">
  <DataTable stickyHeader>
    <Head>
      <Row>
        <Cell numeric><span class='unselectable'>#</span></Cell>
        <Cell><span class='unselectable'>Pagina di sinistra</span></Cell>
        <Cell><span class='unselectable'>Pagina di destra</span></Cell>
        <Cell ><span class='unselectable'>Note</span></Cell>
      </Row>
    </Head>
      <Body>
        {#if options.length > 0}
          {#each options as option (option.id)}
            <Row id={`row-${option?.id}`} on:SMUIDataTableRow:click={(e) => handleSelection(e, option)}>
              <Cell numeric><span class='unselectable'>{option.sequenziale_scansione}</span></Cell>
              <Cell><span class='unselectable'>{option.display_name_sx}</span></Cell>
              <Cell><span class='unselectable'>{option.display_name_dx}</span></Cell>
              <Cell>
                {#if option.testo_nota}
                  <svelte:component this={InformationIcon} width={25} height={25} viewBox={"0 0 24 20"} color={"var(--mdc-theme-secondary)"}/>
                {/if}
              </Cell>
            </Row>
          {/each}
        {/if}
      </Body>
  </DataTable>
</div>


<style>

  .table-container {
    height: 100%;
    width: 100%;
    overflow-y: hidden;
  }

  .unselectable {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  * :global(.mdc-data-table) {
    overflow-y: auto;
    overflow-x: hidden;
  }
  
  * :global(.mdc-data-table::-webkit-scrollbar) {
    width: 15px;
  }

  * :global(.mdc-data-table::-webkit-scrollbar-track) {
    background-color: white;
    border-radius: 10px;
  }

  * :global(.mdc-data-table::-webkit-scrollbar-thumb) {
    background-color: var(--mdc-theme-primary);
    border-radius: 10px;
  }
  
  .table-container::-webkit-scrollbar {
    width: 15px;
  }

  .table-container::-webkit-scrollbar-track {
    background-color: white;
    border-radius: 10px;
  }

  .table-container::-webkit-scrollbar-thumb {
    background-color: var(--mdc-theme-primary);
    border-radius: 10px;
  }

  * :global(.mdc-data-table) {
    height: 100%;
    width: 100%;
  }

  * :global(.mdc-data-table__header-row) {  
    height: min-content;
  }

  * :global(.mdc-data-table__row) {
    height: 2rem;
    cursor: pointer;
  }

  * :global(.mdc-data-table__row.selected) {
    background-color: var(--mdc-theme-primary-selected);
  }

  * :global(.mdc-data-table__cell) {
    padding: 0 10px 0 10px;
  }

  * :global(.mdc-data-table__header-cell) {
    padding: 0 10px 0 10px;
  }

</style>