<script>
    import { _selectedJob, _selectedScanArr, _isDoublePageEnabled, _isEbraicOrder, _isAddingFakeImages, _isEditingScan, _isMultiEditingScan, _user, _scannerId } from "@store";
    import ConfirmationDialog from "@components/common/ConfirmationDialog.svelte";
    import { delay, notify, removeAllSelectedClass } from "@utility";
    import * as d3 from 'd3-selection';
    import { onMount } from "svelte";

    //smui
    import SegmentedButton, { Segment } from '@smui/segmented-button';

    //icons
    import SortByOrdinatoreIcon from "svelte-material-icons/SortAlphabeticalAscending.svelte";
    import SortBySequenzialeIcon from "svelte-material-icons/SortNumericAscending.svelte";
    import FakeImageIcon from "svelte-material-icons/CameraOff.svelte";
    import RemoveIcon from "svelte-material-icons/ImageRemove.svelte";
    import EditIcon from "svelte-material-icons/ImageEdit.svelte";
    import MultiEditIcon from "svelte-material-icons/Pound.svelte";
    import MoveUpIcon from "svelte-material-icons/ArrowUp.svelte";
    import MoveDownIcon from "svelte-material-icons/ArrowDown.svelte";

    let openConfirmationDialog;
    let confirmationDialogAction;

    onMount(() => {
        createEventListeners();
    })

    const createEventListeners = () => {
        d3.select('#preview-visualizer-table-tools').on('unselect-tool', (e) => {
            selectedTools = selectedTools.filter(el => el.name !== e.detail.name);
        });
    }

    const handleAnswer = async (e) => {
        const answer = e.detail;

        /* remove selected tools */
        selectedTools = selectedTools.filter(el => el.name !== 'Cancella');

        if(!answer) return;

        if(confirmationDialogAction == 'CANCELLA SCANSIONE')
            await doRemove();
    }

    let selectedTools = [];
    const toolsSegments = [
        {
            name: 'Immagine fittizia',
            icon: FakeImageIcon,
            clickHandler: () => {
                _isAddingFakeImages.set(true);
            }
        },
        {
            name: 'Cancella',
            icon: RemoveIcon,
            clickHandler: () => {
                if($_selectedScanArr.length == 0) return notify.warning("Nessuna scansione selezionata!");

                confirmationDialogAction = 'CANCELLA SCANSIONE';
                openConfirmationDialog = true;
            }
        },
        {
            name: 'Rinomina',
            icon: EditIcon,
            clickHandler: () => {
                if($_selectedScanArr.length == 0) return notify.warning("Nessuna scansione selezionata!");
                /* if($_selectedScanArr.some(scan => scan.progressione_sx || scan.progressione_dx)) return notify.warning("Non è possibile rinominare una scansione con progressione"); */
                _isEditingScan.set(true);
            }
        },
        {
            name: 'Modifica massiva',
            icon: MultiEditIcon,
            clickHandler: () => {
                if($_selectedScanArr.length == 0) return notify.warning("Nessuna scansione selezionata!");
                /* if($_selectedScanArr.some(scan => scan.progressione_sx || scan.progressione_dx)) return notify.warning("Non è possibile modificare una scansione con progressione"); */
                _isMultiEditingScan.set(true)
            }
        },
    ]

    const doRemove = async () => {
        for(const scan of $_selectedScanArr) {
            let params = {};
            params.idDigit = $_selectedJob.id_digit;
            params.sequenzialeScansione = scan.sequenziale_scansione;
            params.step = 1;

            await window.electron.database("db-delete-scansione", { id: scan.id });
            await window.electron.database("db-decrease-seq-scansioni", params);

            /* Cancella file tif */
            let imageToDeletePath = await window.electron.filesystem("fs-join-path", [ $_selectedJob.scan_path, scan.id.toString() ]);
            await window.electron.filesystem("fs-delete-file-or-dir", imageToDeletePath + '_0.tif');
            await window.electron.filesystem("fs-delete-file-or-dir", imageToDeletePath + '_1.tif');

            /* Cancella preview jpeg */
            imageToDeletePath = await window.electron.filesystem("fs-join-path", [ $_selectedJob.preview_path, scan.id.toString() ]);
            await window.electron.filesystem("fs-delete-file-or-dir", imageToDeletePath + '_0.dzi');
            await window.electron.filesystem("fs-delete-file-or-dir", imageToDeletePath + '_0_files');
            await window.electron.filesystem("fs-delete-file-or-dir", imageToDeletePath + '_1.dzi');
            await window.electron.filesystem("fs-delete-file-or-dir", imageToDeletePath + '_1_files');

            /* Log Event */
            let eventText = '';
            eventText += `[${new Date().toLocaleString('it-IT').replace(',', '')}]\n`;
            eventText += `Evento: Cancellazione Immagine\n`;
            eventText += `Gruppo: ${$_user.company} - Operatore: ${$_user.displayName || $_user.username} - Livello: ${$_user.profile}\n`;
            eventText += `ID : ${$_scannerId}\n`;
            eventText += `Indice acquisizione cancellato # ${scan.sequenziale_scansione}\n`;
            eventText += $_isDoublePageEnabled ? `${scan.display_name_sx}, ${scan.display_name_dx}\n\n` : `${scan.display_name_sx}\n\n`;
            await window.electron.database('db-log', {level:'EVENT', text: eventText, eventType: 'Cancellazione Immagine'});
        }

        d3.select('#preview-visualizer').dispatch('refresh-scan-table');
        $_selectedScanArr.length = 0;
        removeAllSelectedClass();
    }

    const orderBySegments = [
        {
            name: 'Ordinamento di acquisizione',
            icon: SortBySequenzialeIcon,
            clickHandler: () => {
                d3.select('#preview-visualizer').dispatch('order-by-sequenziale');
            }
        },
        {
            name: 'Ordinamento finale',
            icon: SortByOrdinatoreIcon,
            clickHandler: () => {
                d3.select('#preview-visualizer').dispatch('order-by-ordinatore');
            }
        }
    ];

    let selectedSeqOrder;
    const seqOrderSegments = [
        {
            name: 'Muovi su',
            icon: MoveUpIcon,
            clickHandler: async () => {
                if($_selectedScanArr.length == 0){
                    notify.warning("Nessuna scansione selezionata!");
                    
                    /* remove selected tools */
                    await delay(100);
                    selectedSeqOrder = null;
                    return;
                }
                if($_selectedScanArr.some(scan => scan.sequenziale_scansione == 1)){
                    notify.warning("Non è possibile spostare su la scansione con sequenziale #1");
                    
                    /* remove selected tools */
                    await delay(100);
                    selectedSeqOrder = null;
                    return;
                }

                let params = {};
                params.idDigit = $_selectedJob.id_digit;
                params.idScansioni = $_selectedScanArr.map(scan => scan.id);
                params.sequenzialiScansioni = $_selectedScanArr.map(scan => scan.sequenziale_scansione);
                params.direction = 'up';
                await window.electron.database("db-move-seq-scansioni", params);
                
                /* Refresh scan table and select the first moved scan */
                d3.select('#preview-visualizer').dispatch('refresh-scan-table', {detail: {scanIdList: $_selectedScanArr.map(scan => scan.id)}});

                /* remove selected tools */
                await delay(100);
                selectedSeqOrder = null;
            }
        },
        {
            name: 'Muovi giù',
            icon: MoveDownIcon,
            clickHandler: async () => {
                if($_selectedScanArr.length == 0){
                    notify.warning("Nessuna scansione selezionata!");
                    
                    /* remove selected tools */
                    await delay(100);
                    selectedSeqOrder = null;
                    return;
                }

                let params = {};
                params.idDigit = $_selectedJob.id_digit;
                params.idScansioni = $_selectedScanArr.map(scan => scan.id);
                params.sequenzialiScansioni = $_selectedScanArr.map(scan => scan.sequenziale_scansione);
                params.direction = 'down';
                await window.electron.database("db-move-seq-scansioni", params);

                /* Refresh scan table and select the first moved scan */
                d3.select('#preview-visualizer').dispatch('refresh-scan-table', {detail: {scanIdList: $_selectedScanArr.map(scan => scan.id)}});
                
                /* remove selected tools */
                await delay(100);
                selectedSeqOrder = null;
            }
        }
    ]
</script>

<ConfirmationDialog bind:open={openConfirmationDialog} action={confirmationDialogAction} on:answer={(e) => handleAnswer(e)}/>

<div id="preview-visualizer-table-tools" class="flex-column-segmented">
    <div class="flex-row-segmented">
        <!-- Order by -->
        <SegmentedButton segments={orderBySegments} let:segment singleSelect selected={orderBySegments[0]} key={(segment) => segment.name}>
            <Segment {segment} title={segment.name} on:selected={() => segment.clickHandler()}>
                <svelte:component this={segment.icon} width=25 height=25 viewBox='0 0 24 24' color="var(--mdc-theme-secondary)"/>
            </Segment>
        </SegmentedButton>

        <!-- Sequenziali Order -->
        <SegmentedButton segments={seqOrderSegments} let:segment singleSelect bind:selected={selectedSeqOrder} key={(segment) => segment.name}>
            <Segment {segment} title={segment.name} on:selected={() => segment.clickHandler()}>
                <svelte:component this={segment.icon} width=25 height=25 viewBox='0 0 24 24' color="var(--mdc-theme-secondary)"/>
            </Segment>
        </SegmentedButton>
    </div>

    <div class="flex-row-segmented">
        <!-- Tools -->
        <SegmentedButton segments={toolsSegments} let:segment bind:selected={selectedTools} key={(segment) => segment.name}>
            <Segment {segment} title={segment.name} on:selected={() => segment.clickHandler()}>
                <svelte:component this={segment.icon} width=25 height=25 viewBox='0 0 24 24' color="var(--mdc-theme-secondary)"/>
            </Segment>
        </SegmentedButton>
    </div>
</div>

<style>
    .flex-column-segmented {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;
    }

    .flex-row-segmented {
        display: flex;
        gap: 10px;
    }
</style>