<script>
    import { _isCreatingTemplate , _opticalResolution, _iccProfileSourceList, _iccProfileDestinationList, _iccProfileDisplayList, _cropArea, _cropCentralMargin, _respectMargin, _selectedJob, _pressureKg, _enablePlates, _enableBalance, _balanceFriction, _autoCalcThickRatio, _thickWeightRatio, _autoOpenAfterScan, _autoCloseOnScan, _waitForScanPedal, _waitForPressure, _autoOpenClose, _autoOpenMmDown, _lockGlassMove, _openAmountPercentage, _openGlassSpeed, _closeGlassSpeed, _glassAcceleration, _closingGlassPosition, _workWithContact, _contactDistance, _enableFinePressure, _pressureAccuracy, _upSpeed, _downSpeed, _stopScannerPressure, _precision, _flipScan, _outputResolutionPercentage, _umSharpeningEnabled, _umSharpeningIntensity, _umSharpeningType, _umSharpeningRadius, _umSharpeningNoiseLimit, _umSharpeningHVGain, _umSharpeningNoiseRedEnabled, _umSharpeningNoiseRedIntensity, _umSharpeningChDiffEnabled, _umSharpeningChDiffIntensity, _colorMode, _exposureCompensation, _lightLeft, _lightCenterLeft, _lightCenterRight, _lightRight, _iccProfileSource, _iccProfileDestination, _iccProfileDisplay, _opticalResolutionList } from '@store';

    //smui
    import Dialog, { Title, Content } from '@smui/dialog';
    import Button, { Label } from '@smui/button';
    import Textfield from '@smui/textfield';

    /* Template fields */
    let templateName = "", templateDescription = "";

    const handleCreate = async () => {
        let paramsArr = [];
        paramsArr.push(templateName);
        paramsArr.push(templateDescription);
        paramsArr.push($_opticalResolutionList.split(";")[$_opticalResolution]);
        paramsArr.push($_outputResolutionPercentage);
        paramsArr.push($_precision);
        paramsArr.push($_flipScan);
        paramsArr.push($_umSharpeningEnabled);
        paramsArr.push($_umSharpeningType);
        paramsArr.push($_umSharpeningIntensity);
        paramsArr.push($_umSharpeningRadius);
        paramsArr.push($_umSharpeningNoiseLimit);
        paramsArr.push($_umSharpeningHVGain);
        paramsArr.push($_umSharpeningNoiseRedEnabled);
        paramsArr.push($_umSharpeningNoiseRedIntensity);
        paramsArr.push($_umSharpeningChDiffEnabled);
        paramsArr.push($_umSharpeningChDiffIntensity);
        paramsArr.push($_colorMode);
        paramsArr.push($_exposureCompensation);
        paramsArr.push($_lightLeft);
        paramsArr.push($_lightCenterLeft);
        paramsArr.push($_lightCenterRight);
        paramsArr.push($_lightRight);
        paramsArr.push($_iccProfileSourceList.split(";")[$_iccProfileSource]);
        paramsArr.push($_iccProfileDestinationList.split(";")[$_iccProfileDestination]);
        paramsArr.push($_iccProfileDisplayList.split(";")[$_iccProfileDisplay]);
        paramsArr.push($_pressureKg);
        paramsArr.push($_enablePlates);
        paramsArr.push($_enableBalance);
        paramsArr.push($_balanceFriction);
        paramsArr.push($_autoCalcThickRatio);
        paramsArr.push($_thickWeightRatio);
        paramsArr.push($_autoOpenAfterScan);
        paramsArr.push($_autoCloseOnScan);
        paramsArr.push($_waitForScanPedal);
        paramsArr.push($_waitForPressure);
        paramsArr.push($_autoOpenClose);
        paramsArr.push($_autoOpenMmDown);
        paramsArr.push($_lockGlassMove);
        paramsArr.push($_openAmountPercentage);
        paramsArr.push($_openGlassSpeed);
        paramsArr.push($_closeGlassSpeed);
        paramsArr.push($_glassAcceleration);
        paramsArr.push($_closingGlassPosition);
        paramsArr.push($_workWithContact);
        paramsArr.push($_contactDistance);
        paramsArr.push($_enableFinePressure);
        paramsArr.push($_pressureAccuracy);
        paramsArr.push($_upSpeed);
        paramsArr.push($_downSpeed);
        paramsArr.push($_stopScannerPressure);
        paramsArr.push($_selectedJob.scan_path);
        paramsArr.push($_cropArea.rect.x);
        paramsArr.push($_cropArea.rect.y);
        paramsArr.push($_cropArea.rect.width);
        paramsArr.push($_cropArea.rect.height);
        paramsArr.push($_respectMargin);
        paramsArr.push($_cropCentralMargin);

        await window.electron.database('db-save-config-templates', paramsArr);
        await window.electron.database('db-log', {level:'INFO', text:'[CreateTemplateDialog.svelte][handleCreate] Template ' + templateName + ' created successfully'});

        //Set snackbar fields 
        notify.success("Template creato con successo");

        //Clear fields for future uses
        templateName = "";
        templateDescription = "";

        //Close dialog
        document.dispatchEvent(new CustomEvent("template-created"));
        _isCreatingTemplate.set(false);
    }

    const handleClose = () => {
        _isCreatingTemplate.set(false);
    }
</script>

<div>
    <Dialog bind:open={$_isCreatingTemplate} scrimClickAction="">
        <Title>Nuovo Template</Title>
        <Content>
            <div class="content-flex-row">
                <Textfield label="Nome Template" bind:value={templateName}></Textfield>
                <Textfield textarea label="Descrizione" bind:value={templateDescription} style="min-width: 350px; max-width: 500px; min-height: 55px; max-height: 100px;"></Textfield>
            </div>
        </Content>
        <div class="flex-row">
            <Button on:click={handleCreate} variant="raised">
                <Label>Crea</Label>
            </Button>
            <Button on:click={handleClose} variant="raised" color="secondary">
                <Label>Annulla</Label>
            </Button>
        </div>
    </Dialog>
</div>

<style>
    .content-flex-row {
        display: flex;
        justify-content: space-evenly;
        align-items: flex-end;
        margin-top: 25px;
    }

    .flex-row {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 10px;
    }

    * :global(.mdc-dialog .mdc-dialog__surface) {
        min-width: 40%;
        min-height: 30%;
        padding: 10px;
    }

    * :global(.mdc-dialog__content) {
        display: flex;
        flex-direction: column;
    }
</style>