<script>
    import { _mainViewer, _cropAreaPixels, _cropCentralMargin, _respectMargin, _isDoublePageEnabled, _cropArea, _cropLeftAreaPixels, _isCropLocked, _selectedJob, _savedCropAreaPixels, _savedCentralMargin, _savedRespectMargin } from '@store';
    import * as d3 from "d3-selection";
    import { notify } from '@utility';

    //icons
    import ExpandCloseIcon from "svelte-material-icons/UnfoldMoreHorizontal.svelte";

    //smui
    import Textfield from '@smui/textfield';
    import Accordion, { Panel, Header, Content } from '@smui-extra/accordion';
    import IconButton from '@smui/icon-button';
    import Button, { Label } from '@smui/button';
    
    let panel1Open = true;
    let panel2Open = true;
    let panel3Open = true;

    const applyChanges = (typeChanged) => {
        if(isNaN($_cropCentralMargin)){
            notify.warning("Il margine centrale non può essere vuoto");
            return;
        }

        if(isNaN($_respectMargin)){
            notify.warning("Il margine di rispetto non può essere vuoto");
            return;
        }

        if(isNaN($_cropLeftAreaPixels.width) || $_cropLeftAreaPixels.width <= 0){
            notify.warning("La larghezza deve essere maggiore o uguale a zero");
            return;
        } 

        if(isNaN($_cropLeftAreaPixels.height) || $_cropLeftAreaPixels.height <= 0){
            notify.warning("L'altezza deve essere maggiore o uguale a zero");
            return;
        } 

        if($_cropLeftAreaPixels.width <= $_cropCentralMargin){
            notify.warning("La larghezza deve essere maggiore del margine centrale");
            return;
        }

        switch (typeChanged) {
            case 'width':
            case 'height':
                d3.select("#main-viewer").dispatch("dimensions-updated");
                break;
            case 'centralMargin':
                d3.select("#main-viewer").dispatch("refresh-all-elements");
            case 'respectMargin':
                d3.select("#main-viewer").dispatch("refresh-all-elements");
            default:
                break;
        }
    }

    const handleSaveCropDimensions = async () => {
        await window.electron.database('db-log', {level:'INFO', text:'[CropSettings.svelte][handleSaveCropDimensions] Saving crop dimensions'});

        let params = {
            idDigit: $_selectedJob.id_digit,
            cropArea: $_cropArea.rect,
            cropAreaPixels: $_isDoublePageEnabled ? $_cropLeftAreaPixels : $_cropAreaPixels,
            respectMargin: $_respectMargin,
            centralMargin: $_cropCentralMargin
        }

        await window.electron.database('db-save-crop-area-dimensions', params);
        notify.success("Dimensioni dell'area di crop impostate come default");

        /* Update saved variables */
        _savedCropAreaPixels.set(params.cropAreaPixels);
        _savedCentralMargin.set(params.centralMargin);
        _savedRespectMargin.set(params.respectMargin);
    }
</script>

<div class="accordion-container">
    <Accordion multiple>
        <Panel bind:open={panel1Open} disabled={$_isCropLocked}>
        <Header>
            Dimensione
            <span slot="description">Impostazione manuale della dimensione dell'area di crop</span>
            <IconButton slot="icon" toggle pressed={panel1Open}>
                <ExpandCloseIcon width=25 height=25 color="var(--mdc-theme-secondary)"/>
            </IconButton>
        </Header>
        <Content>
            <div class="fields-row">
                {#if $_isDoublePageEnabled}
                    <Textfield 
                        bind:value={$_cropLeftAreaPixels.width}
                        label="Larghezza"
                        type="number"
                        suffix="Pixel"
                        on:change={() => applyChanges('width')}
                        style="max-width: 130px;"
                        input$min={$_cropCentralMargin}
                        input$step="1"
                        input$required
                        disabled={$_isCropLocked}
                    />
                    <Textfield 
                        bind:value={$_cropLeftAreaPixels.height}
                        label="Altezza"
                        type="number"
                        suffix="Pixel"
                        on:change={() => applyChanges('height')}
                        style="max-width: 130px;"
                        input$min={$_cropCentralMargin}
                        input$step="1"
                        input$required
                        disabled={$_isCropLocked}
                    />
                {:else}
                    <Textfield 
                        bind:value={$_cropAreaPixels.width}
                        label="Larghezza"
                        type="number"
                        suffix="Pixel"
                        on:change={() => applyChanges('width')}
                        style="max-width: 130px;"
                        input$min="1"
                        input$step="1"
                        input$required
                        disabled={$_isCropLocked}
                    />
                    <Textfield 
                        bind:value={$_cropAreaPixels.height}
                        label="Altezza"
                        type="number"
                        suffix="Pixel"
                        on:change={() => applyChanges('height')}
                        style="max-width: 130px;"
                        input$min="1"
                        input$step="1"
                        input$required
                        disabled={$_isCropLocked}
                    />
                {/if}
            </div>
        </Content>
        </Panel>
        <Panel bind:open={panel2Open} disabled={$_isCropLocked}>
            <Header>
                Margini
                <span slot="description">Impostazione manuale dei margini dell'area di crop</span>
                <IconButton slot="icon" toggle pressed={panel2Open}>
                    <ExpandCloseIcon width=25 height=25 color="var(--mdc-theme-secondary)"/>
                </IconButton>
            </Header>
            <Content>
                <div class="fields-row">
                    <Textfield 
                        bind:value={$_cropCentralMargin}
                        label="Margine centrale"
                        type="number"
                        input$min="0"
                        input$step="1"
                        input$required
                        suffix="Pixel"
                        on:change={() => applyChanges('centralMargin')}
                        style="max-width: 130px;"
                        disabled={$_isCropLocked}
                    />
                    <Textfield 
                        bind:value={$_respectMargin}
                        label="Margine di rispetto"
                        type="number"
                        input$min="0"
                        input$step="1"
                        input$required
                        suffix="Pixel"
                        on:change={() => applyChanges('respectMargin')}
                        style="max-width: 135px;"
                        disabled={$_isCropLocked}
                    />
                </div>
            </Content>
        </Panel>
        <Panel bind:open={panel3Open}>
            <Header>
                Dimensioni di Default
                <span slot="description">Dimensioni caricate automaticamente all'avvio del job</span>
                <IconButton slot="icon" toggle pressed={panel3Open}>
                    <ExpandCloseIcon width=25 height=25 color="var(--mdc-theme-secondary)"/>
                </IconButton>
            </Header>
            <Content>
                <div class="fields-row">
                    {#if $_savedCropAreaPixels}
                    <span>Le seguenti dimensioni sono salvate come default e verranno caricate in automatico alla prossima apertura del job. Premi il pulsante "Sovrascrivi" per aggiornarle con quelle dell'area di crop attuale.</span>
                        <Textfield 
                            value={$_savedCropAreaPixels.width}
                            label="Larghezza"
                            type="number"
                            suffix="Pixel"
                            input$readonly
                            style="max-width: 130px;"
                        />
                        <Textfield 
                            value={$_savedCropAreaPixels.height}
                            label="Altezza"
                            type="number"
                            suffix="Pixel"
                            input$readonly
                            style="max-width: 130px;"
                        />
                        <Textfield 
                            value={$_savedCentralMargin}
                            label="Margine Centrale"
                            type="number"
                            suffix="Pixel"
                            input$readonly
                            style="max-width: 130px;"
                        />
                        <Textfield 
                            value={$_savedRespectMargin}
                            label="Margine di rispetto"
                            type="number"
                            suffix="Pixel"
                            input$readonly
                            style="max-width: 130px;"
                        />
                        <Button variant="raised" color="secondary" on:click={handleSaveCropDimensions}>
                            <Label>Sovrascrivi</Label>
                        </Button>
                    {:else}
                        <span>Salvataggio ancora non effettuato. Premere il pulsante "Salva come default" per salvare le dimensioni dell'attuale area di crop come default e permetterne il caricamento automatico alla prossima apertura del job. Le dimensioni verranno anche salvate in Inside.</span>
                        <Button variant="raised" color="secondary" on:click={handleSaveCropDimensions}>
                            <Label>Salva come default</Label>
                        </Button>
                    {/if}
                </div>
            </Content>
        </Panel>
    </Accordion>
</div>

<style>
    .fields-row {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 40px;
    }

    span {
        color: var(--mdc-theme-secondary);
    }

    * :global(.smui-accordion__panel) {
        background-color: var(--mdc-theme-background);
    }

    * :global(.smui-accordion__header__title) {
        color: var(--mdc-theme-primary);
    }

    * :global(.smui-accordion__panel) {
        width: 100%;
        padding: 0;
    }
</style>