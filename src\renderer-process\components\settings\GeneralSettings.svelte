<script>
    import { _selectedJob, _isCreatingTemplate, _opticalResolutionList, _iccProfileSourceList, _iccProfileDestinationList, _iccProfileDisplayList, _cropArea, _cropCentralMargin, _respectMargin, _pressureKg, _enablePlates, _enableBalance, _balanceFriction, _autoCalcThickRatio, _thickWeightRatio, _autoOpenAfterScan, _autoCloseOnScan, _waitForScanPedal, _waitForPressure, _autoOpenClose, _autoOpenMmDown, _lockGlassMove, _openAmountPercentage, _openGlassSpeed, _closeGlassSpeed, _glassAcceleration, _closingGlassPosition, _workWithContact, _contactDistance, _enableFinePressure, _pressureAccuracy, _upSpeed, _downSpeed, _stopScannerPressure, _opticalResolution, _precision, _flipScan, _outputResolutionPercentage, _umSharpeningEnabled, _umSharpeningIntensity, _umSharpeningType, _umSharpeningRadius, _umSharpeningNoiseLimit, _umSharpeningHVGain, _umSharpeningNoiseRedEnabled, _umSharpeningNoiseRedIntensity, _umSharpeningChDiffEnabled, _umSharpeningChDiffIntensity, _colorMode, _exposureCompensation, _lightLeft, _lightCenterLeft, _lightCenterRight, _lightRight, _iccProfileSource, _iccProfileDestination, _iccProfileDisplay, _thickness, _lightDistance } from '@store';
    import IconButtonCustom from '@components/common/IconButton.svelte';
    import CreateTemplateDialog from '@components/settings/CreateTemplateDialog.svelte';
    import NoteAcquisizione from '@components/note/NoteAcquisizione.svelte';
    import NoteDigitalizzazione from '@components/note/NoteDigitalizzazione.svelte';
    import NoteValidazione from '@components/note/NoteValidazione.svelte';
    import NotePalinsesto from '@components/note/NotePalinsesto.svelte';
    import { notify } from '@utility';

    //icons
    import LoadTemplateIcon from "svelte-material-icons/FolderDownload.svelte";
    import CreateTemplateIcon from "svelte-material-icons/FolderPlus.svelte";
    import ExpandCloseIcon from "svelte-material-icons/UnfoldMoreHorizontal.svelte";
    
    //smui
    import Textfield from '@smui/textfield';
    import Select, { Option } from '@smui/select';
    import Accordion, { Panel, Header, Content } from '@smui-extra/accordion';
    import IconButton from '@smui/icon-button';
    import Button, { Label } from '@smui/button';

    let panel1Open = true;
    let panel2Open = false;
    let panel3Open = false;
    let panel4Open = false;
    let panel5Open = false;
    let selectedTemplate, templateDescription = '', templateList = [];

    $: if(selectedTemplate) {
        templateDescription = selectedTemplate.template_description
    }

    document.addEventListener("template-created", async function(e) {
        await getAvailableTemplates();
    });

    const getAvailableTemplates = async () => {
        templateList = await window.electron.database('db-get-config-templates');
        return;
    }

    const loadTemplate = async () => {
        /* Load selected template configurations into store variables */
        loadConfigs();

        /* Send commands to set template configuration to the scanner */
        sendCommands();
    }

    const loadConfigs = () => {
        _opticalResolution.set(selectedTemplate.s_optical_resolution ? $_opticalResolutionList.split(";").indexOf(selectedTemplate.s_optical_resolution.toString()) : -1);
        _outputResolutionPercentage.set(selectedTemplate.s_output_resolution_percentage);
        _precision.set(selectedTemplate.s_precision);
        _flipScan.set(selectedTemplate.s_flip_scan);
        _umSharpeningEnabled.set(selectedTemplate.s_um_sharpening_enabled);
        _umSharpeningType.set(selectedTemplate.s_um_sharpening_type);
        _umSharpeningIntensity.set(selectedTemplate.s_um_sharpening_intensity);
        _umSharpeningRadius.set(selectedTemplate.s_um_sharpening_radius);
        _umSharpeningNoiseLimit.set(selectedTemplate.s_um_sharpening_noise_limit);
        _umSharpeningHVGain.set(selectedTemplate.s_um_sharpening_hv_gain);
        _umSharpeningNoiseRedEnabled.set(selectedTemplate.s_um_sharpening_noise_red_enabled);
        _umSharpeningNoiseRedIntensity.set(selectedTemplate.s_um_sharpening_noise_red_intensity);
        _umSharpeningChDiffEnabled.set(selectedTemplate.s_um_sharpening_ch_diff_enabled);
        _umSharpeningChDiffIntensity.set(selectedTemplate.s_um_sharpening_ch_diff_intensity);
        _colorMode.set(selectedTemplate.s_color_mode);
        _exposureCompensation.set(selectedTemplate.s_exposure_compensation);
        _lightLeft.set(selectedTemplate.s_light_left);
        _lightCenterLeft.set(selectedTemplate.s_light_center_left);
        _lightCenterRight.set(selectedTemplate.s_light_center_right);
        _lightRight.set(selectedTemplate.s_light_right);
        _iccProfileSource.set(selectedTemplate.s_icc_profile_source ? $_iccProfileSourceList.split(";").indexOf(selectedTemplate.s_icc_profile_source.toString()) : -1);
        _iccProfileDestination.set(selectedTemplate.s_icc_profile_destination ? $_iccProfileDestinationList.split(";").indexOf(selectedTemplate.s_icc_profile_destination.toString()) : -1);
        _iccProfileDisplay.set(selectedTemplate.s_icc_profile_display ? $_iccProfileDisplayList.split(";").indexOf(selectedTemplate.s_icc_profile_display.toString()) : -1);
        _pressureKg.set(selectedTemplate.s_pressure_kg);
        _enablePlates.set(selectedTemplate.s_enable_plates);
        _enableBalance.set(selectedTemplate.s_enable_balance);
        _balanceFriction.set(selectedTemplate.s_balance_friction);
        _autoCalcThickRatio.set(selectedTemplate.s_auto_calc_thick_ratio);
        _thickWeightRatio.set(selectedTemplate.s_thick_weight_ratio);
        _autoOpenAfterScan.set(selectedTemplate.s_auto_open_after_scan);
        _autoCloseOnScan.set(selectedTemplate.s_auto_close_on_scan);
        _waitForScanPedal.set(selectedTemplate.s_wait_for_scan_pedal);
        _waitForPressure.set(selectedTemplate.s_wait_for_pressure);
        _autoOpenClose.set(selectedTemplate.s_auto_open_close);
        _autoOpenMmDown.set(selectedTemplate.s_auto_open_mm_down);
        _lockGlassMove.set(selectedTemplate.s_lock_glass_move);
        _openAmountPercentage.set(selectedTemplate.s_open_amount_percentage);
        _openGlassSpeed.set(selectedTemplate.s_open_glass_speed);
        _closeGlassSpeed.set(selectedTemplate.s_close_glass_speed);
        _glassAcceleration.set(selectedTemplate.s_glass_acceleration);
        _closingGlassPosition.set(selectedTemplate.s_closing_glass_position);
        _workWithContact.set(selectedTemplate.s_work_with_contact);
        _contactDistance.set(selectedTemplate.s_contact_distance);
        _enableFinePressure.set(selectedTemplate.s_enable_fine_pressure);
        _pressureAccuracy.set(selectedTemplate.s_pressure_accuracy);
        _upSpeed.set(selectedTemplate.s_up_speed);
        _downSpeed.set(selectedTemplate.s_down_speed);
        _stopScannerPressure.set(selectedTemplate.s_stop_scanner_pressure);
        _selectedJob.scan_path.set(selectedTemplate.s_scan_path);
        _respectMargin.set(selectedTemplate.a_respect_margin);
        _cropCentralMargin.set(selectedTemplate.a_crop_central_margin);
        $_cropArea.rect.x = selectedTemplate.a_crop_area_x;
        $_cropArea.rect.y = selectedTemplate.a_crop_area_y;
        $_cropArea.rect.width = selectedTemplate.a_crop_area_width;
        $_cropArea.rect.height = selectedTemplate.a_crop_area_height;
    }

    const sendCommands = async () => {
        /* Defining commands to send */
		let commandList = [
			{name: 'msd-set-book-cradle-parameters', params: {}, loadingText: 'Setto impostazioni dei pianetti...'},
			{name: 'msd-set-book-cradle-contact-parameters', params: {}, loadingText: 'Setto impostazioni di contatto dei pianetti...'},
			{name: 'msd-set-book-cradle-parameters', params: {}, loadingText: 'Setto impostazioni dei pianetti...'},
			{name: 'msd-set-optical-resolution', params: {}, loadingText: 'Setto la risoluzione ottica...'},
			{name: 'msd-set-output-resolution-percentage', params: {}, loadingText: 'Setto la risoluzione di output in percentuale...'},
			{name: 'msd-set-precision', params: {}, loadingText: 'Setto la precisione (8/16 Bit)...'},
			{name: 'msd-set-flip-scan', params: {}, loadingText: 'Setto la rotazione...'},
			{name: 'msd-set-um-sharpening', params: {}, loadingText: 'Setto impostazioni della maschera di contrasto...'},
			{name: 'msd-set-color-mode', params: {}, loadingText: 'Setto la modalità colore (RGB/Greyscale)...'},
			{name: 'msd-set-exposure-compensation', params: {}, loadingText: 'Setto l\'esposizione...'},
			{name: 'msd-set-light-schematic', params: {}, loadingText: 'Setto la schematica di luce...'},
		];

        /* Setting params for each command */
		setCommandParams(commandList);
        
        /* Send commands */
		d3.select('#command-sender').dispatch('send-commands', {detail: {commandList: commandList}});
    }

    const setCommandParams = (commandList) => {
		for (let command of commandList) {
			switch (command.name) {
				case 'msd-set-book-cradle-parameters':
					command.params.pressureKg = $_pressureKg;
					command.params.enablePlates = $_enablePlates;
					command.params.enableBalance = $_enableBalance;
					command.params.balanceFriction = $_balanceFriction;
					command.params.autoCalcThickRatio = $_autoCalcThickRatio;
					command.params.thickWeightRatio = $_thickWeightRatio;
					command.params.autoOpenAfterScan = $_autoOpenAfterScan;
					command.params.autoCloseOnScan = $_autoCloseOnScan;
					command.params.waitForScanPedal = $_waitForScanPedal;
					command.params.waitForPressure = $_waitForPressure;
					command.params.autoOpenClose = $_autoOpenClose;
					command.params.autoOpenMmDown = $_autoOpenMmDown;
					command.params.lockGlassMove = $_lockGlassMove;
					command.params.openAmountPercentage = $_openAmountPercentage;
					command.params.openGlassSpeed = $_openGlassSpeed;
					command.params.closeGlassSpeed = $_closeGlassSpeed;
					command.params.glassAcceleration = $_glassAcceleration;
					command.params.closingGlassPosition = $_closingGlassPosition;
					command.params.workWithContact = $_workWithContact;
					command.params.contactDistance = $_contactDistance;
					command.params.enableFinePressure = $_enableFinePressure;
					command.params.pressureAccuracy = $_pressureAccuracy;
					command.params.upSpeed = $_upSpeed;
					command.params.downSpeed = $_downSpeed;
					command.params.stopScannerPressure = $_stopScannerPressure;
					break;
				case 'msd-set-book-cradle-contact-parameters':
					command.params.thickness = $_thickness;
					command.params.lightDistance = $_lightDistance;
					break;
				case 'msd-set-optical-resolution':
					command.params.resolutionIndex = $_opticalResolution || 0;
					break;
				case 'msd-set-output-resolution-percentage':
					command.params.resolutionPercentage = $_outputResolutionPercentage;
					break;
				case 'msd-set-precision':
					command.params.precision = $_precision;
					break;
				case 'msd-set-flip-scan':
					command.params.rotation = $_flipScan;
					break;
				case 'msd-set-um-sharpening':
					command.params.isEnabled = $_umSharpeningEnabled;
					command.params.isColorRGB = $_umSharpeningType;
					command.params.intensity = $_umSharpeningIntensity;
					command.params.radius = $_umSharpeningRadius;
					command.params.noiseLimit = $_umSharpeningNoiseLimit;
					command.params.HvsVGain = $_umSharpeningHVGain;
					command.params.isNoiseReduct = $_umSharpeningNoiseRedEnabled;
					command.params.NRIntensity = $_umSharpeningNoiseRedIntensity;
					command.params.isReduceChDiff = $_umSharpeningChDiffEnabled;
					command.params.CDIntensity = $_umSharpeningChDiffIntensity;						
					break;
				case 'msd-set-color-mode':
					command.params.colorMode = $_colorMode;
					break;
				case 'msd-set-exposure-compensation':
					command.params.exposureCompensation = $_exposureCompensation;
					break;
				case 'msd-set-light-schematic':
					command.params.light1 = $_lightLeft;
					command.params.light2 = $_lightCenterLeft;
					command.params.light3 = $_lightCenterRight;
					command.params.light4 = $_lightRight;
					break;
				default:
					break;
			}
		}
	}

    const createTemplate = () => {
        _isCreatingTemplate.set(true);
    }

    const handleSaveNotes = async () => {
        let params = {
            idDigit: $_selectedJob.id_digit
        };

        await window.electron.database('db-log', {level:'DEBUG', text:'[GeneralSettings.svelte][handleSaveNotes] Updating note acquisizione'});
        params.column = 'note_acquisizione';
        params.value = $_selectedJob?.note_acquisizione || null;

        const response = await window.electron.database('db-update-note-job', params);

        if(response.affectedRows > 0){
            notify.success("Note aggiornate correttamente");
        } else {
            notify.error("Errore durante l'aggiornamente delle note");
        }
    }
</script>

<CreateTemplateDialog />

<div class="accordion-container">
    <Accordion multiple>
        <Panel bind:open={panel1Open} >
        <Header>
            Template di Impostazioni
            <span slot="description">Caricamento e creazione di template di impostazioni</span>
            <IconButton slot="icon" toggle pressed={panel1Open}>
                <ExpandCloseIcon width=25 height=25 color="var(--mdc-theme-secondary)"/>
            </IconButton>
        </Header>
        <Content>
            {#await getAvailableTemplates() then }
                <div class="fields-row">
                    <div class="flex-inner-column">
                        <p class="label select">Template</p>
                        <Select bind:value={selectedTemplate} style="width: 180px;">
                            {#each templateList as option}
                                <Option value={option}>{option.template_name}</Option>
                            {/each}
                        </Select>
                    </div>
                    <Textfield textarea bind:value={templateDescription} label="Descrizione" style="min-width: 350px; max-width: 500px; min-height: 55px; max-height: 100px;"></Textfield>
                    <IconButtonCustom icon={LoadTemplateIcon} iconColor="var(--mdc-theme-secondary)" label="Carica" tooltip="Carica template" onclick={loadTemplate} disabled={!selectedTemplate}/>
                    <IconButtonCustom icon={CreateTemplateIcon} iconColor="var(--mdc-theme-success)" label="Crea template" tooltip="Salva impostazioni attuali come un nuovo template" onclick={createTemplate}/>
                </div>
            {/await}
        </Content>
        </Panel>
        <Panel bind:open={panel2Open} >
            <Header>
                Note Acquisizione
                <span slot="description">Visualizza le note di acquisizione</span>
                <IconButton slot="icon" toggle pressed={panel2Open}>
                    <ExpandCloseIcon width=25 height=25 color="var(--mdc-theme-secondary)"/>
                </IconButton>
            </Header>
            <Content>
                <NoteAcquisizione isFromSettings/>
                <div class="buttons-flex-row">
                    <Button on:click={handleSaveNotes} variant="raised">
                        <Label>Salva</Label>
                    </Button>
                </div>
            </Content>
        </Panel>
        <Panel bind:open={panel3Open} >
            <Header>
                Note Digitalizzazione
                <span slot="description">Visualizza le note del fotografico</span>
                <IconButton slot="icon" toggle pressed={panel3Open}>
                    <ExpandCloseIcon width=25 height=25 color="var(--mdc-theme-secondary)"/>
                </IconButton>
            </Header>
            <Content>
                <NoteDigitalizzazione isFromSettings/>
            </Content>
        </Panel>
        <Panel bind:open={panel4Open} >
            <Header>
                Note Validazione
                <span slot="description">Visualizza le note di validazione (warning)</span>
                <IconButton slot="icon" toggle pressed={panel4Open}>
                    <ExpandCloseIcon width=25 height=25 color="var(--mdc-theme-secondary)"/>
                </IconButton>
            </Header>
            <Content>
                <NoteValidazione isFromSettings/>
            </Content>
        </Panel>
        {#if $_selectedJob.is_palinsesto}
        <Panel bind:open={panel5Open} >
            <Header>
                Note Palinsesto
                <span slot="description">Visualizza le note relative al palinsesto</span>
                <IconButton slot="icon" toggle pressed={panel5Open}>
                    <ExpandCloseIcon width=25 height=25 color="var(--mdc-theme-secondary)"/>
                </IconButton>
            </Header>
            <Content>
                <NotePalinsesto isFromSettings/>
            </Content>
        </Panel>
        {/if}
    </Accordion>
</div>

<style>
    .fields-row {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 40px;
    }

    .flex-inner-column {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: 10px;
        margin-bottom: 10px;
    }

    .buttons-flex-row {
        display: flex;
        justify-content: flex-end;
        width: 100%;
    }

    .accordion-container {
        overflow-y: auto;
    }

    .accordion-container::-webkit-scrollbar {
        width: 15px;
    }

    .accordion-container::-webkit-scrollbar-track {
        background-color: white;
        border-radius: 10px;
    }

    .accordion-container::-webkit-scrollbar-thumb {
        background-color: var(--mdc-theme-primary);
        border-radius: 10px;
    }

    span {
        color: var(--mdc-theme-secondary);
    }

    p {
        margin: 0px;
    }

    .label{
        margin: 0px;
        transform: scale(0.95);
        color: var(--mdc-theme-secondary);
    }

    .label.select{
        align-self: flex-start;
    }

    * :global(.smui-accordion__panel) {
        background-color: var(--mdc-theme-background);
    }

    * :global(.smui-accordion__header__title) {
        color: var(--mdc-theme-primary);
    }

    * :global(.smui-accordion__panel) {
        width: 100%;
        padding: 0;
    }
</style>