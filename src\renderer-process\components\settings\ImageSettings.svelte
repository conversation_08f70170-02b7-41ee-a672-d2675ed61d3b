<script>
    import { _opticalResolutionList, _opticalResolution, _outputResolutionPercentage, _precision, _flipScan, _umSharpeningEnabled, _umSharpeningType, _umSharpeningIntensity, _umSharpeningRadius, _umSharpeningNoiseLimit, _umSharpeningHVGain, _umSharpeningNoiseRedEnabled, _umSharpeningNoiseRedIntensity, _umSharpeningChDiffEnabled, _umSharpeningChDiffIntensity, _colorMode, _exposureCompensation, _lightLeft, _lightCenterLeft, _lightCenterRight, _lightRight, _iccProfileSourceList, _iccProfileDestinationList, _iccProfileDisplayList, _iccProfileSource, _iccProfileDestination, _iccProfileDisplay, _user } from '@store';

    //smui
    import Select, { Option } from '@smui/select';
    import Textfield from '@smui/textfield';
    import Checkbox from '@smui/checkbox';
    import Accordion, { Panel, Header, Content } from '@smui-extra/accordion';
    import IconButton from '@smui/icon-button';

    //icons
    import ExpandCloseIcon from "svelte-material-icons/UnfoldMoreHorizontal.svelte";

    export let commandSet;
    export let commandsToApply;
    let panel1Open = true;
    let panel2Open = false;
    let panel3Open = false;
    let panel4Open = false;
    let panel5Open = false;
    let panel6Open = false;

    let opticalResolutionList = $_opticalResolutionList.split(";");

    const addCommand = (command) => {
        commandSet.add(command);
        commandsToApply = true;
    }  
</script>

<div class="accordion-container">
    <Accordion multiple>
        <Panel bind:open={panel1Open} >
            <Header>
                Immagine
                <IconButton slot="icon" toggle pressed={panel1Open}>
                    <ExpandCloseIcon width=25 height=25 color="var(--mdc-theme-secondary)"/>
                </IconButton>
            </Header>
            <Content>
                <div class="fields-row">
                    <div class="flex-inner-column">
                        <p class="label select">Risoluzione Ottica</p>
                        <Select bind:value={$_opticalResolution} style="width: 160px;">
                            {#each opticalResolutionList as option, index}
                                <Option value={index} on:click={index != $_opticalResolution ? () => addCommand("msd-set-optical-resolution") : null}>{option}</Option>
                            {/each}
                        </Select>
                    </div>
                    <div class="flex-inner-column">
                        <p class="label select">Rotazione</p>
                        <Select bind:value={$_flipScan} style="width: 120px;">
                            <Option value={0} on:click={0 != $_flipScan ? () => addCommand("msd-set-flip-scan") : null}>0°</Option>
                            <Option value={1} on:click={1 != $_flipScan ? () => addCommand("msd-set-flip-scan") : null}>90°</Option>
                            <Option value={2} on:click={2 != $_flipScan ? () => addCommand("msd-set-flip-scan") : null}>180°</Option>
                            <Option value={3} on:click={3 != $_flipScan ? () => addCommand("msd-set-flip-scan") : null}>270°</Option>
                        </Select>
                    </div>
                    <Textfield
                        style="width: 160px;"
                        bind:value={$_outputResolutionPercentage}
                        label="Risoluzione Output %"
                        type="number"
                        suffix="(0.0-100.0 %)"
                        input$min="0.0"
                        input$max="100.0"
                        input$step="0.1"
                        on:change={() => addCommand("msd-set-output-resolution-percentage")}
                    />
                    <div class="flex-inner-column">
                        <p class="label select">Precisione</p>
                        <Select bind:value={$_precision} style="width: 120px;">
                            <Option value={0} on:click={0 != $_precision ? () => addCommand("msd-set-precision") : null}>8 bit</Option>
                            <Option value={1} on:click={1 != $_precision ? () => addCommand("msd-set-precision") : null}>16 bit</Option>
                        </Select>
                    </div>
                </div>
            </Content>
        </Panel>
        <Panel bind:open={panel2Open} disabled={$_user?.profile == 'Operatore'}>
            <Header>
                Sharpening
                <span slot="description">Impostazioni avanzate per tutor e supertutor</span>
                <IconButton slot="icon" toggle pressed={panel2Open}>
                    <ExpandCloseIcon width=25 height=25 color="var(--mdc-theme-secondary)"/>
                </IconButton>
            </Header>
            <Content>
                <div class="fields-row">
                    <div class="flex-inner-column">
                        <p class="label">Abilitato</p>
                        <Checkbox bind:checked={$_umSharpeningEnabled} on:change={() => addCommand("msd-set-um-sharpening")}/>
                    </div>
                    <div class="flex-inner-column">
                        <p class="label select">Tipo</p>
                        <Select bind:value={$_umSharpeningType} style="width: 100px;">
                            <Option value={0} on:click={0 != $_umSharpeningType ? () => addCommand("msd-set-um-sharpening") : null}>RGB</Option>
                            <Option value={1} on:click={1 != $_umSharpeningType ? () => addCommand("msd-set-um-sharpening") : null}>LAB</Option>
                        </Select>
                    </div>
                    <Textfield
                        style="width: 100px;"
                        bind:value={$_umSharpeningIntensity}
                        label="Intensità"
                        type="number"
                        suffix="(-20-20)"
                        input$min="-20.0"
                        input$max="20.0"
                        input$step="0.1"
                        on:change={() => addCommand("msd-set-um-sharpening")}
                    />
                    <Textfield
                        style="width: 100px;"
                        bind:value={$_umSharpeningRadius}
                        label="Raggio"
                        type="number"
                        suffix="(0.1-5.0)"
                        input$min="0.1"
                        input$max="5.0"
                        input$step="0.1"
                        on:change={() => addCommand("msd-set-um-sharpening")}
                    />
                    <Textfield
                        style="width: 100px;"
                        bind:value={$_umSharpeningNoiseLimit}
                        label="Rumore"
                        type="number"
                        suffix="(0-100)"
                        input$min="0"
                        input$max="100"
                        input$step="1"
                        on:change={() => addCommand("msd-set-um-sharpening")}
                    />
                    <Textfield
                        style="width: 160px;"
                        bind:value={$_umSharpeningHVGain}
                        label="Guadagno H vs V"
                        type="number"
                        suffix="(-100-100)"
                        input$min="-100"
                        input$max="100"
                        input$step="1"
                        on:change={() => addCommand("msd-set-um-sharpening")}
                    />
                </div>
            </Content>
        </Panel>
        <Panel bind:open={panel3Open} disabled={$_user?.profile == 'Operatore'}>
            <Header>
                Riduzione Rumore
                <span slot="description">Impostazioni avanzate per tutor e supertutor</span>
                <IconButton slot="icon" toggle pressed={panel3Open}>
                    <ExpandCloseIcon width=25 height=25 color="var(--mdc-theme-secondary)"/>
                </IconButton>
            </Header>
            <Content>
                <div class="fields-row">
                    <div class="flex-inner-column">
                        <p class="label">Abilitato</p>
                        <Checkbox bind:checked={$_umSharpeningNoiseRedEnabled} on:change={() => addCommand("msd-set-um-sharpening")}/>
                    </div>
                    <Textfield
                        style="width: 100px;"
                        bind:value={$_umSharpeningNoiseRedIntensity}
                        label="Intensità"
                        type="number"
                        suffix="(0-100)"
                        input$min="0"
                        input$max="100"
                        input$step="1"
                        on:change={() => addCommand("msd-set-um-sharpening")}
                    />
                </div>
            </Content>
        </Panel>
        <Panel bind:open={panel4Open} disabled={$_user?.profile == 'Operatore'}>
            <Header>
                Riduzione Differenza Canale
                <span slot="description">Impostazioni avanzate per tutor e supertutor</span>
                <IconButton slot="icon" toggle pressed={panel4Open}>
                    <ExpandCloseIcon width=25 height=25 color="var(--mdc-theme-secondary)"/>
                </IconButton>
            </Header>
            <Content>
                <div class="fields-row">
                    <div class="flex-inner-column">
                        <p class="label">Abilitato</p>
                        <Checkbox bind:checked={$_umSharpeningChDiffEnabled} on:change={() => addCommand("msd-set-um-sharpening")}/>
                    </div>
                    <Textfield
                        style="width: 100px;"
                        bind:value={$_umSharpeningChDiffIntensity}
                        label="Intensità"
                        type="number"
                        suffix="(0-100)"
                        input$min="0"
                        input$max="100"
                        input$step="1"
                        on:change={() => addCommand("msd-set-um-sharpening")}
                    />
                </div>
            </Content>
        </Panel>
        <Panel bind:open={panel5Open} disabled={$_user?.profile == 'Operatore'}>
            <Header>
                Colore
                <span slot="description">Impostazioni avanzate per tutor e supertutor</span>
                <IconButton slot="icon" toggle pressed={panel5Open}>
                    <ExpandCloseIcon width=25 height=25 color="var(--mdc-theme-secondary)"/>
                </IconButton>
            </Header>
            <Content>
                <div class="fields-row">
                    <div class="flex-inner-column">
                        <p class="label select">Modalità</p>
                        <Select bind:value={$_colorMode} style="width: 100px;">
                            <Option value={0} on:click={0 != $_colorMode ? () => addCommand("msd-set-color-mode") : null}>RGB</Option>
                            <Option value={1} on:click={1 != $_colorMode ? () => addCommand("msd-set-color-mode") : null}>Greyscale</Option>
                        </Select>
                    </div>
                    <Textfield
                        style="width: 100px;"
                        bind:value={$_exposureCompensation}
                        label="Esposizione"
                        type="number"
                        suffix="(-100-100)"
                        input$min="-100"
                        input$max="100"
                        input$step="1"
                        on:change={() => addCommand("msd-set-exposure-compensation")}
                    />
                </div>
            </Content>
        </Panel>
        <Panel bind:open={panel6Open} disabled={$_user?.profile == 'Operatore'}>
            <Header>
                Schematica di Luce
                <span slot="description">Impostazioni avanzate per tutor e supertutor</span>
                <IconButton slot="icon" toggle pressed={panel6Open}>
                    <ExpandCloseIcon width=25 height=25 color="var(--mdc-theme-secondary)"/>
                </IconButton>
            </Header>
            <Content>
                <div class="fields-row">
                    <Textfield
                    style="width: 110px;"
                    bind:value={$_lightLeft}
                    label="Sinistra"
                    type="number"
                    suffix="(0-100)"
                    input$min="0"
                    input$max="100"
                    input$step="1"
                    on:change={() => addCommand("msd-set-light-schematic")}
                    />
                    <Textfield
                        style="width: 110px;"
                        bind:value={$_lightCenterLeft}
                        label="Centro-Sinistra"
                        type="number"
                        suffix="(0-100)"
                        input$min="0"
                        input$max="100"
                        input$step="1"
                        on:change={() => addCommand("msd-set-light-schematic")}
                    />
                    <Textfield
                        style="width: 110px;"
                        bind:value={$_lightCenterRight}
                        label="Centro-Destra"
                        type="number"
                        suffix="(0-100)"
                        input$min="0"
                        input$max="100"
                        input$step="1"
                        on:change={() => addCommand("msd-set-light-schematic")}
                    />
                    <Textfield
                        style="width: 110px;"
                        bind:value={$_lightRight}
                        label="Destra"
                        type="number"
                        suffix="(0-100)"
                        input$min="0"
                        input$max="100"
                        input$step="1"
                        on:change={() => addCommand("msd-set-light-schematic")}
                    />
                </div>
            </Content>
        </Panel>
    </Accordion>
</div>

<style>
    .accordion-container {
        overflow-y: auto;
    }

    .fields-row {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 40px;
    }

    .flex-inner-column {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: 10px;
        margin-bottom: 10px;
    }

    span {
        color: var(--mdc-theme-secondary);
    }

    p {
        margin: 0px;
    }

    .label{
        margin: 0px;
        transform: scale(0.95);
        color: var(--mdc-theme-secondary);
    }

    .label.select{
        align-self: flex-start;
    }

    * :global(.smui-accordion__panel) {
        background-color: var(--mdc-theme-background);
    }

    * :global(.smui-accordion__header__title) {
        color: var(--mdc-theme-primary);
    }

    * :global(.smui-accordion__panel) {
        width: 100%;
        padding: 0;
    }

    .accordion-container::-webkit-scrollbar {
        width: 15px;
    }

    .accordion-container::-webkit-scrollbar-track {
        background-color: white;
        border-radius: 10px;
    }

    .accordion-container::-webkit-scrollbar-thumb {
        background-color: var(--mdc-theme-primary);
        border-radius: 10px;
    }
</style>