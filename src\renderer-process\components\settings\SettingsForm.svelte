<script>
    import { _pressureKg, _enablePlates, _enableBalance, _balanceFriction, _autoCalcThickRatio, _thickWeightRatio, _autoOpenAfterScan, _autoCloseOnScan, _waitForScanPedal, _waitForPressure, _autoOpenClose, _autoOpenMmDown, _lockGlassMove, _openAmountPercentage, _openGlassSpeed, _closeGlassSpeed, _glassAcceleration, _closingGlassPosition, _workWithContact, _contactDistance, _enableFinePressure, _pressureAccuracy, _upSpeed, _downSpeed, _stopScannerPressure, _opticalResolution, _precision, _flipScan, _outputResolutionPercentage, _umSharpeningEnabled, _umSharpeningIntensity, _umSharpeningType, _umSharpeningRadius, _umSharpeningNoiseLimit, _umSharpeningHVGain, _umSharpeningNoiseRedEnabled, _umSharpeningNoiseRedIntensity, _umSharpeningChDiffEnabled, _umSharpeningChDiffIntensity, _colorMode, _exposureCompensation, _lightLeft, _lightCenterLeft, _lightCenterRight, _lightRight, _iccProfileSource, _iccProfileDestination, _iccProfileDisplay, _opticalResolutionList, _iccProfileSourceList, _iccProfileDestinationList, _iccProfileDisplayList, _cropArea, _respectMargin, _cropCentralMargin, _isSettingsFormOpen, _mainViewer, _thickness, _lightDistance, _selectedScan } from '@store';
    import SettingsTabs from '@components/settings/SettingsTabs.svelte';
    import * as d3 from "d3-selection";

    //smui components
    import Paper, { Title, Content } from '@smui/paper';
    import Button, { Label } from '@smui/button';

    let commandSet = new Set();
    let commandsToApply = false;

    const sendCommands = async () => {
        let commandList = [];

        for (let command of commandSet) {
            let params = {};
            let loadingText = '';

            switch (command) {
                case 'msd-set-book-cradle-parameters':
                    params.pressureKg = $_pressureKg;
                    params.enablePlates = $_enablePlates;
                    params.enableBalance = $_enableBalance;
                    params.balanceFriction = $_balanceFriction;
                    params.autoCalcThickRatio = $_autoCalcThickRatio;
                    params.thickWeightRatio = $_thickWeightRatio;
                    params.autoOpenAfterScan = $_autoOpenAfterScan;
                    params.autoCloseOnScan = $_autoCloseOnScan;
                    params.waitForScanPedal = $_waitForScanPedal;
                    params.waitForPressure = $_waitForPressure;
                    params.autoOpenClose = $_autoOpenClose;
                    params.autoOpenMmDown = $_autoOpenMmDown;
                    params.lockGlassMove = $_lockGlassMove;
                    params.openAmountPercentage = $_openAmountPercentage;
                    params.openGlassSpeed = $_openGlassSpeed;
                    params.closeGlassSpeed = $_closeGlassSpeed;
                    params.glassAcceleration = $_glassAcceleration;
                    params.closingGlassPosition = $_closingGlassPosition;
                    params.workWithContact = $_workWithContact;
                    params.contactDistance = $_contactDistance;
                    params.enableFinePressure = $_enableFinePressure;
                    params.pressureAccuracy = $_pressureAccuracy;
                    params.upSpeed = $_upSpeed;
                    params.downSpeed = $_downSpeed;
                    params.stopScannerPressure = $_stopScannerPressure;
                    loadingText = 'Setto impostazioni dei pianetti...';
                    break;
                case 'msd-set-book-cradle-contact-parameters':
                    params.thickness = $_thickness;
                    params.lightDistance = $_lightDistance;
                    loadingText = 'Setto impostazioni di contatto dei pianetti...'
                    break;
                case 'msd-set-optical-resolution':
                    params.resolutionIndex = $_opticalResolution;
                    loadingText = 'Setto la risoluzione ottica...';
                    break;
                case 'msd-set-output-resolution-percentage':
                    params.resolutionPercentage = $_outputResolutionPercentage;
                    loadingText = 'Setto la risoluzione di output in percentuale...';
                    break;
                case 'msd-set-precision':
                    params.precision = $_precision;
                    loadingText = 'Setto la precisione (8/16 Bit)...';
                    break;
                case 'msd-set-flip-scan':
                    params.rotation = $_flipScan;
                    loadingText = 'Setto la rotazione...';
                    break;
                case 'msd-set-um-sharpening':
                    params.isEnabled = $_umSharpeningEnabled;
                    params.isColorRGB = $_umSharpeningType;
                    params.intensity = $_umSharpeningIntensity;
                    params.radius = $_umSharpeningRadius;
                    params.noiseLimit = $_umSharpeningNoiseLimit;
                    params.HvsVGain = $_umSharpeningHVGain;
                    params.isNoiseReduct = $_umSharpeningNoiseRedEnabled;
                    params.NRIntensity = $_umSharpeningNoiseRedIntensity;
                    params.isReduceChDiff = $_umSharpeningChDiffEnabled;
                    params.CDIntensity = $_umSharpeningChDiffIntensity;
                    loadingText = 'Setto impostazioni della maschera di contrasto...';
                    break;
                case 'msd-set-color-mode':
                    params.colorMode = $_colorMode;
                    loadingText = 'Setto la modalità colore (RGB/Greyscale)...';
                    break;
                case 'msd-set-exposure-compensation':
                    params.exposureCompensation = $_exposureCompensation;
                    loadingText = 'Setto l\'esposizione...';
                    break;
                case 'msd-set-light-schematic':
                    params.light1 = $_lightLeft;
                    params.light2 = $_lightCenterLeft;
                    params.light3 = $_lightCenterRight;
                    params.light4 = $_lightRight;
                    loadingText = 'Setto la schematica di luce...';
                    break;
                default:
                    break;
            }
            
            commandList.push({name: command, params: params, loadingText: loadingText});
        }
        
        /* Send commands */
		d3.select('#command-sender').dispatch('send-commands', {detail: {commandList: commandList}});

        /* Clear */
        commandSet.clear();
        commandsToApply = false;
    }

    const handleClose = () => {
        _isSettingsFormOpen.set(false);

        /* Update scan images */
        d3.select('#scan-viewers').dispatch('update-scan-images', {detail: { scan: $_selectedScan}});
    }
</script>

<div class="container">
    <Paper elevation=5>
        <Title>Impostazioni</Title>
        <Content>
            <SettingsTabs bind:commandSet={commandSet} bind:commandsToApply={commandsToApply}/>
            <div class="buttons-flex-row">
                
                <Button on:click={sendCommands} variant="raised" disabled={!commandsToApply}>
                    <Label>Applica</Label>
                </Button>
                <Button on:click={handleClose} variant="raised" color="secondary" disabled={commandsToApply}>
                    <Label>Chiudi</Label>
                </Button>
            </div>
        </Content>
    </Paper>
</div>


<style>
    .container {
        display: flex;
        flex-grow: 1;
        height: 100%;
        width: 50%;
        justify-content: center;
        align-items: center;
    }

    .buttons-flex-row {
        display: flex;
        justify-content: flex-end;
        gap: 20px;
        width: 100%;
    }

    * :global(.smui-paper) {
        width: calc(95% - 32px);
        height: calc(95% - 32px);
        padding: 16px;
    }

    * :global(.smui-paper__content) {
        height: calc(100% - 32px);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        gap: 10px;
    }

    * :global(.smui-paper__title) {
        margin: 0;
    }
</style>