<script>
    import GeneralSettings from '@components/settings/GeneralSettings.svelte';
    import CropSettings from '@components/settings/CropSettings.svelte';
    import CradleSettings from '@components/settings/CradleSettings.svelte';
    import ImageSettings from '@components/settings/ImageSettings.svelte';

    //smui 
    import Tab, { Label } from '@smui/tab';
    import TabBar from '@smui/tab-bar';
   
    export let commandSet;
    export let commandsToApply;
    let tabs = ['Generali', 'Crop', 'Pianetti', 'Immagine'];
    let activeTab = 'Generali';
</script>

<div class="tab-container">
    <TabBar tabs={tabs} let:tab bind:active={activeTab}>
        <Tab {tab}>
        <Label>{tab}</Label>
        </Tab>
    </TabBar>

    {#if activeTab == 'Generali'}
        <GeneralSettings />
    {:else if activeTab == 'Crop'}
        <CropSettings />
    {:else if activeTab == 'Pianetti'}
        <CradleSettings bind:commandSet={commandSet} bind:commandsToApply={commandsToApply}/>
    {:else if activeTab == 'Immagine'}
        <ImageSettings bind:commandSet={commandSet} bind:commandsToApply={commandsToApply}/>
    {/if}
</div>

<style>
    .tab-container {
        display: flex;
        flex-direction: column;
        gap: 20px;
        overflow: hidden;
    }

    * :global(.mdc-tab-bar) {
        overflow: hidden;
        flex-shrink: 0;
    }
</style>