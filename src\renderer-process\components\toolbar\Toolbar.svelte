<script>
    import { _cropAreaPixels } from '@store';
    import Tools from '@components/toolbar/Tools.svelte';
    import Nomenclature from '@components/toolbar/nomenclature/Nomenclature.svelte';
    import ScanVisualizerTable from '@components/toolbar/scan-visualizer/ScanVisualizerTable.svelte';
    

	//smui components
    import Paper, { Content } from '@smui/paper';
</script>

<div class="toolbar-container">
    <Paper elevation=5>
        <Content>
            <div class="flex-container">
                <Tools />
                <div class="flex-row">
                    <Nomenclature />
                    <ScanVisualizerTable />
                </div>
            </div>
        </Content>
    </Paper>
</div>

<style>
    .toolbar-container {
        height: 25%;
    }

	.flex-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 10px;
    }

    .flex-row {
        display: flex;
        flex-grow: 1;
        justify-content: space-evenly;
    }

    * :global(.smui-paper) {
        height: calc(100% - 48px);
        display: flex;
        align-items: center;
    }

    * :global(.smui-paper__content) {
        width: 100%;
    }
</style>