<script>
    import { _selectedJob, _user, _isSettingColors, _isSettingsFormOpen, _cropAreaPixels, _flipScan, _rotationDegree, _mainViewer, _isDoublePageEnabled } from '@store';
    import IconButton from "@components/common/IconButton.svelte";
    import { delay } from "@utility";
    import * as d3 from 'd3-selection';
    import Mousetrap from '@ottozz/mousetrap';

	//icons
	import PreviewIcon from "svelte-material-icons/SelectCompare.svelte";
	import ScanIcon from "svelte-material-icons/SelectCompare.svelte";
    import SettingsIcon from "svelte-material-icons/Cog.svelte";
    import ColorIcon from "svelte-material-icons/Palette.svelte";
    import HomeIcon from "svelte-material-icons/Home.svelte";
   
    const startPrescan = async (isFast) => {
        /* Stop Mousetrap events from firing */
        Mousetrap.pause();
        
        d3.select('#scan-command-manager').dispatch(isFast ? 'start-fast-prescan' : 'start-total-prescan');

        await delay(3000);

        /* Allow Mousetrap events to fire again */
        Mousetrap.unpause();
    }

    const startScan = async () => {
        /* Stop Mousetrap events from firing */
        Mousetrap.pause();

        d3.select('#scan-command-manager').dispatch('start-scan');

        await delay(3000);

        /* Allow Mousetrap events to fire again */
        Mousetrap.unpause();
    }

    const backToJobManager = async () => {
        await window.electron.application('restart', {user: $_user, idDigit: $_selectedJob.id_digit});
    }

    const openSettings = () => {
        _isSettingsFormOpen.set(true);
    }

    const startColorLinearization = () => {
        _isSettingColors.set(true);
    }

    /* Shortcuts */
	Mousetrap.bind('ctrl+t', () => startPrescan(false));
    Mousetrap.bind('ctrl+p', () => startPrescan(true));
    Mousetrap.bind('ctrl+s', () => startScan());
</script>

<div class="flex-row">
    <div class="flex-column card">
        <IconButton icon={SettingsIcon} iconWidth=35 iconHeight=35 iconColor="var(--mdc-theme-secondary)" tooltip="Impostazioni" onclick={openSettings} />
        <IconButton icon={ColorIcon} iconWidth=35 iconHeight=35 iconColor="var(--mdc-theme-secondary)" tooltip="Avvia Linearizzazione colore" onclick={startColorLinearization} />
        <IconButton icon={HomeIcon} iconWidth=35 iconHeight=35 iconColor="var(--mdc-theme-secondary)" tooltip="Torna al Job Manager" onclick={backToJobManager} />
    </div>
    <div class="flex-column card">
        <IconButton icon={PreviewIcon} iconColor="var(--mdc-theme-primary)" tooltip="Avvia prescansione completa (Ctrl+t)" badgeText="Full" onclick={() => startPrescan(false)} />
        <IconButton icon={PreviewIcon} iconColor="var(--mdc-theme-warning)" tooltip="Avvia prescansione della sola area di crop (Ctrl+p)" badgeText="Fast" onclick={() => startPrescan(true)} />
        <IconButton icon={ScanIcon} iconColor="var(--mdc-theme-success)" tooltip="Avvia scansione (Ctrl+s)" onclick={startScan} />
    </div>
</div>

<style>
    .flex-row {
        height: 250px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .card {
        background-color: var(--mdc-theme-background);
        border: 1px solid var(--mdc-theme-disabled);
        border-radius: 5px;
        padding: 5px;
    }

    .flex-column {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        height: 100%;
    }
</style>