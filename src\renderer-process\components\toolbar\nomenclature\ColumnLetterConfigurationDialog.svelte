<script>
    import { _isConfiguringColumnLetter } from '@store';
    import { alfabeti } from '@utility';
    import { createEventDispatcher } from 'svelte';

    //smui
    import Dialog, { Title, Content } from '@smui/dialog';
    import Button, { Label } from '@smui/button';
    import Textfield from '@smui/textfield';
    import Radio from '@smui/radio';
    import <PERSON>Field from '@smui/form-field';

    let colPerPage = 2;
    let startingCol = 'a';
    let alphabet = alfabeti[0];

    const dispatch = createEventDispatcher();

    const handleConfirm = () => {
        const progressione = `${colPerPage};${startingCol};${alphabet.name}`;
        dispatch('confirm', progressione);
        _isConfiguringColumnLetter.set(false);
    }
</script>

<div>
    <Dialog bind:open={$_isConfiguringColumnLetter} scrimClickAction="">
        <Title>Configura logica di progressione</Title>
        <Content>
            <div class="flex-column">
                <!-- PROGRESSIONE -->
                <div class="flex-row" style="margin-bottom: 30px;">
                    <Textfield
                        label="Colonne per pagina"
                        variant="outlined"
                        type="number"
                        input$min="1"
                        input$emptyValueUndefined
                        bind:value={colPerPage}
                    />
                    <Textfield
                        label="Lettera di Inizio"
                        variant="outlined"
                        type="text"
                        bind:value={startingCol}
                    />
                </div>
    
                 <!-- ALFABETO -->
                 <div style="display: flex;">
                    <p style="color: var(--mdc-theme-primary);">Alfabeto:</p>
                    <div class="flex-row-radio" >
                        {#each alfabeti as curAlphabet}
                            <FormField>
                                <Radio bind:group={alphabet} value={curAlphabet}/>
                                <span class="radio-label" slot="label">{curAlphabet.name}</span>
                            </FormField>
                        {/each}
                    </div>
                </div>
            </div>
        </Content>
        <div class="flex-row-buttons">
            <Button on:click={handleConfirm} variant="raised" disabled={!colPerPage || !startingCol}>
                <Label>Conferma</Label>
            </Button>
        </div>
    </Dialog>
</div>

<style>
    .flex-column {
        display: flex;
        flex-direction: column;
    }

    .flex-row {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
    }.flex-row-radio {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        width: 80%;
    }

    .flex-row-buttons {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 10px;
    }

    p, span {
        color: var(--mdc-theme-secondary);
        margin: 0px;
        padding-top: 8px;
    }

    /* Radio */
    * :global(.mdc-radio) {
        padding-right: 0;
    }

    * :global(.mdc-form-field) {
        color: var(--mdc-theme-secondary);
    }

    /* Dialog */
    * :global(.mdc-dialog .mdc-dialog__surface) {
        min-width: 25%;
        min-height: 25%;
    }

    * :global(.mdc-dialog__content) {
        padding-top: 20px;
    }

    /* Textfield */
    * :global(.mdc-text-field) {
        height: 40px;
        width: 150px;
    }
</style>