<script>
    import { _nomenclaturaSx, _nomenclaturaDx } from '@store';
    import IconButton from "@components/common/IconButton.svelte";
    import { createEventDispatcher } from 'svelte';
    import * as d3 from "d3-selection";
    import { onMount } from "svelte";

    //smui
    import Select, { Option } from '@smui/select';
    import Textfield from '@smui/textfield';
    import Checkbox from '@smui/checkbox';

    //icons
    import LockIcon from "svelte-material-icons/Lock.svelte";
    import LockOpenIcon from "svelte-material-icons/LockOpen.svelte";
    import IncreaseIcon from "svelte-material-icons/NumericPositive1.svelte";

    const dispatch = createEventDispatcher();
    let eccezioniList = [];
    let valueSx, valoreSx = 0, parteSx = 0, valueDx, valoreDx = 0, parteDx = 0, enabledSx = false, enabledDx = false, ordinatoreSx, ordinatoreDx;
    let keepSx, increaseValoreSx, increaseParteSx, keepDx, increaseValoreDx, increaseParteDx; 

    onMount(async () => {
        /* Get eccezioni */
        eccezioniList = await window.electron.database('db-get-eccezioni');

        createEventListeners();
    })

    const createEventListeners = () => {
        d3.select('#eccezione').on('ordinatore-sx-changed', (e) => {
            ordinatoreSx = e.detail.value;
        });
        
        d3.select('#eccezione').on('ordinatore-dx-changed', (e) => {
            ordinatoreDx = e.detail.value;

            if(!ordinatoreDx){
                enabledDx = false;
                valueDx = null;
                valoreDx = 0;
                parteDx = 0;
                keepDx = false;
                increaseValoreDx = false;
                increaseParteDx = false;
                /* handleChangeEccezioneDx will fire automatically now */
            }
        });

        d3.select('#eccezione').on('scan-completed', (e) => {
            if(enabledSx && !keepSx){
                enabledSx = false;
                valueSx = null;
                valoreSx = 0;
                parteSx = 0;
                keepSx = false;
                increaseValoreSx = false;
                increaseParteSx = false;
                /* handleChangeEccezioneSx will fire automatically now */
            }

            if(enabledSx && keepSx){
                valoreSx = increaseValoreSx ? parseInt(valoreSx) + 1 : parseInt(valoreSx);
                parteSx = increaseParteSx ? parseInt(parteSx) + 1 : parseInt(parteSx);

                handleChangeValoreSx();
                handleChangeParteSx();
            }

            if(enabledDx && !keepDx){
                enabledDx = false;
                valueDx = null;
                valoreDx = 0;
                parteDx = 0;
                keepDx = false;
                increaseValoreDx = false;
                increaseParteDx = false;
                /* handleChangeEccezioneDx will fire automatically now */
            }

            if(enabledDx && keepDx){
                valoreDx = increaseValoreDx ? parseInt(valoreDx) + 1 : parseInt(valoreDx);
                parteDx = increaseParteDx ? parseInt(parteDx) + 1 : parseInt(parteDx);

                handleChangeValoreDx();
                handleChangeParteDx();
            }
        });
    }

    const handleChangeValoreSx = () => {
        /* Update nomenclatura valore */
        $_nomenclaturaSx.eccezione.valore = valoreSx;

        /* Pad value to show it in textfield */
        valoreSx = String(valoreSx).padStart(2, '0');
        
        /* Inform parent that value has changed */
        dispatch('changeSx');
    }

    const handleChangeEccezioneSx = () => {
        /* Update nomenclatura eccezione */
        $_nomenclaturaSx.eccezione = structuredClone(valueSx);

        if(valueSx) {
            /* Initialize valore and parte for new eccezione */
            valoreSx = valueSx.starting_value;
            parteSx = 0;

            /* Update nomenclatura valore and parte */
            $_nomenclaturaSx.eccezione.valore = valoreSx;
            $_nomenclaturaSx.eccezione.parte = parteSx;

            /* Pad value to show it in textfield */
            valoreSx = String(valoreSx).padStart(2, '0');
            parteSx = String(parteSx).padStart(4, '0');
        }

        /* Show name_short in textfield */
        const eccezioneDiv = d3.select('#eccezione');
        const selectElement = eccezioneDiv.selectAll('.mdc-select__selected-text').nodes()[0];
        d3.select(selectElement).html(valueSx?.name_short || null);
        
        /* Inform parent that value has changed */
        dispatch('changeSx');
    }

    const handleChangeParteSx = () => {
        /* Update nomenclatura parte */
        $_nomenclaturaSx.eccezione.parte = parteSx;

        /* Pad value to show it in textfield */
        parteSx = String(parteSx).padStart(4, '0');
        
        /* Inform parent that value has changed */
        dispatch('changeSx');
    }

    const handleChangeValoreDx = () => {
        /* Update nomenclatura valore */
        $_nomenclaturaDx.eccezione.valore = valoreDx;

        /* Pad value to show it in textfield */
        valoreDx = String(valoreDx).padStart(2, '0');
        
        /* Inform parent that value has changed */
        dispatch('changeDx');
    }

    const handleChangeEccezioneDx = () => {
        /* Update nomenclatura eccezione */
        $_nomenclaturaDx.eccezione = structuredClone(valueDx);

        if(valueDx) {
            /* Initialize valore and parte for new eccezione */
            valoreDx = valueDx.starting_value;
            parteDx = 0;

            /* Update nomenclatura valore and parte */
            $_nomenclaturaDx.eccezione.valore = valoreDx;
            $_nomenclaturaDx.eccezione.parte = parteDx;

            /* Pad value to show it in textfield */
            valoreDx = String(valoreDx).padStart(2, '0');
            parteDx = String(parteDx).padStart(4, '0');
        }

        /* Show name_short in textfield */
        const eccezioneDiv = d3.select('#eccezione');
        const selectElement = eccezioneDiv.selectAll('.mdc-select__selected-text').nodes()[1];
        d3.select(selectElement).html(valueDx?.name_short || null);
        
        /* Inform parent that value has changed */
        dispatch('changeDx');
    }

    const handleChangeParteDx = () => {
        /* Update nomenclatura parte */
        $_nomenclaturaDx.eccezione.parte = parteDx;

        /* Pad value to show it in textfield */
        parteDx = String(parteDx).padStart(4, '0');
        
        /* Inform parent that value has changed */
        dispatch('changeDx');
    }

    const handleEnableSx = () => {
        valueSx = null;
        valoreSx = 0;
        parteSx = 0;
        keepSx = false;
        increaseValoreSx = false;
        increaseParteSx = false;
        handleChangeEccezioneSx();
    }

    const handleEnableDx = () => {
        valueDx = null;
        valoreDx = 0;
        parteDx = 0;
        keepDx = false;
        increaseValoreDx = false;
        increaseParteDx = false;
        handleChangeEccezioneDx();
        
        /* dispatch events */
        d3.select('#nomenclature-tools').dispatch('enable-dx', {detail: {name: 'eccezione', value: enabledDx}});
    }
</script>

<div id="eccezione" class="flex-column">
    <!-- LABEL -->
    <div class="field-label">Eccezione</div>

    <!-- TOOLS SX -->
    <div class="field-tools">
        {#if keepSx}
            <IconButton icon={IncreaseIcon} iconWidth=25 iconHeight=25 iconViewbox="0 0 24 24" iconColor={increaseValoreSx ? "var(--mdc-theme-primary)" : "var(--mdc-theme-secondary)"} tooltip="Incrementa valore"  onclick={() => increaseValoreSx = !increaseValoreSx}/>
        {/if}
        <div class="flex-row">
            <Checkbox bind:checked={enabledSx} on:change={handleEnableSx} disabled={!ordinatoreSx?.exceptions_enabled}/>
            {#if valueSx}
                <IconButton icon={keepSx ? LockIcon : LockOpenIcon} iconWidth=25 iconHeight=25 iconViewbox="0 0 24 24" iconColor={keepSx ? "var(--mdc-theme-primary)" : "var(--mdc-theme-secondary)"} tooltip="Mantieni eccezione"  onclick={() => keepSx = !keepSx}/>
            {/if}
        </div>
        {#if keepSx}
            <IconButton icon={IncreaseIcon} iconWidth=25 iconHeight=25 iconViewbox="0 0 24 24" iconColor={increaseParteSx ? "var(--mdc-theme-primary)" : "var(--mdc-theme-secondary)"} tooltip="Incrementa parte"  onclick={() => increaseParteSx = !increaseParteSx}/>
        {/if}
    </div>

    <!-- SX -->
    <div class="flex-row">
        <!-- VALORE -->
        {#if valueSx}
            <Textfield 
                style="width: 85px;"
                helperLine$style="width: 85px;"
                variant="outlined"
                noLabel
                type="number"
                input$min={valueSx?.starting_value || 0}
                bind:value={valoreSx}
                on:change={handleChangeValoreSx}
            />
        {/if}

        <!-- ECCEZIONE -->
        <Select
        variant="outlined"
        noLabel
        class={!valueSx ? 'expand' : null}
        disabled={!enabledSx}
        bind:value={valueSx}
        key={(eccezione) => `${eccezione ? eccezione.metis_code : ''}`}
        on:SMUISelect:change={handleChangeEccezioneSx}
        >
            {#each eccezioniList as eccezione}
                <Option value={eccezione}>{eccezione.name_long}</Option>
            {/each}
        </Select>
        
        <!-- PARTE -->
        {#if valueSx}
            <Textfield 
                style="width: 85px;"
                helperLine$style="width: 85px;"
                variant="outlined"
                noLabel
                type="number"
                input$min="0"
                bind:value={parteSx}
                on:change={handleChangeParteSx}
            >
            </Textfield>
        {/if}
    </div>

    <!-- DX -->
     <!-- TOOLS DX -->
    <div class="field-tools">
        {#if keepDx}
            <IconButton icon={IncreaseIcon} iconWidth=25 iconHeight=25 iconViewbox="0 0 24 24" iconColor={increaseValoreDx ? "var(--mdc-theme-primary)" : "var(--mdc-theme-secondary)"} tooltip="Incrementa valore"  onclick={() => increaseValoreDx = !increaseValoreDx}/>
        {/if}
        <div class="flex-row">
            <Checkbox bind:checked={enabledDx} on:change={handleEnableDx} disabled={!ordinatoreDx?.exceptions_enabled}/>
            {#if valueDx}
                <IconButton icon={keepDx ? LockIcon : LockOpenIcon} iconWidth=25 iconHeight=25 iconViewbox="0 0 24 24" iconColor={keepDx ? "var(--mdc-theme-primary)" : "var(--mdc-theme-secondary)"} tooltip="Mantieni eccezione"  onclick={() => keepDx = !keepDx}/>
            {/if}
        </div>
        {#if keepDx}
            <IconButton icon={IncreaseIcon} iconWidth=25 iconHeight=25 iconViewbox="0 0 24 24" iconColor={increaseParteDx ? "var(--mdc-theme-primary)" : "var(--mdc-theme-secondary)"} tooltip="Incrementa parte"  onclick={() => increaseParteDx = !increaseParteDx}/>
        {/if}
    </div>
    
    <div class="flex-row">
        <!-- VALORE -->
        {#if valueDx}
            <Textfield
                variant="outlined"
                noLabel
                type="number"
                input$min={valueDx.starting_value}
                bind:value={valoreDx}
                on:change={handleChangeValoreDx}
            />
        {/if}

        <!-- ECCEZIONE -->
        <Select
        variant="outlined"
        noLabel
        class={!valueDx ? 'expand' : null}
        disabled={!enabledDx}
        bind:value={valueDx}
        key={(eccezione) => `${eccezione ? eccezione.metis_code : ''}`}
        on:SMUISelect:change={handleChangeEccezioneDx}
        >
            {#each eccezioniList as eccezione}
                <Option value={eccezione}>{eccezione.name_long}</Option>
            {/each}
        </Select>

        <!-- PARTE -->
        {#if valueDx}
            <Textfield
                variant="outlined"
                noLabel
                type="number"
                input$min="0"
                bind:value={parteDx}
                on:change={handleChangeParteDx}
            >
            </Textfield>
        {/if}
    </div>
</div>

<style>
    .flex-column {
        display: flex;
        flex-direction: column;
    }
    
    .flex-row {
        display: flex;
        align-items: center;
    }

    .field-label {
        border: 1px solid var(--mdc-theme-secondary);
        background-color: var(--mdc-theme-background);
        color: var(--mdc-theme-secondary);
        display: flex;
        align-items: center;
        justify-content: center;
        height: 30px;
        padding: 0 5px;
    }

    .field-tools {
        border: 1px solid var(--mdc-theme-secondary);
        background-color: var(--mdc-theme-background);
        display: flex;
        align-items: center;
        justify-content: space-around;
        height: 30px;
    }

    /* Select */
    * :global(.mdc-select__menu.mdc-menu.mdc-menu-surface.mdc-menu-surface--open.mdc-menu-surface--fullwidth){
        width: max-content;
    }

    * :global(.mdc-select.mdc-menu-surface--anchor.expand) {
        width: 100%;
    }

    * :global(.mdc-select__anchor){
        min-width: 140px;
        width: fit-content;
        padding: 0px 16px;
        height: 40px;
    }

    * :global(.mdc-select:not(.mdc-select--disabled) .mdc-select__selected-text){
        color: var(--mdc-theme-primary);
        font-weight: bold;
    }

    * :global(.mdc-select--disabled) {
        cursor: default;
        pointer-events: none;
        background-image: url('/diagonal-stripes.svg');
        border: 1px solid var(--mdc-theme-secondary);
        height: 38px;
    }

    * :global(.mdc-select__dropdown-icon){
        width: 0;
        margin: 0;
    }

    * :global(.mdc-notched-outline__leading) {
        border-radius: 0px !important;
    }

    * :global(.mdc-notched-outline__trailing) {
        border-radius: 0px !important;
    }

    /* Select scrollbar */
    * :global(.mdc-select__menu::-webkit-scrollbar) {
        width: 10px;
    }

    * :global(.mdc-select__menu::-webkit-scrollbar-track) {
        background-color: white;
        border-radius: 10px;
    }

    * :global(.mdc-select__menu::-webkit-scrollbar-thumb) {
        background-color: var(--mdc-theme-primary);
        border-radius: 10px;
    }

    /* Textfield */
    * :global(.mdc-text-field) {
        height: 40px;
        width: 85px;
    }

    * :global(.mdc-text-field--disabled) {
        pointer-events: none;
        background-image: url('/diagonal-stripes.svg');
        width: auto !important;
    }

    * :global(.mdc-text-field:not(.mdc-text-field--disabled) .mdc-text-field__affix) {
        color: var(--mdc-theme-primary);
        font-weight: bold;
    }

    * :global(.mdc-text-field--disabled .mdc-text-field__affix) {
        display: none;
    }

    * :global(.mdc-text-field:not(.mdc-text-field--disabled) .mdc-text-field__input) {
        color: var(--mdc-theme-primary);
        font-weight: bold;
    }

    * :global(.mdc-text-field--disabled .mdc-text-field__input) {
        display: none;
    }

    /* Checkbox */
    * :global(.mdc-checkbox) {
        transform: scale(0.8);
    }

    * :global(.mdc-checkbox--disabled) {
        pointer-events: auto;
        cursor: not-allowed;
    }

    * :global(.mdc-checkbox--disabled .mdc-checkbox__background) {
        border-color: var(--mdc-theme-disabled) !important;
    }

    * :global(.mdc-checkbox--selected.mdc-checkbox--disabled .mdc-checkbox__background) {
        background-color: var(--mdc-theme-primary-hover) !important;
    }

    * :global(.mdc-checkbox__ripple) {
        top: 3px;
        left: 4px;
    }

    /* Icon Button */
    * :global(.mdc-icon-button) {
        padding: 0;
    }
</style>