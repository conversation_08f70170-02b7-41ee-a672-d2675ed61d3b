<script>
    import { _nomenclaturaSx, _nomenclaturaDx } from '@store';
    import { createEventDispatcher } from 'svelte';
    import * as d3 from 'd3-selection';
    import { onMount } from "svelte";
    
    //smui
    import Textfield from '@smui/textfield';
    import Checkbox from '@smui/checkbox';
    
    const dispatch = createEventDispatcher();
    let valueSx = 0, valueDx = 0, enabledSx = false, enabledDx = false, ordinatoreSx, ordinatoreDx;

    onMount(() => {
        createEventListeners();
    })

    const createEventListeners = () => {
        d3.select('#interni-pagina').on('ordinatore-sx-changed', (e) => {
            ordinatoreSx = e.detail.value;
        });

        d3.select('#interni-pagina').on('ordinatore-dx-changed', (e) => {
            ordinatoreDx = e.detail.value;

            if(!ordinatoreDx) {
                enabledDx = false;
                valueDx = 0;
                handleChangeDx();
            }
        });
    }

    const handleChangeSx = () => {
        /* Update nomenclatura */
        $_nomenclaturaSx.internoPagina = valueSx;
        
        /* Pad value to show it in textfield */
        valueSx = String(valueSx).padStart(4, '0');

        /* Inform parent that value has changed */
        dispatch('changeSx');
    }

    const handleChangeDx = () => {
        /* Update nomenclatura */
        $_nomenclaturaDx.internoPagina = valueDx;
        
        /* Pad value to show it in textfield */
        valueDx = String(valueDx).padStart(4, '0');

        /* Inform parent that value has changed */
        dispatch('changeDx');
    }

    const handleEnableSx = () => {
        valueSx = enabledSx ? 1 : 0;
        handleChangeSx();
    }

    const handleEnableDx = () => {
        valueDx = enabledDx ? 1 : 0;
        handleChangeDx();
    }
</script>

<div id="interni-pagina" class="flex-column">
    <!-- LABEL -->
    <div class="field-label">Interni</div>

    <!-- TOOLS SX -->
    <div class="field-tools">
        <Checkbox bind:checked={enabledSx} on:change={handleEnableSx} disabled={!ordinatoreSx?.interni_enabled}/>
    </div>

    <!-- SX -->
    <Textfield
        noLabel
        variant="outlined"
        type="number"
        input$min="0"
        prefix="-sub."
        disabled={!enabledSx}
        bind:value={valueSx}
        on:change={handleChangeSx}
    />

    <!-- TOOLS DX -->
    <div class="field-tools">
        <Checkbox bind:checked={enabledDx} on:change={handleEnableDx} disabled={!ordinatoreDx?.interni_enabled}/>
    </div>
    
    <!-- DX -->
    <Textfield
        noLabel
        variant="outlined"
        type="number"
        input$min="0"
        prefix="-sub."
        disabled={!enabledDx}
        bind:value={valueDx}
        on:change={handleChangeDx}
    />
</div>

<style>
    .flex-column {
        display: flex;
        flex-direction: column;
    }

    .field-label {
        border: 1px solid var(--mdc-theme-secondary);
        background-color: var(--mdc-theme-background);
        color: var(--mdc-theme-secondary);
        display: flex;
        align-items: center;
        justify-content: center;
        height: 30px;
        padding: 0 5px;
    }

    .field-tools {
        border: 1px solid var(--mdc-theme-secondary);
        background-color: var(--mdc-theme-background);
        display: flex;
        align-items: center;
        justify-content: center;
        height: 30px;
    }

    /* Textfield */
    * :global(.mdc-text-field) {
        height: 40px;
        width: 125px;
    }

    * :global(.mdc-text-field--disabled) {
        pointer-events: none;
        background-image: url('/diagonal-stripes.svg');
        width: auto !important;
    }
    
    * :global(.mdc-text-field:not(.mdc-text-field--disabled) .mdc-text-field__affix) {
        color: var(--mdc-theme-primary);
        font-weight: bold;
    }

    * :global(.mdc-text-field--disabled .mdc-text-field__affix) {
        display: none;
    }

    * :global(.mdc-text-field:not(.mdc-text-field--disabled) .mdc-text-field__input) {
        color: var(--mdc-theme-primary);
        font-weight: bold;
    }

    * :global(.mdc-text-field--disabled .mdc-text-field__input) {
        display: none;
    }

    * :global(.mdc-notched-outline__leading) {
        border-radius: 0px !important;
    }

    * :global(.mdc-notched-outline__trailing) {
        border-radius: 0px !important;
    }
    
    /* Checkbox */
    * :global(.mdc-checkbox) {
        transform: scale(0.8);
    }

    * :global(.mdc-checkbox--disabled) {
        pointer-events: auto;
        cursor: not-allowed;
    }

    * :global(.mdc-checkbox--disabled .mdc-checkbox__background) {
        border-color: var(--mdc-theme-disabled) !important;
    }

    * :global(.mdc-checkbox--selected.mdc-checkbox--disabled .mdc-checkbox__background) {
        background-color: var(--mdc-theme-primary-hover) !important;
    }

    * :global(.mdc-checkbox__ripple) {
        top: 3px;
        left: 4px;
    }
</style>