<script>
    import { _nomenclaturaDx, _nomenclaturaSx, _selectedJob, _isDoublePageEnabled, _cropLeftAreaPixels, _cropAreaPixels, _savedCropAreaPixels } from "@store";
    import IconButton from "@components/common/IconButton.svelte";
    import NomenclatureTools from "@components/toolbar/nomenclature/NomenclatureTools.svelte";
    import InternoPagina from "@components/toolbar/nomenclature/InternoPagina.svelte";
    import Ordinatore from "@components/toolbar/nomenclature/Ordinatore.svelte";
    import NumeroPagina from "@components/toolbar/nomenclature/NumeroPagina.svelte";
    import TipoPagina from "@components/toolbar/nomenclature/TipoPagina.svelte";
    import Eccezione from "@components/toolbar/nomenclature/Eccezione.svelte";
    import { romanize } from "@utility";
    import { onMount } from "svelte";
    import * as d3 from "d3-selection";

    //icons
    import ResetCropIcon from "svelte-material-icons/CropRotate.svelte";

    let scansioniList = [], componentDidMount;

    onMount(async () => {
        componentDidMount = false;

        /* Get scansioni */
        await getScansioni();

        /* initialize Nomenclature */
        initNomenclature();

        /* Create event listeners */
        createEventListeners();

        componentDidMount = true;
    })

    const createEventListeners = () => {
        d3.select('#nomenclature').on('update-scansioni-list', async (e) => {
            /* Get scansioni */
            await getScansioni();
        });

        d3.select('#nomenclature').on('scan-completed', async (e) => {
            /* Get scansioni */
            await getScansioni();

            d3.select('#ordinatore').dispatch('scan-completed');
        });        
    }

    const getScansioni = async () => {
        scansioniList = await window.electron.database('db-get-scansioni', { idDigit: $_selectedJob.id_digit });
    }

    const initNomenclature = () => {
        _nomenclaturaSx.set({});
        _nomenclaturaDx.set({});
    }

    const updateDisplayName = (side) => {        
        if(side == 'sx')
            $_nomenclaturaSx.displayName = buildDisplayName($_nomenclaturaSx);
        else if($_nomenclaturaDx?.ordinatore)
            $_nomenclaturaDx.displayName = buildDisplayName($_nomenclaturaDx);
        else _nomenclaturaDx.set({});

        /* Check if a scan with the same displayName already exists */
        checkNomenclatureDuplicate(side, side == 'sx' ? $_nomenclaturaSx : $_nomenclaturaDx);

        /* Send event to update text overlay */
        d3.select('#main-viewer').dispatch('update-text-overlay');
    }
    
    const buildDisplayName = (nomenclatura) => {
        if(!nomenclatura?.ordinatore) return '';

        let displayName = '';

        /* INTERNO */
        if(nomenclatura.internoPagina > 0)
            displayName = `-sub.${String(nomenclatura.internoPagina).padStart(4, '0')}_`;
        
        /* ORDINATORE */
        displayName += nomenclatura.ordinatore.name_short;

        /* PAGE NUMBER */
        /* Prefix params */
        if(nomenclatura.ordinatore.page_number_prefix){
            if(!nomenclatura.ordinatore.page_number_prefix.includes('$'))
                displayName += `${nomenclatura.ordinatore.page_number_prefix}`;
            else
                displayName += `${nomenclatura.ordinatore.page_number_prefix.replace('$', nomenclatura.prefissoNumeroPagina)}`;
        }
        /* Value */
        switch (nomenclatura.ordinatore.page_number_type) {
            case 'number':
            case '(prefix)_number':
                if(nomenclatura.ordinatore.name_short == 'nf' || nomenclatura.ordinatore.name_short == 'np')
                    displayName += `[${String(nomenclatura.numeroPagina)}${nomenclatura.tipoPagina?.charAt(0) || ''}]`;
                else if(nomenclatura.ordinatore.roman_number) 
                    displayName += `${romanize(nomenclatura.numeroPagina)}`;
                else
                    displayName += `${String(nomenclatura.numeroPagina).padStart(nomenclatura.ordinatore.page_number_pad, '0')}`;
                break;
            case 'text':
                    displayName += `${String(nomenclatura.letteraPagina)}`;
                break;
            case 'letter':
                displayName += `${nomenclatura.letteraPagina == 'pi' ? 'pi.greco' : nomenclatura.letteraPagina}${Number.isFinite(Number(nomenclatura.letteraPagina)) ? '.' : ''}${nomenclatura.numeroPagina ? String(nomenclatura.numeroPagina).padStart(nomenclatura.ordinatore.page_number_pad, '0') : ''}`;
                break;
            case 'column_number':
                if(nomenclatura.ordinatore.roman_number)
                    displayName += `${romanize(nomenclatura.numeroPagina)}-${romanize(nomenclatura.numeroPaginaEnd)}`;
                else
                    displayName += `${String(nomenclatura.numeroPagina).padStart(nomenclatura.ordinatore.page_number_pad, '0')}-${String(nomenclatura.numeroPaginaEnd).padStart(nomenclatura.ordinatore.page_number_pad, '0')}`;
                break;
            case 'column_letter':
                displayName += `${nomenclatura.letteraPagina == 'pi' ? 'pi.greco' : nomenclatura.letteraPagina}-${nomenclatura.letteraPaginaEnd}`;
                break;
            default:
                break;
        }

        /* PAGE TYPE */
        if(nomenclatura.ordinatore.page_type_enabled && nomenclatura.ordinatore.name_short != 'nf')
            displayName += nomenclatura.tipoPagina?.charAt(0) || '';

        /* ECCEZIONE */
        if(nomenclatura.eccezione){
            /* Valore */
            displayName += `.[${String(nomenclatura.eccezione.valore).padStart(2, '0')}`;

            /* Codice */
            displayName += `.${nomenclatura.eccezione.name_short}`;

            /* Parte */
            if(nomenclatura.eccezione.parte == 0 || nomenclatura.eccezione.parte_as_number)
                displayName += `.${String(nomenclatura.eccezione.parte).padStart(4, '0')}]`;
            else
                displayName += `.${String.fromCharCode(nomenclatura.eccezione.parte + 96).padEnd(4, '0')}]`;
        }

        return displayName;
    }
    
    const checkNomenclatureDuplicate = (side, nomenclatura) => {
        let sequenzialeDuplicato;
        for (const scan of scansioniList) {
            if(side == 'sx' && nomenclatura?.displayName == scan.display_name_sx){
                sequenzialeDuplicato = scan.sequenziale_scansione;
                break;
            }

            if(side == 'dx' && nomenclatura?.displayName == scan.display_name_dx && scan.display_name_dx) {
                sequenzialeDuplicato = scan.sequenziale_scansione;
                break;
            }
        }

        nomenclatura.sequenzialeDuplicato = sequenzialeDuplicato || null;
    }

    const handleLoadCropDefault = () => {
        d3.select('#main-viewer').dispatch('load-default-dimensions');
    }
</script>

<div id="nomenclature" class="flex-column" >
    <!-- LABELS -->
     <div class="flex-row">
        <div class="text">
            <p><span class="title">Segnatura:</span> { $_selectedJob.is_stampato ? $_selectedJob.identifier + ' (' + $_selectedJob.segnatura + ')' : $_selectedJob.identifier}</p>
        </div>
        <div class="flex-row text">
            <p><span class="title">Crop:</span>
                {#if $_isDoublePageEnabled}
                    {$_cropLeftAreaPixels.width}x{$_cropLeftAreaPixels.height}
                {:else}
                    {$_cropAreaPixels.width}x{$_cropAreaPixels.height}
                {/if}
            </p>
            <IconButton icon={ResetCropIcon} iconWidth=25 iconHeight=25 iconColor={"var(--mdc-theme-secondary)"} tooltip="Carica dimensioni di default"  onclick={handleLoadCropDefault} disabled={!$_savedCropAreaPixels}/>
        </div>
    </div>

    <!-- TABLE -->
    {#if componentDidMount}
        <div class="flex-row">
            <!-- TOOLS -->
            <NomenclatureTools />
            <!-- INTERNI -->
            <InternoPagina on:changeSx={() => updateDisplayName('sx')} on:changeDx={() => updateDisplayName('dx')}/>
            <!-- ORDINATORE -->
            <Ordinatore {scansioniList} on:changeSx={() => updateDisplayName('sx')} on:changeDx={() => updateDisplayName('dx')}/>
            <!-- NUMERO PAGINA -->
            <NumeroPagina {scansioniList} on:changeSx={() => updateDisplayName('sx')} on:changeDx={() => updateDisplayName('dx')}/>
            <!-- TIPO PAGINA -->
            <TipoPagina on:changeSx={() => updateDisplayName('sx')} on:changeDx={() => updateDisplayName('dx')}/>
            <!-- ECCEZIONE -->
            <Eccezione on:changeSx={() => updateDisplayName('sx')} on:changeDx={() => updateDisplayName('dx')}/>
        </div>
    {/if}
</div>

<style>
    .flex-column {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .flex-row {
        display: flex;
        justify-content: space-evenly;
    }

    .text {
        background-color: var(--mdc-theme-background);
        box-shadow: 0px 0px 10px 0px var(--mdc-theme-background) inset;
        border-radius: 5px;
        white-space: nowrap;
    }

    p {
        color: var(--mdc-theme-secondary);
        margin: 10px;
    }

    .title {
        color: var(--mdc-theme-primary);
    }

    /* Icon Button */
    * :global(.mdc-icon-button){
        padding: 0px 5px 0px 0px;
    }
</style>