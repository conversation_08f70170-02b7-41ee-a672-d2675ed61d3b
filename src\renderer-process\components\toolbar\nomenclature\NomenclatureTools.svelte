<script>
    import { _isDoublePageEnabled, _isEbraicOrder, _nomenclaturaSx, _nomenclaturaDx, _selectedJob } from '@store';
    import IconButton from "@components/common/IconButton.svelte";

    // icons
    import StarDavidIcon from "svelte-material-icons/StarDavid.svelte";
    import AlertIcon from "svelte-material-icons/Alert.svelte";

    const handleEbraicOrder = () => {
        _isEbraicOrder.set(!$_isEbraicOrder);
    }
</script>


<div id="nomenclature-tools" class="flex-column">
    <div class="tool">
        <IconButton icon={StarDavidIcon} iconWidth=25 iconHeight=25 iconColor={$_isEbraicOrder ? "var(--mdc-theme-primary)" : "var(--mdc-theme-secondary)"} tooltip="Abilita ordinamento arabo"  onclick={handleEbraicOrder}/>
    </div>

    <div class="filler sx">
        {#if $_nomenclaturaSx?.sequenzialeDuplicato && !$_selectedJob.is_stampato}
            <AlertIcon width=20 height=20 viewBox="0 0 24 24" color="var(--mdc-theme-warning)"/>
        {/if}
    </div>
    <div class="label sx">Sx</div>
    
    <div class="tool dx">
        {#if $_nomenclaturaDx?.sequenzialeDuplicato && !$_selectedJob.is_stampato}
            <AlertIcon width=20 height=20 viewBox="0 0 24 24" color="var(--mdc-theme-warning)"/>
        {/if}
    </div>
    <div class="label dx">Dx</div>
</div>

<style>
    .flex-column {
        display: flex;
        flex-direction: column;
    }

    .label, .tool, .filler {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 25px;
        border: 1px solid var(--mdc-theme-secondary);
        background-color: var(--mdc-theme-background);
        color: var(--mdc-theme-secondary);
        padding: 0 5px;
    }

    .label {
        height: 40px;
    }

    .tool, .filler {
        height: 30px;
    }

    .filler.sx, .tool.dx {
        border-bottom: none;
    }

    .label.sx, .label.dx {
        border-top: none;
    }
    
    /* Checkbox */
    * :global(.mdc-checkbox) {
        transform: scale(0.8);
    }

    /* Icon Button */
    * :global(.mdc-icon-button) {
        padding: 0;
    }
</style>