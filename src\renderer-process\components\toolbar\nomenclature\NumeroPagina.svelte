<script>
    import { _nomenclaturaSx, _nomenclaturaDx, _selectedJob, _isConfiguringLetter, _isConfiguringColumnNumber, _isConfiguringColumnLetter, _progressioneSx, _progressioneDx, _acquisitionMode } from '@store';
    import LetterConfigurationDialog from '@components/toolbar/nomenclature/LetterConfigurationDialog.svelte';
    import ColumnNumberConfigurationDialog from '@components/toolbar/nomenclature/ColumnNumberConfigurationDialog.svelte';
    import ColumnLetterConfigurationDialog from '@components/toolbar/nomenclature/ColumnLetterConfigurationDialog.svelte';
    import { createEventDispatcher } from 'svelte';
    import { formatAlphabet, increaseAlphabet, getAlphabetFromName, notify } from "@utility";
    import * as d3 from 'd3-selection';
    import { onMount } from "svelte";

    //smui
    import Textfield from '@smui/textfield';
    import Checkbox from '@smui/checkbox';
    
    const dispatch = createEventDispatcher();
    export let scansioniList = [];
    let letterSx = '', letterEndSx = '', numberPrefixSx = '', numberSx = 0, numberEndSx = 0, letterDx = '', letterEndDx = '', numberPrefixDx = '', numberDx = 0, numberEndDx = 0, enabledSx = false, enabledDx = false, ordinatoreSx, ordinatoreDx;
    let progressioniListSx = [], progressioniListDx = [];

    onMount(async () => {
        _progressioneSx.set(null);
        _progressioneDx.set(null);
        createEventListeners();
    })

    const createEventListeners = () => {
        d3.select('#numero-pagina').on('ordinatore-sx-changed', (e) => {
            ordinatoreSx = e.detail.value;
            
            enabledSx = ordinatoreSx ? ordinatoreSx.page_number_type : false;
            initValueSx(ordinatoreSx?.page_number_type);
            handleChangeSx();
        });

        d3.select('#numero-pagina').on('ordinatore-dx-changed', (e) => {
            ordinatoreDx = e.detail.value;

            enabledDx = ordinatoreDx ? ordinatoreDx.page_number_type : false;
            initValueDx(ordinatoreDx?.page_number_type);
            handleChangeDx();
        });

        d3.select('#numero-pagina').on('scan-completed', (e) => {
            const newOrdinatoreSx = e.detail.ordinatoreSx;
            const newOrdinatoreDx = e.detail.ordinatoreDx;

            /* Update ordinatore Sx if needed */
            if(enabledSx && ordinatoreSx.metis_code == newOrdinatoreSx.metis_code){
                ordinatoreSx = e.detail.ordinatoreSx;
                initValueSx(ordinatoreSx.page_number_type);
                handleChangeSx();
            }

            /* Update ordinatore Dx if needed */
            if(enabledDx && ordinatoreDx.metis_code == newOrdinatoreDx.metis_code){
                ordinatoreDx = e.detail.ordinatoreDx;
                initValueDx(ordinatoreDx?.page_number_type);
                handleChangeDx();
            }
        });
    }

    const initValueSx = (type) => {        
        if(!type){
            letterSx = '';
            letterEndSx = '';
            numberPrefixSx = '';
            numberSx = 0;
            numberEndSx = 0;
            _progressioneSx.set(null);
            return;
        }

        if(type != 'letter'){
            progressioniListSx.length = 0;
            _progressioneSx.set(null);
        }

        const lastScan = scansioniList[scansioniList.length - 1];
        const lastOrdinatoreCode = lastScan.pagina_doppia ? lastScan.codice_ordinatore_dx : lastScan.codice_ordinatore_sx;
        let lastProgressione = lastScan.pagina_doppia ? lastScan.progressione_dx : lastScan.progressione_sx;
        const lastNumberPrefix = lastScan.pagina_doppia ? lastScan.prefisso_numero_pagina_dx : lastScan.prefisso_numero_pagina_sx;
        const lastNumber = lastScan.pagina_doppia ? lastScan.numero_pagina_dx : lastScan.numero_pagina_sx;
        const lastNumberEnd = lastScan.pagina_doppia ? lastScan.numero_pagina_fine_dx : lastScan.numero_pagina_fine_sx;
        const lastLetter = lastScan.pagina_doppia ? lastScan.lettera_dx : lastScan.lettera_sx;
        const lastLetterEnd = lastScan.pagina_doppia ? lastScan.lettera_fine_dx : lastScan.lettera_fine_sx;

        /* NUMBER PREFIX */
        numberPrefixSx = type.includes('prefix') ? lastNumberPrefix : '';

        /* NUMBER */
        if(type == 'number' || type == '(prefix)_number'){
            let numberToSet = 1;

            const shouldIncrement = ordinatoreSx.metis_code == lastOrdinatoreCode;
            if($_acquisitionMode == 'normal' && shouldIncrement)
                numberToSet = lastNumber + ordinatoreSx.page_increment - 1;
            else if(($_acquisitionMode == 'leggio-recto' || $_acquisitionMode == 'leggio-verso') && shouldIncrement)
                numberToSet = Number(numberSx) + Number(ordinatoreSx.page_increment);
            
            numberSx = numberToSet;
            return;
        }

        /* TEXT */
        if(type == 'text'){
            letterSx = '';
            return;
        }

        /* LETTER */
        if(type == 'letter') {
            /* Check if there is not a progression to continue */
            if(progressioniListSx.length == 0 && progressioniListDx.length == 0){
                notify.warning("La progressione per questo ordinatore deve inizare da un recto");
                letterSx = '';
                numberSx = null;
                $_nomenclaturaSx.progressioneIndex = null;
                _progressioneSx.set(null);
                return;
            }

            /* Set same progression if there is one on Dx */
            if(progressioniListSx.length == 0)
                progressioniListSx = progressioniListDx.map(x => x);

            const lastProgressioneIndexSx = lastScan.indice_progressione_sx;
            const nextProgressioneIndexSx = lastProgressioneIndexSx != null ? lastProgressioneIndexSx + ordinatoreSx.page_increment : ordinatoreSx.page_increment - 1;                

            /* Handle end of progression */
            if(!progressioniListSx[nextProgressioneIndexSx]){
                notify.warning("La progressione è terminata per il foglio Sx");
                
                /* Check if ther is another progression started on Dx to continue */
                if(ordinatoreDx) {
                    const lastProgressioneIndexDx = lastScan.indice_progressione_dx;
                    const nextProgressioneIndexDx = lastProgressioneIndexDx != null ? lastProgressioneIndexDx + ordinatoreDx.page_increment : ordinatoreDx.page_increment - 1;
                    
                    if(progressioniListDx.length > 0 && progressioniListDx[nextProgressioneIndexDx]) {
                        progressioniListSx = progressioniListDx.map(x => x);
                        _progressioneSx.set($_progressioneDx);

                        const nextProgressioneIndexSx = 0;
                        letterSx = progressioniListSx[nextProgressioneIndexSx].letter;
                        numberSx = progressioniListSx[nextProgressioneIndexSx].number;
                        $_nomenclaturaSx.progressioneIndex = nextProgressioneIndexSx;
                        return;
                    }
                }

                progressioniListSx.length = 0;
                letterSx = '';
                numberSx = null;
                $_nomenclaturaSx.progressioneIndex = null;
                _progressioneSx.set(null);
                return;
            }

            if(lastProgressioneIndexSx == null){
                progressioniListSx = progressioniListDx.map(x => x);
                _progressioneSx.set($_progressioneDx)
            }

            letterSx = progressioniListSx[nextProgressioneIndexSx].letter;
            numberSx = progressioniListSx[nextProgressioneIndexSx].number;
            $_nomenclaturaSx.progressioneIndex = nextProgressioneIndexSx;
        }

        /* COLUMN NUMBER */
        if(type == 'column_number') {
            const shouldIncrement = ordinatoreSx.metis_code == lastOrdinatoreCode && lastProgressione ? true : false;

            if(!shouldIncrement){
                notify.warning("La progressione per questo ordinatore deve inizare da un recto");
                letterSx = null;
                letterEndSx = null;
                _progressioneSx.set(null);
                return;
            }

            const lastProgressioneSplit = lastProgressione.split(';');
            const colPerPage = lastProgressioneSplit[0];
            const startingCol = lastProgressioneSplit[1];

            numberSx = parseInt(lastNumberEnd) + 1;
            numberEndSx = parseInt(numberSx) + parseInt(colPerPage) - 1;
            _progressioneSx.set(lastProgressione);
        } 

        /* COLUMN LETTER */
        if(type == 'column_letter') {
            const shouldIncrement = ordinatoreSx.metis_code == lastOrdinatoreCode && lastProgressione ? true : false;

            if(!shouldIncrement){
                notify.warning("La progressione per questo ordinatore deve inizare da un recto");
                numberSx = null;
                numberEndSx = null;
                _progressioneSx.set(null);
                return;
            }

            const lastProgressioneSplit = lastProgressione.split(';');
            const colPerPage = lastProgressioneSplit[0];
            const startingCol = lastProgressioneSplit[1];
            let alphabet = formatAlphabet(getAlphabetFromName(lastProgressioneSplit[2]), startingCol);
            let alphabetIncremented = false;

            /* letterSx */
            const lastLetterIndex = alphabet.letters.findIndex(el => el == lastLetter);
            let newLetterSxIndex = lastLetterIndex + parseInt(colPerPage);
            if(!alphabet.letters[newLetterSxIndex]) {
                const alphabetIndexDiff = newLetterSxIndex - alphabet.letters.length;
                alphabet = increaseAlphabet(alphabet);
                newLetterSxIndex = alphabetIndexDiff;

                const newStartingCol = startingCol+startingCol.charAt(startingCol.length - 1);
                lastProgressioneSplit[1] = newStartingCol;
                lastProgressione = lastProgressioneSplit.join(';');
                alphabetIncremented = true;                
            }
            letterSx = alphabet.letters[newLetterSxIndex];

            /* letterEndSx */
            let newLetterEndSxIndex = newLetterSxIndex + parseInt(colPerPage) - 1;
            
            if(!alphabet.letters[newLetterEndSxIndex] && !alphabetIncremented) {
                const alphabetIndexDiff = newLetterEndSxIndex - alphabet.letters.length;
                alphabet = increaseAlphabet(alphabet);
                newLetterEndSxIndex = alphabetIndexDiff;

                const newStartingCol = startingCol+startingCol.charAt(startingCol.length - 1);
                lastProgressioneSplit[1] = newStartingCol;
                lastProgressione = lastProgressioneSplit.join(';');
            }
            letterEndSx = alphabet.letters[newLetterEndSxIndex];
            
            _progressioneSx.set(lastProgressione);
        }
        
    }

    const handleChangeSx = () => {
        /* Update nomenclatura */
        $_nomenclaturaSx.prefissoNumeroPagina = numberPrefixSx || '';
        $_nomenclaturaSx.numeroPagina = numberSx;
        $_nomenclaturaSx.numeroPaginaEnd = numberEndSx;
        $_nomenclaturaSx.letteraPagina = letterSx;
        $_nomenclaturaSx.letteraPaginaEnd = letterEndSx;
        
        /* Pad value to show it in textfield */
        if(numberSx)
            numberSx = String(numberSx).padStart(ordinatoreSx.page_number_pad, '0');

        if(numberEndSx)
            numberEndSx = String(numberEndSx).padStart(ordinatoreSx.page_number_pad, '0');

        /* Inform parent that value has changed */
        dispatch('changeSx');
    }

    const initValueDx = (type) => {
        const lastScan = scansioniList[scansioniList.length - 1];
        const lastOrdinatoreCode = lastScan.pagina_doppia ? lastScan.codice_ordinatore_dx : lastScan.codice_ordinatore_sx;
        let lastProgressione = lastScan.pagina_doppia ? lastScan.progressione_dx : lastScan.progressione_sx;
        const lastNumberEnd = lastScan.pagina_doppia ? lastScan.numero_pagina_fine_dx : lastScan.numero_pagina_fine_sx;
        const lastLetterEnd = lastScan.pagina_doppia ? lastScan.lettera_fine_dx : lastScan.lettera_fine_sx;

        if(type != 'letter'){
            progressioniListDx.length = 0;
            _progressioneDx.set(null);
        }

        /* NUMBER PREFIX */
        numberPrefixDx = numberPrefixSx;

        /* NUMBER */
        if(type == 'number' || type == '(prefix)_number') {
            numberDx = parseInt(numberSx) + 1;
            return;
        }

        /* TEXT */
        if(type == 'text'){
            letterDx = '';
            return;
        }

        /* LETTER */
        if(type == 'letter') {
            if(progressioniListDx.length == 0) {
                _isConfiguringLetter.set(true);
                return;
            }

            const lastProgressioneIndexDx = lastScan.indice_progressione_dx;
            const nextProgressioneIndexDx = lastProgressioneIndexDx != null ? lastProgressioneIndexDx + ordinatoreDx.page_increment : ordinatoreDx.page_increment - 1;

            if(!progressioniListDx[nextProgressioneIndexDx]){
                notify.warning("La progressione è terminata per il foglio Dx");
                letterDx = '';
                numberDx = null;
                progressioniListDx.length = 0;
                _progressioneDx.set(null);
                $_nomenclaturaDx.progressioneIndex = null;
                d3.select('#ordinatore').dispatch('switch-to-single-page');
                return;
            }

            letterDx = progressioniListDx[nextProgressioneIndexDx].letter;
            numberDx = progressioniListDx[nextProgressioneIndexDx].number;
            $_nomenclaturaDx.progressioneIndex = nextProgressioneIndexDx;
        }

        /* COLUMN NUMBER */
        if(type == 'column_number') {
            const shouldIncrement = ordinatoreDx.metis_code == lastOrdinatoreCode && lastProgressione ? true : false;         

            if(!shouldIncrement){
                _isConfiguringColumnNumber.set(true);
                return;
            }

            const lastProgressioneSplit = lastProgressione.split(';');
            const colPerPage = lastProgressioneSplit[0];
            numberDx = parseInt(lastNumberEnd) + parseInt(colPerPage) + 1;
            numberEndDx = parseInt(numberDx) + parseInt(colPerPage) - 1;
            _progressioneDx.set(lastProgressione);
        }

        /* COLUMN LETTER */
        if(type == 'column_letter') {
            const shouldIncrement = ordinatoreDx.metis_code == lastOrdinatoreCode && lastProgressione ? true : false;         

            if(!shouldIncrement){
                _isConfiguringColumnLetter.set(true);
                return;
            }

            const lastProgressioneSplit = lastProgressione.split(';');
            const colPerPage = lastProgressioneSplit[0];
            const startingCol = lastProgressioneSplit[1];
            let alphabet = formatAlphabet(getAlphabetFromName(lastProgressioneSplit[2]), startingCol);
            let alphabetIncremented = false;

            /* letterDx */
            const lastLetterEndIndex = alphabet.letters.findIndex(el => el == lastLetterEnd);
            let newLetterDxIndex = lastLetterEndIndex + parseInt(colPerPage) + 1;

            if(!alphabet.letters[newLetterDxIndex]) {
                const alphabetIndexDiff = newLetterDxIndex - alphabet.letters.length;
                alphabet = increaseAlphabet(alphabet);
                newLetterDxIndex = alphabetIndexDiff;

                const newStartingCol = startingCol+startingCol.charAt(startingCol.length - 1);
                lastProgressioneSplit[1] = newStartingCol;
                lastProgressione = lastProgressioneSplit.join(';');
                alphabetIncremented = true;
            }
            letterDx = alphabet.letters[newLetterDxIndex];

            /* letterEndDx */
            let newLetterEndDxIndex = newLetterDxIndex + parseInt(colPerPage) - 1;

            if(!alphabet.letters[newLetterEndDxIndex] && !alphabetIncremented) {
                const alphabetIndexDiff = newLetterEndDxIndex - alphabet.letters.length;
                alphabet = increaseAlphabet(alphabet);
                newLetterEndDxIndex = alphabetIndexDiff;

                const newStartingCol = startingCol+startingCol.charAt(startingCol.length - 1);
                lastProgressioneSplit[1] = newStartingCol;
                lastProgressione = lastProgressioneSplit.join(';');
            }
            letterEndDx = alphabet.letters[newLetterEndDxIndex];

            _progressioneDx.set(lastProgressione);
        }
    }

    const handleChangeDx = () => {
        /* Update nomenclatura */
        $_nomenclaturaDx.prefissoNumeroPagina = numberPrefixDx || '';
        $_nomenclaturaDx.numeroPagina = numberDx;
        $_nomenclaturaDx.numeroPaginaEnd = numberEndDx;
        $_nomenclaturaDx.letteraPagina = letterDx;
        $_nomenclaturaDx.letteraPaginaEnd = letterEndDx;
        
        /* Pad value to show it in textfield */        
        if(numberDx && ordinatoreDx)
            numberDx = String(numberDx).padStart(ordinatoreDx.page_number_pad, '0');

        if(numberEndDx && ordinatoreDx)
            numberEndDx = String(numberEndDx).padStart(ordinatoreDx.page_number_pad, '0');

        /* Inform parent that value has changed */
        dispatch('changeDx');
    }

    const handleConfirmLetterLogic = (e) => {
        const progressione = e.detail.progressione;
        progressioniListDx = e.detail.progressioniList;

        const lastScan = scansioniList[scansioniList.length - 1];
        const lastProgressioneIndexSx = lastScan.indice_progressione_sx;
        const lastProgressioneIndexDx = lastScan.indice_progressione_dx;
        const lastProgressioneSx = lastScan.progressione_sx;
        const lastProgressioneDx = lastScan.progressione_dx;

        /* Start new Progressione */
        if(progressione && lastProgressioneDx != progressione) {
            letterDx = progressioniListDx[0].letter;
            numberDx = progressioniListDx[0].number;
            $_nomenclaturaDx.progressioneIndex = 0;
            _progressioneDx.set(progressione);
            handleChangeDx();
            return;
        }

        /* Resume Progressione */
        progressioniListSx = progressioniListDx;
        const nextProgressioneIndexSx = lastProgressioneIndexSx != null ? lastProgressioneIndexSx + ordinatoreSx.page_increment : ordinatoreSx.page_increment - 1;
        const nextProgressioneIndexDx = lastProgressioneIndexDx + ordinatoreDx.page_increment;

        if(progressioniListSx[nextProgressioneIndexSx]){
            letterSx = progressioniListSx[nextProgressioneIndexSx].letter;
            numberSx = progressioniListSx[nextProgressioneIndexSx].number;
            $_nomenclaturaSx.progressioneIndex = nextProgressioneIndexSx;
            _progressioneSx.set(lastProgressioneSx);
            handleChangeSx();
        }

        if(progressioniListDx[nextProgressioneIndexDx]){
            letterDx = progressioniListDx[nextProgressioneIndexDx].letter;
            numberDx = progressioniListDx[nextProgressioneIndexDx].number;
            $_nomenclaturaDx.progressioneIndex = nextProgressioneIndexDx;
            _progressioneDx.set(lastProgressioneDx);
            handleChangeDx();
        }
    }

    const handleConfirmColumnNumberLogic = (e) => {
        const progressione = e.detail;

        const progressioneSplit = progressione.split(';');
        const colPerPage = progressioneSplit[0];
        const startingCol = progressioneSplit[1];
        
        numberDx = parseInt(startingCol);
        numberEndDx = parseInt(startingCol) + parseInt(colPerPage) - 1;
        _progressioneDx.set(progressione);
        handleChangeDx();
    }

    const handleConfirmColumnLetterLogic = (e) => {
        const progressione = e.detail;

        const progressioneSplit = progressione.split(';');
        const colPerPage = progressioneSplit[0];
        const startingCol = progressioneSplit[1];
        const alphabet = formatAlphabet(getAlphabetFromName(progressioneSplit[2]), startingCol);

        letterDx = startingCol;
        const letterDxIndex = alphabet.letters.findIndex(el => el == letterDx);
        letterEndDx = alphabet.letters[letterDxIndex + parseInt(colPerPage) - 1];
        _progressioneDx.set(progressione);
        handleChangeDx();
    }

    const buildProgressioneString = (ordinatore, progressione) => {
        let progressioneString = '';
        
        if(!ordinatore || !progressione)
            return '';

        const progressioneSplit = progressione.split(';');

        if(ordinatore?.page_number_type == 'letter'){
            const letterStart = progressioneSplit[0];
            const letterEnd = progressioneSplit[1];
            const numericProgression = progressioneSplit[2];

            progressioneString = `${letterStart}`;
            if(letterEnd)
                progressioneString += `-${letterEnd}`;
            if(numericProgression)
                progressioneString += ` ${numericProgression}`;
        }
        else if(ordinatore?.page_number_type?.includes('column')){
            const colPerPage = progressioneSplit[0];
            const startingCol = progressioneSplit[1];
            progressioneString = `num.col: ${colPerPage}, inizio:${String(startingCol).charAt(0)}`;
        }
        return(progressioneString);
    }
</script>

<LetterConfigurationDialog on:confirm={handleConfirmLetterLogic}/>
<ColumnNumberConfigurationDialog on:confirm={handleConfirmColumnNumberLogic}/>
<ColumnLetterConfigurationDialog on:confirm={handleConfirmColumnLetterLogic}/>

<div id="numero-pagina" class="flex-column" class:letter={ordinatoreSx?.page_number_type == 'letter' || ordinatoreDx?.page_number_type == 'letter'} class:column={ordinatoreSx?.page_number_type?.includes('column') || ordinatoreDx?.page_number_type?.includes('column')} class:prefix={ordinatoreSx?.page_number_type == '(prefix)_number' || ordinatoreDx?.page_number_type == '(prefix)_number'}>
    <!-- LABEL -->
    <div class="field-label">Numero</div>

    <!-- TOOLS SX -->
    <div class="field-tools">
        <Checkbox bind:checked={enabledSx} disabled/>

        {#if $_progressioneSx}
            <span class="warning">{buildProgressioneString(ordinatoreSx, $_progressioneSx)}</span>
        {/if}
    </div>

    <!-- SX -->
    {#if ordinatoreSx?.page_number_type == 'letter'}
        <div class="flex-row">
            <Textfield
                variant="outlined"
                noLabel
                type="text"
                disabled={!enabledSx}
                bind:value={letterSx}
                on:change={handleChangeSx}
                input$emptyValueUndefined
            />
            <Textfield
                variant="outlined"
                noLabel
                type="number"
                input$min="0"
                disabled={!enabledSx}
                bind:value={numberSx}
                on:change={handleChangeSx}
                input$emptyValueUndefined
            />
        </div>
    {:else if ordinatoreSx?.page_number_type == 'column_letter'}
        <div class="flex-row">
            <Textfield
                style="padding: 0 10px; width: 50px;"
                variant="outlined"
                noLabel
                type="text"
                disabled={!enabledSx}
                value="col."
                input$readonly
            />
            <Textfield
                style="padding: 0 10px; width: 85px;"
                variant="outlined"
                noLabel
                type="text"
                disabled={!enabledSx}
                bind:value={letterSx}
                on:change={handleChangeSx}
                input$emptyValueUndefined
            />
            <Textfield
                style="padding: 0 10px; width: 25px;"
                variant="outlined"
                noLabel
                type="text"
                disabled={!enabledSx}
                value="-"
                input$readonly
            />
            <Textfield
                style="padding: 0 10px; width: 85px;"
                variant="outlined"
                noLabel
                type="text"
                disabled={!enabledSx}
                bind:value={letterEndSx}
                on:change={handleChangeSx}
                input$emptyValueUndefined
            /> 
        </div>
    {:else if ordinatoreSx?.page_number_type == 'column_number'}
        <div class="flex-row">
            <Textfield
                style="padding: 0 10px; width: 50px;"
                variant="outlined"
                noLabel
                type="text"
                disabled={!enabledSx}
                value="col."
                input$readonly
            />
            <Textfield
                style="padding: 0 10px; width: 85px;"
                variant="outlined"
                noLabel
                type="number"
                input$min="0"
                disabled={!enabledSx}
                bind:value={numberSx}
                on:change={handleChangeSx}
                input$emptyValueUndefined
            />
            <Textfield
                style="padding: 0 10px; width: 25px;"
                variant="outlined"
                noLabel
                type="text"
                disabled={!enabledSx}
                value="-"
                input$readonly
            />
            <Textfield
                style="padding: 0 10px; width: 85px;"
                variant="outlined"
                noLabel
                type="number"
                input$min={numberSx + 1}
                disabled={!enabledSx}
                bind:value={numberEndSx}
                on:change={handleChangeSx}
                input$emptyValueUndefined
            />
        </div>
    {:else if ordinatoreSx?.page_number_type == 'text'}
        <Textfield
            style="padding: 0 10px; width: 85px;"
            variant="outlined"
            noLabel
            type="text"
            disabled={!enabledSx}
            bind:value={letterSx}
            on:change={handleChangeSx}
            input$emptyValueUndefined
        />
    {:else if ordinatoreSx?.page_number_type == '(prefix)_number'}
        <div class="flex-row">
            <Textfield
                style="padding: 0 10px; width: 85px;"
                variant="outlined"
                noLabel
                type="text"
                disabled={!enabledSx}
                bind:value={numberPrefixSx}
                on:change={handleChangeSx}
                input$emptyValueUndefined
            />
            <Textfield
                style="padding: 0 10px; width: 85px;"
                variant="outlined"
                noLabel
                type="number"
                input$min="0"
                disabled={!enabledSx}
                bind:value={numberSx}
                on:change={handleChangeSx}
                input$emptyValueUndefined
            />
        </div>
    {:else}
        <Textfield
            variant="outlined"
            noLabel
            type="number"
            input$min="0"
            disabled={!enabledSx}
            bind:value={numberSx}
            on:change={handleChangeSx}
            input$emptyValueUndefined
        />
    {/if}

    <!-- TOOLS DX -->
    <div class="field-tools">
        <Checkbox bind:checked={enabledDx} disabled/>

        {#if $_progressioneDx}
            <span class="warning">{buildProgressioneString(ordinatoreDx, $_progressioneDx)}</span>
        {/if}
    </div>

    <!-- DX -->
    {#if ordinatoreDx?.page_number_type == 'letter'}
        <div class="flex-row">
            <Textfield
                variant="outlined"
                noLabel
                type="text"
                disabled={!enabledDx}
                bind:value={letterDx}
                on:change={handleChangeDx}
                input$emptyValueUndefined
            />
            <Textfield
                variant="outlined"
                noLabel
                type="number"
                input$min="0"
                disabled={!enabledDx}
                bind:value={numberDx}
                on:change={handleChangeDx}
                input$emptyValueUndefined
            />
        </div>
    {:else if ordinatoreDx?.page_number_type == 'column_letter'}
        <div class="flex-row">
            <Textfield
                style="padding: 0 10px; width: 50px;"
                variant="outlined"
                noLabel
                type="text"
                disabled={!enabledSx}
                value="col."
                input$readonly
            />
            <Textfield
                style="padding: 0 10px; width: 85px;"
                variant="outlined"
                noLabel
                type="text"
                disabled={!enabledDx}
                bind:value={letterDx}
                on:change={handleChangeDx}
                input$emptyValueUndefined
            />
            <Textfield
                style="padding: 0 10px; width: 25px;"
                variant="outlined"
                noLabel
                type="text"
                disabled={!enabledSx}
                value="-"
                input$readonly
            />
            <Textfield
                style="padding: 0 10px; width: 85px;"
                variant="outlined"
                noLabel
                type="text"
                disabled={!enabledDx}
                bind:value={letterEndDx}
                on:change={handleChangeDx}
                input$emptyValueUndefined
            /> 
        </div>
    {:else if ordinatoreDx?.page_number_type == 'column_number'}
        <div class="flex-row">
            <Textfield
                style="padding: 0 10px; width: 50px;"
                variant="outlined"
                noLabel
                type="text"
                disabled={!enabledSx}
                value="col."
                input$readonly
            />
            <Textfield
                style="padding: 0 10px; width: 85px;"
                variant="outlined"
                noLabel
                type="number"
                input$min="0"
                disabled={!enabledDx}
                bind:value={numberDx}
                on:change={handleChangeDx}
                input$emptyValueUndefined
            />
            <Textfield
                style="padding: 0 10px; width: 25px;"
                variant="outlined"
                noLabel
                type="text"
                disabled={!enabledSx}
                value="-"
                input$readonly
            />
            <Textfield
                style="padding: 0 10px; width: 85px;"
                variant="outlined"
                noLabel
                type="number"
                input$min={numberDx + 1}
                disabled={!enabledDx}
                bind:value={numberEndDx}
                on:change={handleChangeDx}
                input$emptyValueUndefined
            />
        </div>
    {:else if ordinatoreSx?.page_number_type == 'text'}
        <Textfield
            style="padding: 0 10px; width: 85px;"
            variant="outlined"
            noLabel
            type="text"
            disabled={!enabledDx}
            bind:value={letterDx}
            on:change={handleChangeDx}
            input$emptyValueUndefined
        />
    {:else if ordinatoreDx?.page_number_type == '(prefix)_number'}
        <div class="flex-row">
            <Textfield
                style="padding: 0 10px; width: 85px;"
                variant="outlined"
                noLabel
                type="text"
                disabled={!enabledDx}
                bind:value={numberPrefixDx}
                on:change={handleChangeDx}
                input$emptyValueUndefined
            />
            <Textfield
                style="padding: 0 10px; width: 85px;"
                variant="outlined"
                noLabel
                type="number"
                input$min="0"
                disabled={!enabledDx}
                bind:value={numberDx}
                on:change={handleChangeDx}
                input$emptyValueUndefined
            />
        </div>
    {:else}
        <Textfield
            variant="outlined"
            noLabel
            type="number"
            input$min="0"
            disabled={!enabledDx}
            bind:value={numberDx}
            on:change={handleChangeDx}
            input$emptyValueUndefined
        />
    {/if}
</div>

<style>
    #numero-pagina {
        width: 85px;
    }

    #numero-pagina.prefix {
        width: 170px;
    }

    #numero-pagina.letter {
        width: 170px;
    }

    #numero-pagina.column {
        width: 245px;
    }

    .flex-column {
        display: flex;
        flex-direction: column;
    }

    .flex-row {
        display: flex;
    }

    .field-label {
        border: 1px solid var(--mdc-theme-secondary);
        background-color: var(--mdc-theme-background);
        color: var(--mdc-theme-secondary);
        display: flex;
        align-items: center;
        justify-content: center;
        height: 30px;
        padding: 0 5px;
    }

    .field-tools {
        border: 1px solid var(--mdc-theme-secondary);
        background-color: var(--mdc-theme-background);
        display: flex;
        align-items: center;
        justify-content: center;
        height: 30px;
    }

    .warning {
        color: var(--mdc-theme-warning);
    }

    /* Textfield */
    * :global(.mdc-text-field) {
        height: 40px;
    }

    * :global(.mdc-text-field--disabled) {
        pointer-events: none;
        background-image: url('/diagonal-stripes.svg');
        width: auto !important;
        border-right: 1px solid var(--mdc-theme-secondary);
    }

    * :global(.mdc-text-field:not(.mdc-text-field--disabled) .mdc-text-field__affix) {
        color: var(--mdc-theme-primary);
        font-weight: bold;
    }

    * :global(.mdc-text-field--disabled .mdc-text-field__affix) {
        display: none;
    }

    * :global(.mdc-text-field:not(.mdc-text-field--disabled) .mdc-text-field__input) {
        color: var(--mdc-theme-primary);
        font-weight: bold;
    }

    * :global(.mdc-text-field--disabled .mdc-text-field__input) {
        display: none;
    }

    * :global(.mdc-notched-outline__leading) {
        border-radius: 0px !important;
    }

    * :global(.mdc-notched-outline__trailing) {
        border-radius: 0px !important;
    }

    /* Checkbox */
    * :global(.mdc-checkbox) {
        transform: scale(0.8);
    }

    * :global(.mdc-checkbox--disabled) {
        pointer-events: auto;
        cursor: not-allowed;
    }
    
    * :global(.mdc-checkbox--disabled .mdc-checkbox__background) {
        border-color: var(--mdc-theme-disabled) !important;
    } 
    
    * :global(.mdc-checkbox--selected.mdc-checkbox--disabled .mdc-checkbox__background) {
        background-color: var(--mdc-theme-primary-hover) !important;
    }

    * :global(.mdc-checkbox__ripple) {
        top: 3px;
        left: 4px;
    }
</style>