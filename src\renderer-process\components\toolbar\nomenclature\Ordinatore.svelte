<script>
    import { _nomenclaturaSx, _nomenclaturaDx,_selectedJob, _isDoublePageEnabled, _acquisitionMode } from '@store';
    import { createEventDispatcher } from 'svelte';
    import * as d3 from "d3-selection";
    import { onMount } from "svelte";

    //smui
    import Select, { Option } from '@smui/select';
    import Checkbox from '@smui/checkbox';

    const dispatch = createEventDispatcher();
    export let scansioniList = [];
    let ordinatoriList = [];
    let valueSx, valueDx, enabledSx = false, enabledDx = false;

    onMount(async () => {
        /* get ordinatori */
        ordinatoriList =  await window.electron.database('db-get-ordinatori');

        if($_selectedJob.is_stampato)
            ordinatoriList = ordinatoriList.filter((ordinatore) => ordinatore.category.includes('stampati'));
        else ordinatoriList = ordinatoriList.filter((ordinatore) => ordinatore.category.includes('manoscritti'));

        /* Get scansioni */
        scansioniList = await window.electron.database('db-get-scansioni', { idDigit: $_selectedJob.id_digit });

        /* initialize */
        enabledSx = true;
        valueSx = initValueSx();
        initValueDx();

        createEventListeners();
    })

    const createEventListeners = () => {
        d3.select('#ordinatore').on('scan-completed', (e) => {
            /* Update ordinatore Sx if needed */
            valueSx = initValueSx();
            
            /* Update ordinatore Dx if needed */
            if(valueDx){
                const nextOrdinatoreDx = ordinatoriList.find((ord) => ord.metis_code == valueDx.next_ordinatore);
                if(nextOrdinatoreDx.metis_code != valueDx.metis_code)
                    valueDx = structuredClone(nextOrdinatoreDx);
                    /* handleChangeDx will fire automatically now */
            }

            d3.select('#numero-pagina').dispatch('scan-completed', {detail: {ordinatoreSx: valueSx, ordinatoreDx: valueDx}});
            d3.select('#eccezione').dispatch('scan-completed', {detail: {ordinatoreSx: valueSx, ordinatoreDx: valueDx}});
        });

        d3.select('#ordinatore').on('switch-to-single-page', (e) => {
            enabledDx = false;
            valueDx = null;
            /* handleChangeDx will fire automatically now */            

            /* Set store variable and dispatch event to inform main viewer */
            _isDoublePageEnabled.set(false);
            d3.select('#main-viewer').dispatch('switch-to-single-page');
        });

        d3.select('#ordinatore').on('clear-ordinatore-sx', (e) => {            
            valueSx = null;
        });
    }

    const initValueSx = () => {
        let ordinatoreToSet;

        if(scansioniList.length > 0){
            /* Select next odinatore basing on last scan */
            const lastScan = scansioniList[scansioniList.length - 1];
            const lastOrdinatoreCode = lastScan.pagina_doppia ? lastScan.codice_ordinatore_dx : lastScan.codice_ordinatore_sx;
            const lastOrdinatore = ordinatoriList.find((ord) => ord.metis_code == lastOrdinatoreCode);
            ordinatoreToSet = ordinatoriList.find((ord) => ord.metis_code == lastOrdinatore.next_ordinatore);
        } else {
            /* Select Colorchecker */
            ordinatoreToSet = ordinatoriList.find((ord) => ord.metis_code == 1);
        }
        
        return structuredClone(ordinatoreToSet);
    }

    const initValueDx = () => {
        /* If last scan is pagina doppia, enable dx and set value */
        const lastScan = scansioniList[scansioniList.length - 1];

        /* Set local values */
        enabledDx = lastScan?.pagina_doppia || false;
        valueDx = enabledDx ? structuredClone(valueSx) : null;

        /* Set store variable and dispatch event to inform main viewer */
        _isDoublePageEnabled.set(enabledDx);
        d3.select('#main-viewer').dispatch(enabledDx ? 'switch-to-double-page' : 'switch-to-single-page', {detail: {isInit: true}});
    }

    const handleChangeSx = () => {
        /* Update nomenclatura */
        $_nomenclaturaSx.ordinatore = structuredClone(valueSx);

        /* Show name_short in textfield */
        const ordinatoreDiv = d3.select('#ordinatore');    
        const selectElement = ordinatoreDiv.selectAll('.mdc-select__selected-text').nodes()[0];
        d3.select(selectElement).html(valueSx?.name_short || null);

        /* Inform parent that value has changed */
        dispatch('changeSx');

        /* Inform other components that value has changed */
        d3.select('#interni-pagina').dispatch('ordinatore-sx-changed', {detail: {value: valueSx}});
        d3.select('#numero-pagina').dispatch('ordinatore-sx-changed', {detail: {value: valueSx}});
        d3.select('#tipo-pagina').dispatch('ordinatore-sx-changed', {detail: {value: valueSx}});
        d3.select('#eccezione').dispatch('ordinatore-sx-changed', {detail: {value: valueSx}});

        /* check if the selected valueSx is enabled for double page */
        if(enabledDx && !valueSx.book_mode_enabled) {
            enabledDx = false;
            valueDx = null;
            /* handleChangeDx will fire automatically now */            

            /* Set store variable and dispatch event to inform main viewer */
            _isDoublePageEnabled.set(false);
            d3.select('#main-viewer').dispatch('switch-to-single-page');
        }
    }

    const handleChangeDx = () => {
        /* Update nomenclatura */
        $_nomenclaturaDx.ordinatore = structuredClone(valueDx);

        /* Show name_short in textfield */
        const ordinatoreDiv = d3.select('#ordinatore');
        const selectElement = ordinatoreDiv.selectAll('.mdc-select__selected-text').nodes()[1];
        d3.select(selectElement).html(valueDx?.name_short || null);
        
        /* Inform parent that value has changed */
        dispatch('changeDx');

        /* Inform other components that value has changed */
        d3.select('#interni-pagina').dispatch('ordinatore-dx-changed', {detail: {value: valueDx}});
        d3.select('#numero-pagina').dispatch('ordinatore-dx-changed', {detail: {value: valueDx}});
        d3.select('#tipo-pagina').dispatch('ordinatore-dx-changed', {detail: {value: valueDx}});
        d3.select('#eccezione').dispatch('ordinatore-dx-changed', {detail: {value: valueDx}});
    }

    const handleEnableDx = () => {
        valueDx = enabledDx && valueSx.page_number_type != 'letter' ? structuredClone(valueSx) : null;
        /* handleChangeDx will fire automatically now */

        /* Set store variable and dispatch event to inform main viewer */
        _isDoublePageEnabled.set(enabledDx);
        d3.select('#main-viewer').dispatch(enabledDx ? 'switch-to-double-page' : 'switch-to-single-page');
    }
</script>

<div id="ordinatore" class="flex-column">
    <!-- LABEL -->
    <div class="field-label">Ordinatore</div>

    <!-- TOOLS SX -->
    <div class="field-tools">
        <Checkbox bind:checked={enabledSx} disabled />
    </div>

    <!-- SX -->
    <Select
    variant="outlined"
    noLabel
    bind:value={valueSx}
    key={(ordinatore) => `${ordinatore ? ordinatore.metis_code : ''}`}
    on:SMUISelect:change={handleChangeSx}
    >
        {#each ordinatoriList as ordinatore}
            <Option value={ordinatore}>{ordinatore.name_long}</Option>
        {/each}
    </Select>

    <!-- TOOLS DX -->
    <div class="field-tools">
        <Checkbox bind:checked={enabledDx} on:change={handleEnableDx} disabled={!valueSx?.book_mode_enabled || $_acquisitionMode != 'normal'}/>
    </div>

    <!-- DX -->
    <Select
    variant="outlined"
    noLabel
    disabled={!enabledDx}
    bind:value={valueDx}
    key={(ordinatore) => `${ordinatore ? ordinatore.metis_code : ''}`}
    on:SMUISelect:change={handleChangeDx}
    >
        {#each ordinatoriList as ordinatore}
            <Option value={ordinatore}>{ordinatore.name_long}</Option>
        {/each}
    </Select>
</div>

<style>
    .flex-column {
        display: flex;
        flex-direction: column;
    }

    .field-label {
        border: 1px solid var(--mdc-theme-secondary);
        background-color: var(--mdc-theme-background);
        color: var(--mdc-theme-secondary);
        display: flex;
        align-items: center;
        justify-content: center;
        height: 30px;
        padding: 0 5px;
    }

    .field-tools {
        border: 1px solid var(--mdc-theme-secondary);
        background-color: var(--mdc-theme-background);
        display: flex;
        align-items: center;
        justify-content: center;
        height: 30px;
    }

    /* Select */
    * :global(.mdc-select__menu.mdc-menu.mdc-menu-surface.mdc-menu-surface--open.mdc-menu-surface--fullwidth){
        width: max-content;
    }

    * :global(.mdc-select__anchor){
        min-width: 100px;
        width: fit-content;
        padding: 0px 16px;
        height: 40px;
    }

    * :global(.mdc-select:not(.mdc-select--disabled) .mdc-select__selected-text){
        color: var(--mdc-theme-primary);
        font-weight: bold;
    }

    * :global(.mdc-select--disabled) {
        cursor: default;
        pointer-events: none;
        background-image: url('/diagonal-stripes.svg');
        border: 1px solid var(--mdc-theme-secondary);
        height: 38px;
    }

    * :global(.mdc-select__dropdown-icon){
        width: 0;
        margin: 0;
    }

    * :global(.mdc-notched-outline__leading) {
        border-radius: 0px !important;
    }

    * :global(.mdc-notched-outline__trailing) {
        border-radius: 0px !important;
    }

    /* Select scrollbar */
    * :global(.mdc-select__menu::-webkit-scrollbar) {
        width: 10px;
    }

    * :global(.mdc-select__menu::-webkit-scrollbar-track) {
        background-color: white;
        border-radius: 10px;
    }

    * :global(.mdc-select__menu::-webkit-scrollbar-thumb) {
        background-color: var(--mdc-theme-primary);
        border-radius: 10px;
    }

    /* Checkbox */
    * :global(.mdc-checkbox) {
        transform: scale(0.8);
    }

    * :global(.mdc-checkbox--disabled) {
        pointer-events: auto;
        cursor: not-allowed;
    }
    
    * :global(.mdc-checkbox--disabled .mdc-checkbox__background) {
        border-color: var(--mdc-theme-disabled) !important;
    }

    * :global(.mdc-checkbox--selected.mdc-checkbox--disabled .mdc-checkbox__background) {
        background-color: var(--mdc-theme-primary-hover) !important;
    }

    * :global(.mdc-checkbox__ripple) {
        top: 3px;
        left: 4px;
    }
</style>