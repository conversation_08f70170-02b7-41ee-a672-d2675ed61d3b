<script>
    import { _isEditingScan, _user, _scannerId, _nomenclaturaSxOT, _nomenclaturaDxOT, _isDoublePageEnabledOT, _isEbraicOrderOT } from '@store';
    import NomenclatureOT from '@components/toolbar/nomenclature/edit-scan/NomenclatureOT.svelte';
    import { notify } from '@utility';
    import * as d3 from 'd3-selection';

    //smui
    import Paper, { Title, Content } from '@smui/paper';
    import Button, { Label } from '@smui/button';

    export let isFromPreview = false;
    export let selectedScan;

    const handleConfirm = async () => {        
        if(!$_nomenclaturaSxOT.ordinatore || (!$_nomenclaturaDxOT.ordinatore && $_isDoublePageEnabledOT)) {
            notify.warning("Specificare gli ordinatori per rinominare la scansione");
            return;
        }

        /* rename scan */
        await renameScan(); 

        /* Refresh scan table */
        if(isFromPreview)
            d3.select('#preview-visualizer').dispatch('refresh-scan-table', {detail: {scanId: selectedScan.id}});
        else
            d3.select('#scan-visualizer-table').dispatch('refresh-scan-table', {detail: {scanId: selectedScan.id}});

        /* Close form */ 
        handleClose();
    }

    const renameScan = async () => {
        const paramsArr = [];

        paramsArr.push($_isDoublePageEnabledOT);
        paramsArr.push($_isEbraicOrderOT);
        paramsArr.push($_nomenclaturaSxOT.ordinatore.metis_code);
        paramsArr.push($_nomenclaturaSxOT.ordinatore.name_short);
        paramsArr.push($_nomenclaturaSxOT.progressioneIndex || null);
        paramsArr.push($_nomenclaturaSxOT.letteraPagina || '');
        paramsArr.push($_nomenclaturaSxOT.letteraPaginaEnd || '');
        paramsArr.push($_nomenclaturaSxOT.prefissoNumeroPagina || '');
        paramsArr.push($_nomenclaturaSxOT.numeroPagina || 0);
        paramsArr.push($_nomenclaturaSxOT.numeroPaginaEnd || 0);
        paramsArr.push($_nomenclaturaSxOT.internoPagina || 0);
        paramsArr.push($_nomenclaturaSxOT.tipoPagina);
        paramsArr.push($_nomenclaturaSxOT.eccezione ? $_nomenclaturaSxOT.eccezione.metis_code : null);
        paramsArr.push($_nomenclaturaSxOT.eccezione ? $_nomenclaturaSxOT.eccezione.name_short : null);
        paramsArr.push($_nomenclaturaSxOT.eccezione ? $_nomenclaturaSxOT.eccezione.valore : null);
        paramsArr.push($_nomenclaturaSxOT.eccezione ? $_nomenclaturaSxOT.eccezione.parte : null);
        paramsArr.push(buildDisplayName($_nomenclaturaSxOT));
        paramsArr.push($_isDoublePageEnabledOT ? $_nomenclaturaDxOT.ordinatore?.metis_code : null);
        paramsArr.push($_isDoublePageEnabledOT ? $_nomenclaturaSxOT.ordinatore?.name_short : null);
        paramsArr.push($_isDoublePageEnabledOT ? $_nomenclaturaDxOT.progressioneIndex || null : null);
        paramsArr.push($_isDoublePageEnabledOT ? $_nomenclaturaDxOT.letteraPagina || '' : null);
        paramsArr.push($_isDoublePageEnabledOT ? $_nomenclaturaDxOT.letteraPaginaEnd || '' : null);
        paramsArr.push($_isDoublePageEnabledOT ? $_nomenclaturaDxOT.prefissoNumeroPagina || '' : null);
        paramsArr.push($_isDoublePageEnabledOT ? $_nomenclaturaDxOT.numeroPagina || 0 : null);
        paramsArr.push($_isDoublePageEnabledOT ? $_nomenclaturaDxOT.numeroPaginaEnd || 0 : null);
        paramsArr.push($_isDoublePageEnabledOT ? $_nomenclaturaDxOT.internoPagina || 0 : null);
        paramsArr.push($_isDoublePageEnabledOT ? $_nomenclaturaDxOT.tipoPagina : null);
        paramsArr.push($_isDoublePageEnabledOT && $_nomenclaturaDxOT.eccezione ? $_nomenclaturaDxOT.eccezione.metis_code : null);
        paramsArr.push($_isDoublePageEnabledOT && $_nomenclaturaDxOT.eccezione ? $_nomenclaturaDxOT.eccezione.name_short : null);
        paramsArr.push($_isDoublePageEnabledOT && $_nomenclaturaDxOT.eccezione ? $_nomenclaturaDxOT.eccezione.valore : null);
        paramsArr.push($_isDoublePageEnabledOT && $_nomenclaturaDxOT.eccezione ? $_nomenclaturaDxOT.eccezione.parte : null);
        paramsArr.push($_isDoublePageEnabledOT ? buildDisplayName($_nomenclaturaDxOT) : '');
        paramsArr.push(selectedScan.crop_area_width);
        paramsArr.push(selectedScan.crop_area_height);
        paramsArr.push(selectedScan.id);

        await window.electron.database('db-log', {level:'INFO', text:'[EditScanForm.svelte][renameScan] Renaming scan with id: ' + selectedScan.id});
        const response = await window.electron.database("db-update-scansione", paramsArr);
              
        if(response.changedRows > 0) {
            await window.electron.database('db-log', {level:'INFO', text:'[EditScanForm.svelte][renameScan] Scan renamed successfully'});
            notify.success("Scansione rinominata con successo");
            
            /* Log Event */
            let eventText = '';
            eventText += `[${new Date().toLocaleString('it-IT').replace(',', '')}]\n`;
            eventText += `Evento: Rinominazione Immagine\n`;
            eventText += `Gruppo: ${$_user.company} - Operatore: ${$_user.displayName || $_user.username} - Livello: ${$_user.profile}\n`;
            eventText += `ID : ${$_scannerId}\n`;
            eventText += `Indice acquisizione modificato #${selectedScan.sequenziale_scansione}\n`;
            eventText += $_isDoublePageEnabledOT ? `${buildDisplayName($_nomenclaturaSxOT)}, ${buildDisplayName($_nomenclaturaDxOT)} (era: ${selectedScan.display_name_sx}, ${selectedScan.display_name_dx} )\n\n` : `${buildDisplayName($_nomenclaturaSxOT)} (era: ${selectedScan.display_name_sx} )\n\n`;
            await window.electron.database('db-log', {level:'EVENT', text: eventText, eventType: 'Rinominazione Immagine'});
        } else {
            await window.electron.database('db-log', {level:'ERROR', text:'[EditScanForm.svelte][renameScan] Error during scan renaming'});
            notify.error("Errore durante la rinomina della scansione #" + selectedScan.sequenziale_scansione);
        }
    }

    const buildDisplayName = (nomenclatura) => {
        if(!nomenclatura?.ordinatore) return '';

        let displayName = '';

        /* INTERNO */
        if(nomenclatura.internoPagina > 0)
            displayName = `-sub.${String(nomenclatura.internoPagina).padStart(4, '0')}_`;
        
        /* ORDINATORE */
        displayName += nomenclatura.ordinatore.name_short;

        /* PAGE NUMBER */
        /* Prefix params */
        if(nomenclatura.ordinatore.page_number_prefix){
            if(!nomenclatura.ordinatore.page_number_prefix.includes('$'))
                displayName += `${nomenclatura.ordinatore.page_number_prefix}`;
            else
                displayName += `${nomenclatura.ordinatore.page_number_prefix.replace('$', nomenclatura.prefissoNumeroPagina)}`;
        }
            
        /* Value */
        switch (nomenclatura.ordinatore.page_number_type) {
            case 'number':
            case '(prefix)_number':
                if(nomenclatura.ordinatore.name_short == 'nf' || nomenclatura.ordinatore.name_short == 'np')
                    displayName += `[${String(nomenclatura.numeroPagina)}${nomenclatura.tipoPagina?.charAt(0) || ''}]`;
                else if(nomenclatura.ordinatore.roman_number) 
                    displayName += `${romanize(nomenclatura.numeroPagina)}`;
                else
                    displayName += `${String(nomenclatura.numeroPagina).padStart(nomenclatura.ordinatore.page_number_pad, '0')}`;
                break;
            case 'text':
                    displayName += `${String(nomenclatura.letteraPagina)}`;
                break;
            case 'letter':
                displayName += `${nomenclatura.letteraPagina == 'pi' ? 'pi.greco' : nomenclatura.letteraPagina}${nomenclatura.numeroPagina ? String(nomenclatura.numeroPagina).padStart(nomenclatura.ordinatore.page_number_pad, '0') : ''}`;
                break;
            case 'column_number':
                if(nomenclatura.ordinatore.roman_number)
                    displayName += `${romanize(nomenclatura.numeroPagina)}-${romanize(nomenclatura.numeroPaginaEnd)}`;
                else
                    displayName += `${String(nomenclatura.numeroPagina).padStart(nomenclatura.ordinatore.page_number_pad, '0')}-${String(nomenclatura.numeroPaginaEnd).padStart(nomenclatura.ordinatore.page_number_pad, '0')}`;
                break;
            case 'column_letter':
                displayName += `${nomenclatura.letteraPagina == 'pi' ? 'pi.greco' : nomenclatura.letteraPagina}-${nomenclatura.letteraPaginaEnd}`;
                break;
            default:
                break;
        }

        /* PAGE TYPE */
        if(nomenclatura.ordinatore.page_type_enabled && nomenclatura.ordinatore.name_short != 'nf')
            displayName += nomenclatura.tipoPagina?.charAt(0) || '';

        /* ECCEZIONE */
        if(nomenclatura.eccezione){
            /* Valore */
            displayName += `.[${String(nomenclatura.eccezione.valore).padStart(2, '0')}`;

            /* Codice */
            displayName += `.${nomenclatura.eccezione.name_short}`;

            /* Parte */
            if(nomenclatura.eccezione.parte == 0 || nomenclatura.eccezione.parte_as_number)
                displayName += `.${String(nomenclatura.eccezione.parte).padStart(4, '0')}]`;
            else
                displayName += `.${String.fromCharCode(nomenclatura.eccezione.parte + 96).padEnd(4, '0')}]`;
        }

        return displayName;
    }

    const handleClose = () => {
        if(isFromPreview)
            d3.select('#preview-visualizer-table-tools').dispatch('unselect-tool', {detail: {name: 'Rinomina'}});
        else
            d3.select('#scan-visualizer-table-tools').dispatch('unselect-tool', {detail: {name: 'Rinomina'}});

        _isEditingScan.set(false);
    }
</script>

<div class="container" class:isFromPreview>
    <div class="paper-container" class:isFromPreview>
        <Paper elevation=3>
            <Title>Rinomina Scansione</Title>
            <Content>
                <div class="content-container">
                    <div class="control-column">
                        <p>Definire di seguito la nomenclatura da utilizzare</p>
                        <NomenclatureOT {selectedScan}/>
                    </div>
                    <div class="buttons-container">
                        <Button on:click={handleConfirm} variant="raised">
                            <Label>Conferma</Label>
                        </Button>
                        <Button on:click={handleClose} variant="raised" color="secondary">
                            <Label>Chiudi</Label>
                        </Button>
                    </div>
                </div>
            </Content>
        </Paper>
    </div>
</div>

<style>
    .container {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 50%;
    }

    .container.isFromPreview {
        width: 100%;
    }

    .paper-container {
        min-width: 75%;
    }

    .paper-container.isFromPreview {
        min-width: 50%;
    }

    .content-container {
        display: flex;
        flex-direction: column;
        gap: 50px;
    }

    .control-column {
        display: flex;
        flex-direction: column;
        align-self: center;
        align-items: center;
    }

    .buttons-container {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 10px;
    }

    * :global(.mdc-form-field) {
        color: var(--mdc-theme-secondary);
    }

    * :global(.smui-paper) {
        padding: 20px;
    }
</style>