<script>
    import { _nomenclaturaDxOT, _nomenclaturaSxOT, _selectedJob } from "@store";
    import NomenclatureToolsOT from "@components/toolbar/nomenclature/edit-scan/NomenclatureToolsOT.svelte";
    import InternoPaginaOT from "@components/toolbar/nomenclature/edit-scan/InternoPaginaOT.svelte";
    import OrdinatoreOT from "@components/toolbar/nomenclature/edit-scan/OrdinatoreOT.svelte";
    import NumeroPaginaOT from "@components/toolbar/nomenclature/edit-scan/NumeroPaginaOT.svelte";
    import TipoPaginaOT from "@components/toolbar/nomenclature/edit-scan/TipoPaginaOT.svelte";
    import EccezioneOT from "@components/toolbar/nomenclature/edit-scan/EccezioneOT.svelte";
    import { onMount } from "svelte";

    export let selectedScan;
    let componentDidMount, scansioniList = [];

    onMount(async () => {
        componentDidMount = false;

        /* Get scansioni */
        await getScansioni();

        /* Init module Nomenclature with values from the selected scan */
        initNomenclature();

        componentDidMount = true;
    })

    const getScansioni = async () => {
        scansioniList = await window.electron.database('db-get-scansioni', { idDigit: $_selectedJob.id_digit });
    }

    const initNomenclature = () => {
        _nomenclaturaSxOT.set({});
        _nomenclaturaDxOT.set({});
    }

    const updateDisplayName = (side) => {
        if(side == 'sx')
            $_nomenclaturaSxOT.displayName = buildDisplayName($_nomenclaturaSxOT);
        else if($_nomenclaturaDxOT?.ordinatore)
            $_nomenclaturaDxOT.displayName = buildDisplayName($_nomenclaturaDxOT);
        else _nomenclaturaDxOT.set({});
    }

    const buildDisplayName = (nomenclatura) => {
        if(!nomenclatura?.ordinatore) return '';

        let displayName = '';

        /* INTERNO */
        if(nomenclatura.internoPagina > 0)
            displayName = `-sub.${String(nomenclatura.internoPagina).padStart(4, '0')}_`;
        
        /* ORDINATORE */
        displayName += nomenclatura.ordinatore.name_short;

        /* PAGE NUMBER */
        /* Prefix params */
        if(nomenclatura.ordinatore.page_number_prefix){
            if(!nomenclatura.ordinatore.page_number_prefix.includes('$'))
                displayName += `${nomenclatura.ordinatore.page_number_prefix}`;
            else
                displayName += `${nomenclatura.ordinatore.page_number_prefix.replace('$', nomenclatura.prefissoNumeroPagina)}`;
        }
        /* Value */
        switch (nomenclatura.ordinatore.page_number_type) {
            case 'number':
            case '(prefix)_number':
                if(nomenclatura.ordinatore.name_short == 'nf' || nomenclatura.ordinatore.name_short == 'np')
                    displayName += `[${String(nomenclatura.numeroPagina)}${nomenclatura.tipoPagina?.charAt(0) || ''}]`;
                else if(nomenclatura.ordinatore.roman_number) 
                    displayName += `${romanize(nomenclatura.numeroPagina)}`;
                else
                    displayName += `${String(nomenclatura.numeroPagina).padStart(nomenclatura.ordinatore.page_number_pad, '0')}`;
                break;
            case 'text':
                    displayName += `${String(nomenclatura.letteraPagina)}`;
                break;
            case 'letter':
                displayName += `${nomenclatura.letteraPagina == 'pi' ? 'pi.greco' : nomenclatura.letteraPagina}${Number.isFinite(Number(nomenclatura.letteraPagina)) ? '.' : ''}${nomenclatura.numeroPagina ? String(nomenclatura.numeroPagina).padStart(nomenclatura.ordinatore.page_number_pad, '0') : ''}`;
                break;
            case 'column_number':
                if(nomenclatura.ordinatore.roman_number)
                    displayName += `${romanize(nomenclatura.numeroPagina)}-${romanize(nomenclatura.numeroPaginaEnd)}`;
                else
                    displayName += `${String(nomenclatura.numeroPagina).padStart(nomenclatura.ordinatore.page_number_pad, '0')}-${String(nomenclatura.numeroPaginaEnd).padStart(nomenclatura.ordinatore.page_number_pad, '0')}`;
                break;
            case 'column_letter':
                displayName += `${nomenclatura.letteraPagina == 'pi' ? 'pi.greco' : nomenclatura.letteraPagina}-${nomenclatura.letteraPaginaEnd}`;
                break;
            default:
                break;
        }

        /* PAGE TYPE */
        if(nomenclatura.ordinatore.page_type_enabled && nomenclatura.ordinatore.name_short != 'nf')
            displayName += nomenclatura.tipoPagina?.charAt(0) || '';

        /* ECCEZIONE */
        if(nomenclatura.eccezione){
            /* Valore */
            displayName += `.[${String(nomenclatura.eccezione.valore).padStart(2, '0')}`;

            /* Codice */
            displayName += `.${nomenclatura.eccezione.name_short}`;

            /* Parte */
            if(nomenclatura.eccezione.parte == 0 || nomenclatura.eccezione.parte_as_number)
                displayName += `.${String(nomenclatura.eccezione.parte).padStart(4, '0')}]`;
            else
                displayName += `.${String.fromCharCode(nomenclatura.eccezione.parte + 96).padEnd(4, '0')}]`;
        }

        return displayName;
    }
</script>

<div id="nomenclature-ot" class="flex-row" >
    <!-- TABLE -->
    {#if componentDidMount}
        <div class="flex-row">
            <!-- TOOLS -->
            <NomenclatureToolsOT />
            <!-- INTERNI -->
            <InternoPaginaOT {selectedScan} on:changeSx={() => updateDisplayName('sx')} on:changeDx={() => updateDisplayName('dx')}/>
            <!-- ORDINATORE -->
            <OrdinatoreOT {selectedScan} on:changeSx={() => updateDisplayName('sx')} on:changeDx={() => updateDisplayName('dx')}/>
            <!-- NUMERO PAGINA -->
            <NumeroPaginaOT {selectedScan} on:changeSx={() => updateDisplayName('sx')} on:changeDx={() => updateDisplayName('dx')}/>
            <!-- TIPO PAGINA -->
            <TipoPaginaOT {selectedScan} on:changeSx={() => updateDisplayName('sx')} on:changeDx={() => updateDisplayName('dx')}/>
            <!-- ECCEZIONE -->
            <EccezioneOT {selectedScan} on:changeSx={() => updateDisplayName('sx')} on:changeDx={() => updateDisplayName('dx')}/>
        </div>
    {/if}
</div>

<style>
    .flex-row {
        display: flex;
        justify-content: space-evenly;
    }
</style>