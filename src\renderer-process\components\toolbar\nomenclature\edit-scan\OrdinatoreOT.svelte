<script>
    import { _nomenclaturaSxOT, _nomenclaturaDxOT,_selectedJob, _isDoublePageEnabledOT } from '@store';
    import { delay } from '@utility';
    import { createEventDispatcher } from 'svelte';
    import * as d3 from "d3-selection";
    import { onMount } from "svelte";

    //smui
    import Select, { Option } from '@smui/select';
    import Checkbox from '@smui/checkbox';

    const dispatch = createEventDispatcher();
    export let selectedScan;
    let ordinatoriList = [];
    let valueSx, valueDx, enabledSx = false, enabledDx = false;

    onMount(async () => {
        /* get ordinatori */
        ordinatoriList = await window.electron.database('db-get-ordinatori');

        /* Await to ensure components are created and can receive events */
        await delay(500);

        /* initialize */
        initValues();
    })

    const initValues = () => {
        const ordinatoreSx = ordinatoriList.find((ord) => ord.metis_code == selectedScan.codice_ordinatore_sx);
        const ordinatoreDx = ordinatoriList.find((ord) => ord.metis_code == selectedScan.codice_ordinatore_dx);

        enabledSx = true;
        valueSx = structuredClone(ordinatoreSx);

        if(ordinatoreDx){
            enabledDx = true;
            valueDx = structuredClone(ordinatoreDx);
        } else {
            enabledDx = false;
            valueDx = null;
        }

        _isDoublePageEnabledOT.set(enabledDx);
    }

    const handleChangeSx = () => {        
        /* Update nomenclatura */
        $_nomenclaturaSxOT.ordinatore = structuredClone(valueSx);

        /* Show name_short in textfield */
        const ordinatoreDiv = d3.select('#ordinatore-ot');    
        const selectElement = ordinatoreDiv.selectAll('.mdc-select__selected-text').nodes()[0];
        d3.select(selectElement).html(valueSx?.name_short || null);

        /* Inform parent that value has changed */
        dispatch('changeSx');

        /* Inform other components that value has changed */
        d3.select('#interni-pagina-ot').dispatch('ordinatore-sx-changed', {detail: {value: valueSx}});
        d3.select('#numero-pagina-ot').dispatch('ordinatore-sx-changed', {detail: {value: valueSx}});
        d3.select('#tipo-pagina-ot').dispatch('ordinatore-sx-changed', {detail: {value: valueSx}});
        d3.select('#eccezione-ot').dispatch('ordinatore-sx-changed', {detail: {value: valueSx}});
    }

    const handleChangeDx = () => {
        /* Update nomenclatura */
        $_nomenclaturaDxOT.ordinatore = structuredClone(valueDx);

        /* Show name_short in textfield */
        const ordinatoreDiv = d3.select('#ordinatore-ot');
        const selectElement = ordinatoreDiv.selectAll('.mdc-select__selected-text').nodes()[1];
        d3.select(selectElement).html(valueDx?.name_short || null);
        
        /* Inform parent that value has changed */
        dispatch('changeDx');

        /* Inform other components that value has changed */
        d3.select('#interni-pagina-ot').dispatch('ordinatore-dx-changed', {detail: {value: valueDx}});
        d3.select('#numero-pagina-ot').dispatch('ordinatore-dx-changed', {detail: {value: valueDx}});
        d3.select('#tipo-pagina-ot').dispatch('ordinatore-dx-changed', {detail: {value: valueDx}});
        d3.select('#eccezione-ot').dispatch('ordinatore-dx-changed', {detail: {value: valueDx}});
    }

    const handleEnableDx = () => {
        valueDx = enabledDx ? structuredClone(valueSx) : null;
        /* handleChangeDx will fire automatically now */

        /* Set store variable and dispatch event to inform main viewer */
        _isDoublePageEnabledOT.set(enabledDx);
    }
</script>

<div id="ordinatore-ot" class="flex-column">
    <!-- LABEL -->
    <div class="field-label">Ordinatore</div>

    <!-- TOOLS SX -->
    <div class="field-tools">
        <Checkbox bind:checked={enabledSx} disabled />
    </div>

    <!-- SX -->
    <Select
    variant="outlined"
    noLabel
    bind:value={valueSx}
    key={(ordinatore) => `${ordinatore ? ordinatore.metis_code : ''}`}
    on:SMUISelect:change={handleChangeSx}
    >
        {#each ordinatoriList as ordinatore}
            <Option value={ordinatore}>{ordinatore.name_long}</Option>
        {/each}
    </Select>

    <!-- TOOLS DX -->
    <div class="field-tools">
        <Checkbox bind:checked={enabledDx} on:change={handleEnableDx} disabled={!valueSx?.book_mode_enabled}/>
    </div>

    <!-- DX -->
    <Select
    variant="outlined"
    noLabel
    disabled={!enabledDx}
    bind:value={valueDx}
    key={(ordinatore) => `${ordinatore ? ordinatore.metis_code : ''}`}
    on:SMUISelect:change={handleChangeDx}
    >
        {#each ordinatoriList as ordinatore}
            <Option value={ordinatore}>{ordinatore.name_long}</Option>
        {/each}
    </Select>
</div>

<style>
    .flex-column {
        display: flex;
        flex-direction: column;
    }

    .field-label {
        border: 1px solid var(--mdc-theme-secondary);
        background-color: var(--mdc-theme-background);
        color: var(--mdc-theme-secondary);
        display: flex;
        align-items: center;
        justify-content: center;
        height: 30px;
        padding: 0 5px;
    }

    .field-tools {
        border: 1px solid var(--mdc-theme-secondary);
        background-color: var(--mdc-theme-background);
        display: flex;
        align-items: center;
        justify-content: center;
        height: 30px;
    }

    /* Select */
    * :global(.mdc-select__menu.mdc-menu.mdc-menu-surface.mdc-menu-surface--open.mdc-menu-surface--fullwidth){
        width: max-content;
    }

    * :global(.mdc-select__anchor){
        min-width: 100px;
        width: fit-content;
        padding: 0px 16px;
        height: 40px;
    }

    * :global(.mdc-select:not(.mdc-select--disabled) .mdc-select__selected-text){
        color: var(--mdc-theme-primary);
        font-weight: bold;
    }

    * :global(.mdc-select--disabled) {
        cursor: default;
        pointer-events: none;
        background-image: url('/diagonal-stripes.svg');
        border: 1px solid var(--mdc-theme-secondary);
        height: 38px;
    }

    * :global(.mdc-select__dropdown-icon){
        width: 0;
        margin: 0;
    }

    * :global(.mdc-notched-outline__leading) {
        border-radius: 0px !important;
    }

    * :global(.mdc-notched-outline__trailing) {
        border-radius: 0px !important;
    }

    /* Select scrollbar */
    * :global(.mdc-select__menu::-webkit-scrollbar) {
        width: 10px;
    }

    * :global(.mdc-select__menu::-webkit-scrollbar-track) {
        background-color: white;
        border-radius: 10px;
    }

    * :global(.mdc-select__menu::-webkit-scrollbar-thumb) {
        background-color: var(--mdc-theme-primary);
        border-radius: 10px;
    }

    /* Checkbox */
    * :global(.mdc-checkbox) {
        transform: scale(0.8);
    }

    * :global(.mdc-checkbox--disabled) {
        pointer-events: auto;
        cursor: not-allowed;
    }
    
    * :global(.mdc-checkbox--disabled .mdc-checkbox__background) {
        border-color: var(--mdc-theme-disabled) !important;
    }

    * :global(.mdc-checkbox--selected.mdc-checkbox--disabled .mdc-checkbox__background) {
        background-color: var(--mdc-theme-primary-hover) !important;
    }

    * :global(.mdc-checkbox__ripple) {
        top: 3px;
        left: 4px;
    }
</style>