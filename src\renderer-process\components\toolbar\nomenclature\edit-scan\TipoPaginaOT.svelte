<script>
    import { _nomenclaturaSxOT, _nomenclaturaDxOT } from '@store';
    import { createEventDispatcher } from 'svelte';
    import * as d3 from 'd3-selection';
    import { onMount } from "svelte";
    
    //smui
    import Select, { Option } from '@smui/select';
    import Checkbox from '@smui/checkbox';
    
    const dispatch = createEventDispatcher();
    export let selectedScan;
    let valueSx, valueDx, enabledSx = false, enabledDx = false, ordinatoreSx, ordinatoreDx;

    onMount(() => {
        /* Create event listeners */
        createEventListeners();
    })

    const createEventListeners = () => {
        d3.select('#tipo-pagina-ot').on('ordinatore-sx-changed', (e) => {
            ordinatoreSx = e.detail.value;

            enabledSx = ordinatoreSx ? ordinatoreSx.page_type_enabled : false;
            valueSx = enabledSx ? selectedScan.tipo_pagina_sx || 'verso' : null;
            /* handleChangeSx will fire automatically now */
        });

        d3.select('#tipo-pagina-ot').on('ordinatore-dx-changed', (e) => {
            ordinatoreDx = e.detail.value;

            enabledDx = ordinatoreDx ? ordinatoreDx.page_type_enabled : false;
            valueDx = enabledDx ? selectedScan.tipo_pagina_dx || 'recto' : null;
            /* handleChangeDx will fire automatically now */
        });
    }

    const handleChangeSx = () => {
        /* Update nomenclatura */
        $_nomenclaturaSxOT.tipoPagina = valueSx;

        /* Inform parent that value has changed */
        dispatch('changeSx');
    }

    const handleChangeDx = () => {
        /* Update nomenclatura */
        $_nomenclaturaDxOT.tipoPagina = valueDx;

        /* Inform parent that value has changed */
        dispatch('changeDx');
    }
</script>

<div id="tipo-pagina-ot" class="flex-column">
    <!-- LABEL -->
    <div class="field-label">Tipo</div>

    <!-- TOOLS SX -->
    <div class="field-tools">
        <Checkbox bind:checked={enabledSx} disabled/>
    </div>

    <!-- SX -->
    <Select
    variant="outlined"
    noLabel
    disabled={!enabledSx}
    bind:value={valueSx}
    on:SMUISelect:change={handleChangeSx}
    >
        <Option value="verso">verso</Option>
        <Option value="recto">recto</Option>
    </Select>

    <!-- TOOLS DX -->
    <div class="field-tools">
        <Checkbox bind:checked={enabledDx} disabled/>
    </div>

    <!-- DX -->
    <Select
    variant="outlined"
    noLabel
    disabled={!enabledDx}
    bind:value={valueDx}
    on:SMUISelect:change={handleChangeDx}
    >
        <Option value="verso">verso</Option>
        <Option value="recto">recto</Option>
    </Select>
</div>

<style>
    .flex-column {
        display: flex;
        flex-direction: column;
    }

    .field-label {
        border: 1px solid var(--mdc-theme-secondary);
        background-color: var(--mdc-theme-background);
        color: var(--mdc-theme-secondary);
        display: flex;
        align-items: center;
        justify-content: center;
        height: 30px;
        padding: 0 5px;
    }

    .field-tools {
        border: 1px solid var(--mdc-theme-secondary);
        background-color: var(--mdc-theme-background);
        display: flex;
        align-items: center;
        justify-content: center;
        height: 30px;
    }

    /* Select */
    * :global(.mdc-select__menu.mdc-menu.mdc-menu-surface.mdc-menu-surface--open.mdc-menu-surface--fullwidth){
        width: fit-content;
    }

    * :global(.mdc-select__anchor){
        min-width: 100px;
        width: fit-content;
        padding: 0px 16px;
        height: 40px;
        width: 100px;
    }

    * :global(.mdc-select:not(.mdc-select--disabled) .mdc-select__selected-text){
        color: var(--mdc-theme-primary);
        font-weight: bold;
    }

    * :global(.mdc-select--disabled) {
        cursor: default;
        pointer-events: none;
        background-image: url('/diagonal-stripes.svg');
        border: 1px solid var(--mdc-theme-secondary);
        height: 38px;
    }

    * :global(.mdc-select__dropdown-icon){
        width: 0;
        margin: 0;
    }

    * :global(.mdc-notched-outline__leading) {
        border-radius: 0px !important;
    }

    * :global(.mdc-notched-outline__trailing) {
        border-radius: 0px !important;
    }

    /* Select scrollbar */
    * :global(.mdc-select__menu::-webkit-scrollbar) {
        width: 10px;
    }

    * :global(.mdc-select__menu::-webkit-scrollbar-track) {
        background-color: white;
        border-radius: 10px;
    }

    * :global(.mdc-select__menu::-webkit-scrollbar-thumb) {
        background-color: var(--mdc-theme-primary);
        border-radius: 10px;
    }

    /* Checkbox */
    * :global(.mdc-checkbox) {
        transform: scale(0.8);
    }

    * :global(.mdc-checkbox--disabled) {
        pointer-events: auto;
        cursor: not-allowed;
    }

    * :global(.mdc-checkbox--disabled .mdc-checkbox__background) {
        border-color: var(--mdc-theme-disabled) !important;
    }

    * :global(.mdc-checkbox--selected.mdc-checkbox--disabled .mdc-checkbox__background) {
        background-color: var(--mdc-theme-primary-hover) !important;
    }

    * :global(.mdc-checkbox__ripple) {
        top: 3px;
        left: 4px;
    }
</style>