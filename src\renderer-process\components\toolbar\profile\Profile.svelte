<script>
    import { _user, _selected<PERSON>ob } from "@store";
    import IconButton from "@components/common/IconButton.svelte";

    //smui
    import Button, { Label } from '@smui/button';
    import Tooltip, { Wrapper } from '@smui/tooltip';

    //icons
    import UserIcon from "svelte-material-icons/AccountCircle.svelte";
    import LogoutIcon from "svelte-material-icons/LogoutVariant.svelte";
    import OfficeIcon from "svelte-material-icons/OfficeBuilding.svelte";
    import ProfileIcon from "svelte-material-icons/BadgeAccountHorizontal.svelte";
    import StampatoIcon from "svelte-material-icons/Bookshelf.svelte";
    import MssIcon from "svelte-material-icons/BookOpenVariant.svelte";

    export let flexDirection = "column";
    export let showBackButton = false;
    export let showLogoutButton = false;

    const classMap = {
        row: "flex-row-container",
        column: "flex-column-container"
    }

    const logout = async () => {
        await window.electron.application('restart');
    }

    const backToJobManager = async () => {
        await window.electron.application('restart', {user: $_user, idDigit: $_selectedJob.id_digit});
    }
</script>

<div class={classMap[flexDirection]}>
    <div class="flex-row">
        <UserIcon width=30 height=30 color="var(--mdc-theme-secondary)" />
        <div class="flex-column">
            <span class="label">{$_user?.displayName}</span>
            {#if showLogoutButton}
                <IconButton icon={LogoutIcon} iconWidth=20 iconHeight=30  iconColor="var(--mdc-theme-primary)" tooltip="Logout" onclick={logout}/>
            {/if}
        </div>
    </div>
    <div class="flex-row">
        <ProfileIcon width=30 height=30 color="var(--mdc-theme-secondary)" />
        <span class="label">{$_user?.profile}</span>
    </div>
    <div class="flex-row">
        <OfficeIcon width=30 height=30 color="var(--mdc-theme-secondary)" />
        <span class="label">{$_user?.company}</span>
    </div>
    {#if $_selectedJob}
        <div class="flex-row">
            {#if $_selectedJob.is_stampato}
                <StampatoIcon width=30 height=30 color="var(--mdc-theme-secondary)" />
            {:else}
                <MssIcon width=30 height=30 color="var(--mdc-theme-secondary)" />
            {/if}
            
            <Wrapper>
                <span class="label">{$_selectedJob.identifier}</span>
                <Tooltip showDelay=0 hideDelay=0>id_digit: {$_selectedJob.id_digit}</Tooltip>
            </Wrapper>
        </div>
    {/if}
    {#if showBackButton}
        <div class="flex-row">
            <Button variant="raised" on:click={backToJobManager}>
                <Label>Job Manager</Label>
            </Button>
        </div>
    {/if}
</div>

<style>
    .flex-column-container {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
        gap: 10px;
    }

    .flex-row-container {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 20px;
    }

    .flex-row {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 10px;
    }

    .flex-column {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .label {
        font-weight: bold;
        color: var(--mdc-theme-secondary);
        text-wrap: nowrap;
    }

    * :global(.mdc-button__label) {
        white-space: nowrap;
    }
</style>