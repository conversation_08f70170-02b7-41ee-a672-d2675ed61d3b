<script>
  import { _selectedJob, _selectedScan, _nextSequenziale, _acquisitionMode } from '@store';
  import ScanVisualizerTableTools from '@components/toolbar/scan-visualizer/ScanVisualizerTableTools.svelte';
  import ScanNotesDialog from '@components/note/ScanNotesDialog.svelte'
  import IconButton from "@components/common/IconButton.svelte";
  import { removeAllSelectedClass, scrollToElement, scrollUpOrDown } from '@utility';
  import Mousetrap from '@ottozz/mousetrap';
  import * as d3 from 'd3-selection';
  import { onMount } from 'svelte';

  //smui components
  import DataTable, { Head, Body, Row, Cell } from '@smui/data-table';

  //icons
  import InformationIcon from "svelte-material-icons/Information.svelte";
  import AlertIcon from "svelte-material-icons/Alert.svelte";

  let options = [];

  $: scanNumber = () => {
    let num = 0;

    options.forEach(scan => {
      num += scan.pagina_doppia ? 2 : 1;
    });

    return num;
  }

  onMount(async () => {
    /* Get scan list */
    await getScansioni();

    /* Define keyboard shortcuts */
    Mousetrap.bind('down', (e) => handleArrowSelection(e, 'down'));
    Mousetrap.bind('up', (e) => handleArrowSelection(e, 'up'));
  
    /* Create event listeners */
    createEventListeners();

    selectLastScan();
  });

  const getScansioni = async () => {
    options = await window.electron.database('db-get-scansioni', { idDigit: $_selectedJob.id_digit });

    /* Set sequenziale to use for the next scan equal to last sequenziale + 1 */
    if($_acquisitionMode == 'normal') {
      const maxSequenziale = options.length > 0 ? Math.max(...options.map(scan => scan.sequenziale_scansione)) : 0;
      _nextSequenziale.set(maxSequenziale + 1);
    }
    else if($_acquisitionMode == 'leggio-recto') {
      const maxSequenziale = options.length > 0 ? Math.max(...options.map(scan => scan.sequenziale_scansione)) : 0;
      _nextSequenziale.set(maxSequenziale + 2);
    }
    else if($_acquisitionMode == 'leggio-verso') {
      // For leggio-verso, find the first available odd number (in between recto scans)
      // Sort scans by sequenziale_scansione
      const sortedScans = [...options].sort((a, b) => a.sequenziale_scansione - b.sequenziale_scansione);

      // Find the first gap between consecutive scans that can fit a verso scan
      let foundGap = false;
      for (let i = 0; i < sortedScans.length - 1; i++) {
        const currentSeq = sortedScans[i].sequenziale_scansione;
        const nextSeq = sortedScans[i + 1].sequenziale_scansione;

        // If there's a gap of 2 or more between consecutive scans, we can insert a verso scan
        if (nextSeq - currentSeq >= 2) {
          _nextSequenziale.set(currentSeq + 1);
          foundGap = true;
          break;
        }
      }

      // If no gap was found, set the next sequenziale to the highest + 1
      if (!foundGap) {
        const maxSequenziale = sortedScans.length > 0 ? Math.max(...sortedScans.map(scan => scan.sequenziale_scansione)) : 0;
        _nextSequenziale.set(maxSequenziale + 1);
      }
    }
  };

  const selectLastScan = () => {
    if(options){
      _selectedScan.set(options[options.length - 1]);

      /* Set selected class and scroll table */
      setSelectedClassAndScrollToElement();
      
      /* Update scan images */
      d3.select('#scan-viewers').dispatch('update-scan-images', {detail: { scan: $_selectedScan}});
    }
  }

  const selectScan = (scanId) => {
    /* Select provided scan */
    if(options){
        const scanToSelect = options.find((scan) => scan.id == scanId);
        _selectedScan.set(scanToSelect);

        /* Set selected class and scroll table */
        setSelectedClassAndScrollToElement();
        
        /* Update scan images */
        d3.select('#scan-viewers').dispatch('update-scan-images', {detail: { scan: $_selectedScan}});
    }
  }

  const createEventListeners = () => {
    d3.select('#scan-visualizer-table').on('refresh-scan-table', async (e) => {
      const scanId = e.detail?.scanId;

      await getScansioni();

      if(scanId)
          selectScan(scanId);
      else
          selectLastScan();

      /* Refresh scansioni list also for nomenclature */
      d3.select('#nomenclature').dispatch('update-scansioni-list');
    });
  }

  const setSelectedClassAndScrollToElement = () => {
    removeAllSelectedClass();

    const selectedIndex = options.findIndex(el => el.id === $_selectedScan?.id);
    
    if(selectedIndex != -1){
      d3.select(`#row-${options[selectedIndex]?.id}`).classed('selected', true);
      scrollToElement(d3.select(".mdc-data-table").node(), d3.select(".selected").node());
    }
  }

  const setSelectedClassAndScrollUpOrDown = () => {
    removeAllSelectedClass();

    const selectedIndex = options.findIndex(el => el.id === $_selectedScan?.id);
    
    if(selectedIndex != -1){
      d3.select(`#row-${options[selectedIndex]?.id}`).classed('selected', true);
      scrollUpOrDown(d3.select(".mdc-data-table").node(), d3.select(".selected").node());
    }
  }

  const setSelectedClassWithoutScroll = () => {
    removeAllSelectedClass();

    const selectedIndex = options.findIndex(el => el.id === $_selectedScan?.id);
    
    if(selectedIndex != -1){
      d3.select(`#row-${options[selectedIndex]?.id}`).classed('selected', true);
    }
  }

  let openScanNotesDialog = false;
  const handleNotes = () => {
      openScanNotesDialog = true;
  }

  const handleSelection = (option) => {
    /* Update store variable */
    _selectedScan.set(option);

    /* Set selected class without scrolling the table */
    setSelectedClassWithoutScroll();
    
    /* Update scan images */
    d3.select('#scan-viewers').dispatch('update-scan-images', {detail: { scan: $_selectedScan}});
  }

  const handleArrowSelection = (e, direction) => {
    e.preventDefault();

    const selectedIndex = options.findIndex(el => el.id === $_selectedScan.id);
    const newIndex = direction == 'up' ? selectedIndex - 1 : selectedIndex + 1;

    /* If already selected row is first or last, return */
    if (!options[newIndex]) return;

    /* Update store variable */
    _selectedScan.set(options[newIndex]);
    
    /* Set selected class and scroll the table */
    setSelectedClassAndScrollUpOrDown();

    /* Update scan images */
    d3.select('#scan-viewers').dispatch('update-scan-images', {detail: { scan: $_selectedScan}});
  }
</script>

<ScanNotesDialog bind:openScanNotesDialog={openScanNotesDialog} bind:scanObj={$_selectedScan}/>

<div id="scan-visualizer-table" class="flex-column-container">
  <!-- Table tools -->
  <ScanVisualizerTableTools />

  <!-- Table -->
  <div class="table-container">
    <DataTable stickyHeader>
      <Head>
        <Row>
          <Cell numeric><span class='unselectable'>Tot: {scanNumber()}</span></Cell>
          <Cell><span class='unselectable'>Pagina di sinistra</span></Cell>
          <Cell><span class='unselectable'>Pagina di destra</span></Cell>
          <Cell numeric><span class='unselectable'>Note</span></Cell>
        </Row>
      </Head>
        <Body>
          {#if options.length > 0}
            {#each options as option (option.id)}
              <Row id={`row-${option?.id}`} on:SMUIDataTableRow:click={() => handleSelection(option)}>
                <Cell numeric>
                  {#if option.alert && !$_selectedJob.is_stampato}
                    <AlertIcon width=20 height=20 viewBox="0 -3 24 24" color="var(--mdc-theme-warning)"/>
                  {/if}
                  <span class='unselectable'>{option.sequenziale_scansione}</span>
                </Cell>
                <Cell><span class='unselectable'>{option.display_name_sx}</span></Cell>
                <Cell><span class='unselectable'>{option.display_name_dx}</span></Cell>
                <Cell>
                  {#if option.testo_nota}
                    <IconButton icon={InformationIcon} iconWidth=25 iconHeight=25 iconColor="var(--mdc-theme-secondary)" tooltip="Mostra note" onclick={handleNotes} disabled={$_selectedScan != option}/>
                  {/if}
                </Cell>
              </Row>
            {/each}
          {/if}
        </Body>
    </DataTable>
  </div>
</div>


<style>
.flex-column-container {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 2px;
  }

  .table-container {
    height: 220px;
  }

  .unselectable {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  * :global(.mdc-data-table) {
    height: 100%;
    min-width: 600px;
    overflow-y: auto;
  }
  
  * :global(.mdc-data-table::-webkit-scrollbar) {
    width: 8px;
  }

  * :global(.mdc-data-table::-webkit-scrollbar-track) {
    background-color: white;
    border-radius: 10px;
  }

  * :global(.mdc-data-table::-webkit-scrollbar-thumb) {
    background-color: var(--mdc-theme-primary);
    border-radius: 10px;
  }

  * :global(.mdc-data-table__header-row) {  
    height: min-content;
  }

  * :global(.mdc-data-table__row) {
    height: 2rem;
    cursor: pointer;
  }

  * :global(.mdc-data-table__row.selected) {
    background-color: var(--mdc-theme-primary-selected);
  }

  * :global(.mdc-data-table__cell) {
    padding: 0 10px 0 5px;
  }

  * :global(.mdc-data-table__header-cell) {
    padding: 0 10px 0 5px;
  }

  * :global(.mdc-radio) {
    width: 10px;
  } 

  * :global(.mdc-icon-button) {
        padding: 0px 0px 0 0px;
  }
</style>