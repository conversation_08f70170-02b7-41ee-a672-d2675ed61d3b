<script>
	import routes from '../routes';
	import Router from 'svelte-spa-router';
	import { replace } from 'svelte-spa-router';
	import { SvelteToast } from '@zerodevx/svelte-toast';

	const conditionsFailed = (event) => {
		replace(event.detail.userData.fallbackUrl);
	}
</script>


<main>
	<SvelteToast />
	<Router {routes} on:conditionsFailed={conditionsFailed} />
</main>

<style>
	main {
		height: 100%;
		font-family: Roboto, sans-serif;
		--toastWidth: 600px;
		--toastContainerLeft: 35%;
		--toastContainerBottom: 25%;
		--toastContainerTop: auto;
	}
</style>