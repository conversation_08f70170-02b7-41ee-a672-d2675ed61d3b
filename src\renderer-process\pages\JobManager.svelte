<script>
    import { _selectedJob, _isFinalizing, _scannerId, _host, _user, _showJobsInRecovery, _isRecovering, _isLeggio } from "@store";
    import CircularProgressIndicator from "@components/common/CircularProgressIndicator.svelte";
    import JobsTable from "@components/job-manager/JobsTable.svelte";
    import JobsTableTools from "@components/job-manager/JobsTableTools.svelte";
    import Profile from "@components/toolbar/profile/Profile.svelte";
    import { removeAllSelectedClass } from "@utility";
    import * as d3 from 'd3-selection';
    import { onMount } from "svelte";

    let options = [];

    onMount(async () => {
        if(!$_host || !$_scannerId){
            const hostInfo = await window.electron.database('db-get-host-info');
            _host.set(hostInfo?.host.toUpperCase());
            _scannerId.set(hostInfo?.scanner_id.toUpperCase());
            _isLeggio.set(hostInfo?.is_leggio);
        }

        /* Fetch jobs */
        await fetchJobs(false);

        /* Automatically select last job if present in commandLine switch */
        await initSelectedJob();

        /* Create event listeners */
        createEventListeners();
    });

    const fetchJobs = async () => {
        const jobList = await window.electron.database("db-get-jobs", { userCompany: $_user.company, getInRecovery: $_showJobsInRecovery });

        if(!jobList) return options = [];

        for (const job of jobList) {
            /* NOTE - added for develop branch */
            job.scan_path = `P:\\PRISMA\\${job.identifier}`;
            job.preview_path = `P:\\PRISMA\\${job.identifier}\\previews`;
            job.finalized_path = `P:\\PRISMA\\FINALIZZATI\\${job.identifier}`;
            job.recovery_path = `P:\\PRISMA\\RECOVERY\\${job.identifier}`;
        }
            
        options = jobList;
    }

    const initSelectedJob = async () => {
        const lastJobIdDigit = await window.electron.application('get-last-job');

        if(lastJobIdDigit){
            /* Get last job object by its id_digit */
            const lastJob = options.find((job) => job.id_digit == lastJobIdDigit);

            if(lastJob) {
                /* Select last Job*/
                hanldeJobSelection({detail: lastJob});

                /* Set selected class to lastJob */
                const lastJobIndex = options.findIndex(el => el.id_digit === lastJob.id_digit);
                if(lastJobIndex != -1)
                    d3.select(`#row-${options[lastJobIndex]?.id_digit}`).classed('selected', true);
                
            }
        }
    }

    const createEventListeners = () => {
        d3.select('#job-manager').on('refresh-jobs-table', async (e) => {
            await fetchJobs();

            if(e?.detail?.idDigit){
                const jobToSelect = options.find(job => job.id_digit == e.detail.idDigit) || null;
                _selectedJob.set(jobToSelect);
                return;
            }
            
            _selectedJob.set(null);
            removeAllSelectedClass();
        });
    }

    const hanldeJobSelection = async (e) => {
        const job = e.detail;

        /* Set store variable */
        _selectedJob.set(job);

        /* set selected job */
        await window.electron.variables('config-set-selected-job', {selectedJob: job});

        /* Set log params with selected id_digit */
        await setLogParams(job);
    }

    const setLogParams = async (job) => {
        const logParams = {
            idDigit: job.id_digit,
            user: $_user.displayName || $_user.username,
            userProfile: $_user.profile,
            userCompany: $_user.company,
            scannerId: $_scannerId
        }

        /* Set Log params */
        await window.electron.variables('config-set-log-params', logParams);
    }
</script>

<div id="job-manager" class="container">
    {#if $_isFinalizing}
        <CircularProgressIndicator loadingText='Finalizzazione in corso...'/>
    {:else if $_isRecovering}
        <CircularProgressIndicator loadingText='Reset in corso...'/>
    {:else}
        <div class="flex-container">
            <Profile flexDirection="row" showLogoutButton/>
            <JobsTable {options} on:jobSelected={(e) => hanldeJobSelection(e)}/>
            <JobsTableTools />
        </div>
    {/if}
</div>

<style>
    .container {
        height: 100%;
    }

    .flex-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 10px;

        width: 60%;
        margin: auto;
        margin-top: 5%; 
    }
</style>