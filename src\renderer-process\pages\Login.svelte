<script>
    import { onMount } from 'svelte';
    import { replace } from 'svelte-spa-router';
    import { _user, _host, _scannerId, _isLeggio } from '@store';

    //smui components
    import Paper, { Title, Content } from '@smui/paper';
    import Button from '@smui/button';
    import Textfield from '@smui/textfield';

    //icons
    import AccountIcon from 'svelte-material-icons/Account.svelte';
    import PasswordIcon from 'svelte-material-icons/FormTextboxPassword.svelte';

    let usernameInputText;
    let wrongCredentials = false;
    let appVersion = '';

    let formData = {
        "username": null,
        "password": null
    }

    onMount(async () => {
        usernameInputText.focus();
        appVersion = await window.electron.variables('get-app-variable', {variableName: 'appVersion'});
        
        const hostInfo = await window.electron.database('db-get-host-info');
        _host.set(hostInfo?.host.toUpperCase());
        _scannerId.set(hostInfo?.scanner_id.toUpperCase());
        _isLeggio.set(hostInfo?.is_leggio);

        const logParams = {
            idDigit: null,
            user: null,
            userProfile: null,
            userCompany: null,
            scannerId: $_scannerId
        }
        /* Set Log params */
        await window.electron.variables('config-set-log-params', logParams);
    });

    const login = async (event) => {
        event.preventDefault();
        try {
            /* NOTE - modified for develop branch */
            //let response = await window.electron.auth("login", formData);
            let response = await window.electron.auth("login", {username: "test.foto", password: "test.foto"});
            

            if(response.result == "OK"){
                _user.set(response.data);

                response.data.memberOf.forEach(group => {
                    if(group.includes("Digitalizzazione")){
                        /* Operator and Tutor */
                        group.includes("lv_0") ? $_user.profile = 'Operatore' : null;
                        group.includes("lv_1") ? $_user.profile = 'Tutor' : null; 

                        group.includes("biblionova") ? $_user.company = "Biblionova" : null;
                        group.includes("seret") ? $_user.company = "Seret" : null;

                        /* Supertutor */
                        if(group.includes("lv_2")) {
                            $_user.profile = 'Supertutor';
                            $_user.company = 'BAV';
                        }
                    }
                });

                /* NOTE - added for develop branch */
                $_user.profile = 'Supertutor';
                $_user.company = 'BAV';

                await setLogParams();
                await window.electron.database('db-log', {level:'INFO', text:'[Login.svelte][login] Login success for ' + response.data.username});
                replace('/');
            } else {
                await window.electron.database('db-log', {level:'INFO', text:'[Login.svelte][login] Login failed - wrong credentials'});
                wrongCredentials = true;
            }
		    
        }
        catch (err) {
            await window.electron.database('db-log', {level:'ERROR', text:'[Login.svelte][login] Login error: ' + err.stack});
            wrongCredentials = true;
        }
    }

    const setLogParams = async () => {
        const logParams = {
            idDigit: null,
            user: $_user.displayName || $_user.username,
            userProfile: $_user.profile,
            userCompany: $_user.company,
            scannerId: $_scannerId
        }
        
        await window.electron.variables('config-set-log-params', logParams);
    }
</script>

<div class="container">
    <form>
        <Paper elevation=3>
            <div class="flex-column-container">
                <img src="prisma_logo.png" alt="PRISMA logo" width="200"/>
                <Content>
                    <div class="flex-inner-container">
                        <Textfield variant="outlined" bind:this={usernameInputText} bind:value={formData.username} label="Username" >
                            <div class="icon" slot="trailingIcon">
                                <AccountIcon  width=24 height=24 color="var(--mdc-theme-secondary)" />
                            </div>
                        </Textfield>
                        <Textfield variant="outlined" type="password" bind:value={formData.password} label="Password" >
                            <div class="icon" slot="trailingIcon">
                                <PasswordIcon  width=24 height=24 color="var(--mdc-theme-secondary)" />
                            </div>
                        </Textfield>
                        {#if wrongCredentials}
                            <span class="error">Credenziali errate</span>
                        {/if}
                        <Button color="primary" variant="raised" on:click={login}>Login</Button> 
                    </div>
                </Content>
            </div>
        </Paper>
    </form>
    <span class="bottom-left">Versione: {appVersion}</span>
    <img src="bav_logo.png" alt="BAV logo" class="bottom-right" >
</div>

<style>

    .container {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        padding-top: 5%;
    }

    span {
        color: var(--mdc-theme-secondary)
    }

    .bottom-left {
        position: absolute;
        left: 0px;
        bottom: 0px;
        margin-left: 10px;
        margin-bottom: 10px;
    }

    .bottom-right {
        position: absolute;
        right: 0px;
        bottom: 0px;
        margin-right: 10px;
        margin-bottom: 10px;
    }

    form {
        display: flex;
        flex-flow: column;
        justify-content: center;
        align-items: center;
    }
    
    .flex-column-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 30px;
        padding: 20px 50px 20px 50px;
    }

    .flex-inner-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 20px;
    }

    .error{
        color: var(--mdc-theme-error);
    }

    .icon {
        padding-top: 15px;
        padding-right: 10px;
    }
   
</style>