import { wrap } from 'svelte-spa-router/wrap';
import { isUserLogged } from './utilities/utility';
import Acquisizione from './pages/Acquisizione.svelte';
import JobManager from './pages/JobManager.svelte';
import Login from './pages/Login.svelte';
import NotFound from './pages/NotFound.svelte';
import PreviewVisualizer from './pages/PreviewVisualizer.svelte';

export default {
    '/': wrap({
        component: JobManager,
        userData: {
            fallbackUrl: '/login'
        },
        conditions: [
            (detail) => {
                return isUserLogged();
            },
        ]
    }),
    '/acquisizione': wrap({
        component: Acquisizione,
        userData: {
            fallbackUrl: '/login'
        },
        conditions: [
            (detail) => {
                return isUserLogged();
            },
        ]
    }),
    '/login': Login,
    '/previewVisualizer': PreviewVisualizer,

    // The catch-all route must always be last
    '*': NotFound
};