import { _user } from '../store';
import { get } from 'svelte/store';
import { toast } from '@zerodevx/svelte-toast';
import * as d3 from 'd3-selection';

export const isUserLogged = async () => {
    const userString = await window.electron.application('get-logged-user');
    
    if(userString){
        _user.set(JSON.parse(userString));
    } 

    return get(_user) ? true : false;
}

export const toBase64 = (arr) => {
    return btoa(
       arr.reduce((data, byte) => data + String.fromCharCode(byte), '')
    )
};

export const delay = ms => new Promise(res => setTimeout(res, ms));

export const waitForScanner = async (intervalMs, timeoutMs) => {
    return new Promise(async (resolve, reject) => {
        const timeWas = new Date();
        let response;

        response = await window.electron.metis("msd-get-status");
        if (response.data == 'Ready') return resolve();

        const wait = setInterval(async () => {
            console.log("Checking scanner status...");
            response = await window.electron.metis("msd-get-status");

            if (response.data == 'Ready') {
                console.log("Scanner ready after ", new Date() - timeWas, "ms");
                clearInterval(wait);
                return resolve();
            } else if (new Date() - timeWas > timeoutMs) { // Timeout
                console.log("Scanner still busy after", new Date() - timeWas, "ms");
                clearInterval(wait);
                return reject();
            } else {
                console.log("Scanner still busy, status: ", response.data);
            }
        }, intervalMs);
    });
}

export const insertAtIndex = (str, substring, index) => {
    return str.slice(0, index) + substring + str.slice(index);
}

export const scrollToElement = (table, element) => {
    if(!table || !element) return;
    if(element.rowIndex == 1) return;
    
    table.scrollTop = element.offsetTop;
}

export const scrollUpOrDown = (table, element) => {
    if(!table || !element) return;
    
    const currentScroll = table.scrollTop;
    const tableHeight = table.offsetHeight;

    const elemUpperBound = element.offsetTop - 23;
    const elemLowerBound = element.offsetTop + element.offsetHeight;

    const shouldScrollDown = elemLowerBound - currentScroll > tableHeight;
    const shouldScrollUp = elemUpperBound < currentScroll;

    if(shouldScrollDown)
        table.scrollTop += element.offsetHeight;
    
    if(shouldScrollUp)
        table.scrollTop -= element.offsetHeight;
}

const toastThemes = {
    success: {
        '--toastBackground': 'var(--mdc-theme-success)',
        '--toastColor': 'white',
        '--toastBarBackground': 'green'
    },
    error: {
        '--toastBackground': 'var(--mdc-theme-error)',
        '--toastColor': 'white',
        '--toastBarBackground': 'red'
    },
    warning: {
        '--toastBackground': 'var(--mdc-theme-warning)',
        '--toastColor': 'white',
        '--toastBarBackground': 'orange'
    }
}
export const notify = {
    success: (text) => { toast.push(text, { theme: toastThemes.success }) },
    error: (text) => { toast.push(text, { theme: toastThemes.error }) },
    warning: (text) => { toast.push(text, { theme: toastThemes.warning }) },
}

export const reminder = {
    create: (text) => { return toast.push(text, { theme: toastThemes.warning, initial: 0, dismissable: false })},
    remove: (id) => { toast.pop(id) },
    removeAll: () => { toast.pop(0) }
}

export const removeAllSelectedClass = () => {
    /* Remove selected class to all elements */
    d3.selectAll(".selected").classed("selected", false);
}

export const isObjectEmpty = (obj) => {
    return Object.keys(obj).length === 0
}

export const romanize = (num) => {
    if (isNaN(num)) return NaN;
    
    var digits = String(+num).split(""),
        key = ["","C","CC","CCC","CD","D","DC","DCC","DCCC","CM",
               "","X","XX","XXX","XL","L","LX","LXX","LXXX","XC",
               "","I","II","III","IV","V","VI","VII","VIII","IX"],
        roman = "",
        i = 3;
    while (i--)
        roman = (key[+digits.pop() + (i * 10)] || "") + roman;
    return Array(+digits.join("") + 1).join("M") + roman;
}

export const alfabeti = [
    {
        name: "Latino (23, I)",
        letters: [
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H',
            'I', 'K', 'L', 'M', 'N', 'O', 'P', 'Q',
            'R', 'S' ,'T', 'V', 'X', 'Y', 'Z'
        ]
    },
    {
        name: "Latino (23, J)",
        letters: [
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H',
            'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q',
            'R', 'S' ,'T', 'V', 'X', 'Y', 'Z'
        ]
    },
    {
        name: "Inglese (26)",
        letters: [
            'A', 'B', 'C', 'D', 'E', 'F', 'G',
            'H', 'I', 'J', 'K', 'L', 'M', 'N',
            'O', 'P', 'Q', 'R', 'S' ,'T', 'U',
            'V', 'W', 'X', 'Y', 'Z'
        ]
    },
    {
        name: "Greco",
        letters: [
            'alpha', 'beta', 'gamma', 'delta', 'epsilon',
            'zeta', 'eta', 'theta', 'iota', 'kappa',
            'lambda', 'mi', 'ni', 'xi', 'omikron', 'pi',
            'rho', 'sigma', 'tau', 'hypsilon', 'phi',
            'chi', 'psi', 'omega'
        ]
    },
    {
        name: "Numerico",
        letters: [] // Numbers are handled dynamically in the dialog
    },
];

export const formatAlphabet = (alphabet, letterStart) => {
    if(alphabet.name == 'Greco' || alphabet.name == 'Numerico') return alphabet;
    let newAlphabet = {name: alphabet.name, letters: []};
    let charNumber, newLetter;

    for (const letter of alphabet.letters) {
        newLetter = '';
        charNumber = letterStart.length;

        for (let i = 0; i < charNumber; i++) {
            if(letterStart.charAt(i) == letterStart.charAt(i).toUpperCase())
                newLetter =  newLetter + letter.toUpperCase();
            else
                newLetter =  newLetter + letter.toLowerCase();
        }

        newAlphabet.letters.push(newLetter);
    }

    return newAlphabet;
}

export const increaseAlphabet = (alphabet) => {
    let newAlphabet = {name: alphabet.name, letters: []};
    let newLetter;

    for (const letter of alphabet.letters) {
        newLetter = letter + letter.charAt(letter.length - 1);
        newAlphabet.letters.push(newLetter);
    }

    return newAlphabet;
}

export const getAlphabetFromName = (alphabetName) => {
    const alphabet = alfabeti.find(el => el.name == alphabetName);
    return alphabet || [];
} 